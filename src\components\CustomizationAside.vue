<template>
  <div>
    <!--叶子级菜单-->
    <template v-if="item.children && item.children.length === 0">
      <el-menu-item :key="item.id" :index="item.path">
        {{ item.nameZh }}
      </el-menu-item>
    </template>
    <!--父级菜单-->
    <el-submenu v-else :index="item.path" style="text-align: left">
      <span slot="title" style="font-size: 17px">
        <i :class="item.iconCls"></i>
        {{ item.nameZh }}
      </span>
      <draggable
        v-model="arr1"
        :group="groupA"
        animation="300"
        dragClass="dragClass"
        ghostClass="ghostClass"
        chosenClass="chosenClass"
        @start="onStart"
        @end="onEnd"
      >
        <transition-group :style="style">
          <template v-for="child in item.children">
            <navigation-item
              v-if="child.children && child.children.length > 0"
              :key="child.id"
              :item="child"
            />

            <el-menu-item v-else :key="child.id" :index="child.path">
              <i :class="child.icon"></i>
              {{ child.nameZh }}
            </el-menu-item>
          </template>
        </transition-group>
      </draggable>
    </el-submenu>
  </div>
</template>
 
<script>
import draggable from "vuedraggable";
export default {
  components: {
    draggable,
  },
  name: "NavigationItem",
  props: {
    item: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      drag: false,
      groupA: {
        name: "site",
        pull: true, //可以拖从
        put: true, //可以拖出
      },
      groupB: {
        name: "site",
        pull: true,
        put: true,
      },
      draggable: true, //是否可拖拽
      resizable: true, //是否可更改大小
      options: [
        {
          value: "1",
          label: "合同报表",
        },
        {
          value: "2",
          label: "结算报表",
        },
      ], //左侧分类选项
      selectors: [
        {
          id: "1",
          title: "合同报表",
          child: [
            {
              title: "表格1/1",
              id: "1-1",
              move: true,
              type: "table",
            },
            {
              title: "柱状图1/2",
              id: "1-2",
              move: true,
              type: "bar",
            },
          ],
        },
        {
          id: "2",
          title: "结算报表",
          child: [
            {
              title: "折线图2/1",
              id: "2-1",
              move: true,
              type: "line",
            },
            {
              title: "饼图2/2",
              id: "2-2",
              move: true,
              type: "pie",
            },
          ],
        },
      ], //menu数据
      designData: { layoutCon: [], layoutData: [] }, //容器内容
      bjStyles: {}, //栅格样式
      curDesign: "", //点击容器组件样式
      rowheight: 106, //默认一格高度
      moveShow: false, //显示移动元素
      moveStyle: {}, //显示移动元素的位置
      mouseFalg: false, //按下的开关
      mouseLeft: 0, //鼠标距离x轴位置
      mouseTop: 0, //鼠标距离y轴位置
      designLeft: 0, //自定义容器距离x轴位置
      designTop: 0, //自定义容器距离y轴位置
      moveData: {}, //元素内容
      //定义要被拖拽对象的数组
      arr1: [
        { id: 1, name: "www.itxst.com" },
        { id: 2, name: "www.jd.com" },
        { id: 3, name: "www.baidu.com" },
        { id: 3, name: "www.taobao.com" },
      ],
    };
  },
};
</script>