import Vue from 'vue';
const vm = new Vue();
/**
* element-ui confirm 二次封装
* @param {Object} param
* @returns {Object} Promise 点击了确认resolve() 
*/
export function confirm(param) {
  vm.$confirm('你确定要执行此操作么吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    param();
  }).catch(() => {
    vm.$message({
      type: 'info',
      message: '已取消该操作！'
    });
  });
}