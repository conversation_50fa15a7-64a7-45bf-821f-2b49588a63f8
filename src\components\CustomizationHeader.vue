<template>
  <div id="header" class="header">
    <div class="logo_img_box_header2">
      <img
        src="../assets/slices/sys_logo.png"
        alt="登录logo"
        class="logo_img"
      />
    </div>
    <div class="title">
      <h3><span>|</span>自定义工作台</h3>
    </div>
    <div class="user_login">
      <UserInfo />
    </div>
  </div>
</template>

<script>
import UserInfo from "../components/UserInfo";
export default {
  components: { UserInfo },
};
</script>

<style lang="scss" scoped>
.header {
  width: 100%;
  height: 64px;
  background: #001529;
  // margin-bottom: 10px;
  box-shadow: 0px 0px 16px 0px rgba(0, 0, 0, 0.05),
    0px 0px 6px 0px rgba(0, 21, 41, 0.1);
  /deep/.el-dropdown {
    color: #fff;
  }
}
.logo_img_box_header2 {
  float: left;
  width: 120px;
  text-align: left;
  padding: 8px 0 0 30px;
  img {
    width: 100%;
    // background: #000;
  }
}
.title {
  float: left;
  width: 240px;
  height: 100%;
  span {
    font-size: 12px;
    color: #ccc;
    padding: 0 20px;
  }
  h3 {
    width: 240px;
    height: 100%;
    font-size: 16px;
    font-family: PingFangSC-Semibold, PingFang SC;
    font-weight: 600;
    color: #ffffff;
    line-height: 64px;
    text-align: left;
  }
}
</style>