import {message} from "./message";

require("@/utils/singleSign");
import Vue from 'vue';
import App from './App.vue';
import Vuex from 'vuex';
import ElementUI, {Notification} from 'element-ui';
import router from './router/router';
import axios from 'axios';
// 引入echarts
import * as echarts from 'echarts';
import 'echarts-liquidfill';

import Blob from './Excel/Blob';
import Export2Excel from './Excel/Export2Excel.js';
Vue.use(Blob, Export2Excel /* { default options with global component } */);
// swiper
import "../node_modules/swiper/dist/css/swiper.css";

import VueAwesomeSwiper from 'vue-awesome-swiper';
Vue.use(VueAwesomeSwiper, /* { default options with global component } */);

// JsonExcel
import JsonExcel from 'vue-json-excel';
Vue.component('downloadExcel', JsonExcel);

//引入组件库
import jvuewheel from '@jyeontu/jvuewheel'
//引入样式
import '@jyeontu/jvuewheel/lib/jvuewhell.css'
Vue.use(jvuewheel);

// 将自动注册所有组件为全局组件
import dataV from '@jiaminghi/data-view'

Vue.use(dataV)

// import qs from 'qs'
import $ from 'jquery';
import 'bootstrap/dist/js/bootstrap.min.js';
import './report';

import 'bootstrap/dist/css/bootstrap.min.css';
import 'element-ui/lib/theme-chalk/index.css';
import './assets/icon/iconfont.css';
import './assets/font_8dxc161t53x/iconfont.css';
import './assets/font_oncjam3tj3a/iconfont.css';
import './assets/fonts/demo.css';
// import './components/ef/index.css';
// 全局公共样式
import '@/global.scss';
// 页面公共样式
import '@/style/common.scss';

import fileSaver from "file-saver";
//自定义的element UI loading样式
import './assets/myCss.css';
import InputDart from "@/components/element-dart/InputDart";
import SelectDart from "@/components/element-dart/SelectDart";
import DatePickerDart from "@/components/element-dart/DatePickerDart";
import CpTable from "@/components/CpTable";
import BreadCrumb from "@/components/BreadCrumb.vue";

// 自定义指令
import directives from './directives/index';
Vue.use(directives);
import store from './store/store';
// 拓扑
// import jsPlumb from 'jsplumb';

// Vue.prototype.$jsPlumb = jsPlumb.jsPlumb;

import AmapVue from 'vue-amap';
Vue.use(AmapVue);

Vue.component('cp-table', CpTable);
Vue.component('dart-input', InputDart);
Vue.component('dart-date-picker', DatePickerDart);
Vue.component('dart-select', SelectDart);
Vue.component("BreadCrumb", BreadCrumb);

Vue.prototype.$echarts = echarts;
Vue.prototype.$del = function (title = '此操作将永久删除该数据, 是否继续?', cb) {
  const cbs = [].slice.call(arguments, 1);
  this.$confirm(title, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(async () => {
      for (let i = 0; i < cbs.length; i++) {
        try {
          if (i === 0) {
            await this.$confirm('请您再次确认是否执行删除操作！', '提示', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning'
            });
          }
          const result = await cbs[i]();
          if (i === 0) {
            if (result.RESULTCODE === '0000' || result.data.success) {
              Vue.prototype.$notify({
                position: 'top-right',
                type: 'success',
                title: '结果',
                message: '删除成功',
                duration: 2000
              });
            }
          }
        } catch {
          console.log('参数需要是一个函数');
        }
      }
    })
    .catch(() => { });
};

Vue.prototype.resetSetItem = function (key, newVal) {
  if (key === 'watchStorage') {

    // 创建一个StorageEvent事件
    var newStorageEvent = document.createEvent('StorageEvent');
    const storage = {
      setItem: function (k, val) {
        sessionStorage.setItem(k, val);

        // 初始化创建的事件
        newStorageEvent.initStorageEvent('setItem', false, false, k, null, val, null, null);

        // 派发对象
        window.dispatchEvent(newStorageEvent);
      }
    };
    return storage.setItem(key, newVal);
  }
};

//  解析每个页面action
Reflect.defineProperty(Vue.prototype, '$enterpriseInfo', {
  get() {
    return JSON.parse(sessionStorage.getItem('enterpriseInfo') || '{}');
  }
});
// http response 拦截器
/*axios.interceptors.response.use(
    response => {
      let response_Result = response;
      if (response_Result.data.code == "401") { //token认证过期，请重新登录
        message({
          message: response_Result.data.msg,
          type: "error",
        });
        localStorage.clear();
        sessionStorage.clear();
        router.push('/login');
      }
      return {
        response_Result,
        data: response_Result.data
      };
    });*/


axios.defaults.withCredentials = true;
Vue.prototype.$axios = axios;
Vue.config.productionTip = false;
Vue.use(ElementUI);
Vue.filter('formatterNull', function (value) {
  return value ?? '-';
});
const alarmCur='0';
// Vue.use($);
new Vue({
  render: h => h(App),
  router,
  store
}).$mount('#app');

