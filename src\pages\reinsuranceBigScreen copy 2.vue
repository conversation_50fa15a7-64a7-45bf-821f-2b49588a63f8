<template>
  <div
    class="big-screen-container"
    style="height: 100%; overflow: hidden; position: relative;"
  >
    <!-- 编辑控制按钮 -->
    <div style="position: absolute; top: 1%; right: 1%; z-index: 99;">
      <el-button
        v-if="!editMode"
        size="small"
        @click="toggleEditMode"
      >
        编辑
      </el-button>
      <el-button
        v-if="editMode"
        size="small"
        type="primary"
        @click="saveConfig"
      >
        保存
      </el-button>
      <el-button
        v-if="editMode"
        size="small"
        @click="cancelEdit"
      >
        取消
      </el-button>
      <!-- <el-button
        size="small"
        @click="addNewScene"
      >
        新增场景
      </el-button> -->
    </div>
    

    <!-- 顶部标题栏（编辑模式下显示输入框） -->
    <div class="header-long">
      <span class="header-right-middle">
        <el-input
          v-if="editMode"
          v-model="configEditing.title"
          placeholder="请输入标题"
          size="small"
          style="width: 25%;"
        />
        <span v-else>{{ basicConf.title }}</span>
      </span>
      <div class="header-right-time">
        {{ currentTime }}
      </div>
    </div>

    <!-- 线路列表框 -->
    <div 
      v-if="editMode"
      style="width: 25%;
        height: 60%;
        position: absolute;
        top: 8%;
        right: 1%;
        z-index: 9;"
    >
      <div
        class="search-container"
        style="height: 50px;"
      >
        <el-button
          v-if="!addRouter"
          size="small"
          type="primary"
          style="float: right;"
          @click="addRouter = true"
        >
          新增线路
        </el-button>
        <!-- @click="draw('polyline')" -->
        <!-- @click="searchLocation" -->
        <!-- <el-button
          size="small"
          @click="searchLocation"
        >
          <i class="el-icon-view" /> 预览
        </el-button> -->
        <!-- 搜索结果列表 -->
        <!--  v-if="(searchResults.length > 0 || isSearching || searchError)"  -->
        <div 
          v-if="false"
          class="search-results-container"
        >
          <div class="search-results-header">
            <span>搜索结果</span>
            <el-button 
              type="text" 
              size="mini" 
              style="color: #909399; padding: 0;"
              @click="closeSearchResults"
            >
              关闭
            </el-button>
          </div>
      
          <div class="search-results-content">
            <!-- 加载状态 -->
            <div
              v-if="isSearching"
              class="search-loading"
            >
              <i
                class="el-icon-loading"
                style="margin-right: 8px;"
              />
              <span>正在搜索...</span>
            </div>
        
            <!-- 搜索错误 -->
            <div
              v-else-if="searchError"
              class="search-error"
            >
              <i
                class="el-icon-error"
                style="margin-right: 8px; color: #f56c6c;"
              />
              <span>{{ searchError }}</span>
            </div>
        
            <!-- 无结果 -->
            <div
              v-else-if="false"
              class="search-no-results"
            >
              <i
                class="el-icon-info"
                style="margin-right: 8px; color: #909399;"
              />
              <span>未找到相关结果，请尝试其他关键词</span>
            </div>
        
            <!-- 搜索结果 -->
            <div
              v-if="false"
              class="search-results-list"
            >
              <div 
                v-for="(result, index) in searchResults" 
                :key="index" 
                class="search-result-item"
                :class="{ 'active': selectedResult && selectedResult.title === result.title }"
                @click="selectSearchResult(result)"
              >
                <div class="result-title">
                  <i
                    class="el-icon-place"
                    style="margin-right: 8px; color: #3b82f6;"
                  />
                  <span>{{ result.title }}</span>
                </div>
                <div class="result-address">
                  <i
                    class="el-icon-location"
                    style="margin-right: 8px; color: #909399; font-size: 12px;"
                  />
                  <span>{{ result.address }}</span>
                </div>
                <div class="result-coordinates">
                  <span>坐标: {{ result.point.lng.toFixed(6) }}, {{ result.point.lat.toFixed(6) }}</span>
                </div>
              </div>
            </div>
          </div>
      
          <!-- 搜索结果页脚 -->
          <div class="search-results-footer">
            <span>共找到 {{ searchResults.length }} 个结果</span>
          </div>
        </div>
      </div>
      <el-form
        v-if="addRouter"
        ref="form"
        :model="routerForm"
        size="mini"
        class="route-naming"
      >
        <el-form-item label="起点">
          <el-input
            v-model="routerForm.startPoint.pointName"
            placeholder="请输入搜索关键词"
            clearable
            @focus=" openMapClicks(1)"
          >
            <i
              slot="suffix"
              class="el-input__icon el-icon-search"
              @click="searchLocation(routerForm.startPoint.pointName)"
            />
          </el-input>
          <el-button
            type="primary"
            style="margin-left: 1%;"
            :disabled="!routerForm.startPoint.pointName"
            @click="openSetupPoint('起点',routerForm.startPoint)"
          >
            设置站点
          </el-button>
        </el-form-item>
        <el-form-item label="终点">
          <el-input
            v-model="routerForm.endPoint.pointName"
            placeholder="请输入搜索关键词"
            clearable
            @focus="openMapClicks(2)"
          >
            <i
              slot="suffix"
              class="el-input__icon el-icon-search"
              @click="searchLocation(routerForm.endPoint.pointName)"
            />
          </el-input>
          <el-button
            type="primary"
            style="margin-left: 1%;"
            :disabled="!routerForm.endPoint.pointName"
            @click="openSetupPoint('终点',routerForm.endPoint)"
          >
            设置站点
          </el-button>
        </el-form-item>
        <el-form-item label="线路">
          <el-input
            v-model="routerForm.lineName"
            placeholder="请输入保障线路名称"
          />
          <el-button
            type="primary"
            style="margin-left: 1%;"
          >
            设置线路
          </el-button>
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            @click="addRouters"
          >
            立即创建
          </el-button>
          <el-button @click="cancelAdd">
            取消
          </el-button>
        </el-form-item>
      </el-form>
      <!-- <el-table
        :data="tableData"
        style="width: 100%;
        border-radius: 1%;
        margin-top: 1%;"
      >
        <el-table-column
          prop="date"
          label="线路名称"
        />
        <el-table-column
          prop="name"
          label="起点"
        />
        <el-table-column
          prop="address"
          label="终点"
        />
        <el-table-column
          fixed="right"
          label="操作"
          width="100">
          <template slot-scope="scope">
            <el-button type="text" size="small">删除</el-button>
          </template>
        </el-table-column>
      </el-table> -->
    </div>

    <el-dialog
      :title="pointsType+'信息'"
      :visible.sync="setupPointDialog"
      width="450px"
    >
      <el-form
        :model="form"
        size="mini"
      >
        <el-form-item
          label="站点别名"
          :label-width="formLabelWidth"
        >
          <el-input
            v-model="form.pointAlias"
            autocomplete="off"
          />
        </el-form-item>
        <el-form-item
          label="站点位置"
          :label-width="formLabelWidth"
        >
          <el-input
            v-model="form.pointName"
            autocomplete="off"
            disabled
          />
        </el-form-item>
        <el-form-item
          label="站点精度"
          :label-width="formLabelWidth"
        >
          <el-input
            v-model="form.longitude"
            autocomplete="off"
            disabled
          />
        </el-form-item>
        <el-form-item
          label="站点维度"
          :label-width="formLabelWidth"
        >
          <el-input
            v-model="form.latitude"
            autocomplete="off"
            disabled
          />
        </el-form-item>
        <el-form-item
          label="站点图标"
          :label-width="formLabelWidth"
        >
          <el-upload
            ref="iconUpload"
            class="upload-demo"
            :action="uploadUrl"
            :on-success="handleIconSuccess"
            :on-error="handleUploadError"
            :before-upload="beforeIconUpload"
            :show-file-list="false"
            :headers="headers"
            :on-progress="handleUploadProgress"
            :http-request="customUpload"
          >
            <el-button
              v-if="!form.iconUrl"
              size="mini"
            >
              上传图标
            </el-button>
            <div
              v-if="form.iconUrl"
              class="upload-preview"
            >
              <el-image
                :src="form.iconUrl"
                alt="站点图标"
                class="thumbnail-image"
                :preview-src-list="[form.iconUrl]"
              />
              <el-button
                size="mini"
                type="danger"
                circle
                icon="el-icon-delete"
                class="remove-btn"
                @click="removeIcon"
              />
            </div>
            <div
              v-if="uploadingIcon"
              class="upload-progress"
            >
              <el-progress
                :percentage="uploadIconProgress"
                status="active"
              />
            </div>
          </el-upload>
          <el-input
            v-model="form.iconUrl"
            autocomplete="off"
            style="display: none"
          />
        </el-form-item>
        <el-form-item
          label="站点图片"
          :label-width="formLabelWidth"
        >
          <el-upload
            ref="imageUpload"
            class="upload-demo"
            :action="uploadUrl"
            :on-success="handleImageSuccess"
            :on-error="handleUploadError"
            :before-upload="beforeImageUpload"
            :show-file-list="false"
            :headers="headers"
            :on-progress="handleUploadProgress"
            :http-request="customUpload"
          >
            <el-button
              v-if="!form.ImgUrl"
              size="mini"
            >
              上传图片
            </el-button>
            <div
              v-if="form.ImgUrl"
              class="upload-preview"
            >
              <el-image
                :src="form.ImgUrl"
                alt="站点图片"
                class="thumbnail-image"
                :preview-src-list="[form.ImgUrl]"
              />
              <el-button
                size="mini"
                type="danger"
                circle
                icon="el-icon-delete"
                class="remove-btn"
                @click="removeImage"
              />
            </div>
            <div
              v-if="uploadingImage"
              class="upload-progress"
            >
              <el-progress
                :percentage="uploadImageProgress"
                status="active"
              />
            </div>
          </el-upload>
          <el-input
            v-model="form.ImgUrl"
            autocomplete="off"
            style="display: none"
          />
        </el-form-item>
        <el-form-item
          label="站点类型"
          :label-width="formLabelWidth"
        >
          <el-select v-model="form.pointType" placeholder="请选择站点类型">
            <el-option label="中心点" value="中心点"></el-option>
            <el-option label="外围点" value="外围点"></el-option>
            <el-option label="光缆点" value="光缆点"></el-option>
            <el-option label="定位点" value="定位点"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          label="防火墙数量"
          :label-width="formLabelWidth"
        >
          <el-input
            v-model="form.fhqNum"
            autocomplete="off"
          />
        </el-form-item>
        <el-form-item
          label="交换机数量"
          :label-width="formLabelWidth"
        >
          <el-input
            v-model="form.jhjNum"
            autocomplete="off"
          />
        </el-form-item>
        <el-form-item>
          <el-button style="float: right;" @click="cancelSiteChanges">取消</el-button>
          <el-button type="primary" style="float: right;margin-right:10px" @click="confirmSiteChanges(form)">确认</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
    
    <!-- 场景切换按钮 -->
    <!-- <div
      v-show="showTail"
      class="scene-buttons"
    >
      <div class="buttonZ1">
        <span
          class="buttonSpanCur"
          @click="changeScene(1)"
        >制证网</span>
      </div>
      <div class="buttonZ2">
        <span
          class="buttonSpan"
          @click="changeScene(4)"
        >指挥网</span>
      </div>
      <div class="buttonZ3">
        <span
          class="buttonSpan"
          @click="changeScene(3)"
        >重保场景-线路</span>
      </div>
      <div class="buttonZ4">
        <span
          class="buttonSpan"
          @click="changeScene(2)"
        >重保场景-拓扑</span>
      </div>
    </div> -->
    
    <!-- 地图选点提示 -->
    <div 
      v-if="isSelectingRoute" 
      class="route-selector-tip"
    >
      <span>{{ routeSelectionStep === 1 ? '请选择线路起始点' : '请选择线路终止点' }}</span>
      <el-button 
        size="mini" 
        type="danger" 
        style="margin-left: 10px;"
        @click="cancelSelectingRoute"
      >
        取消
      </el-button>
    </div>
    
    <!-- 地图容器 -->
    <div
      id="allmap"
      class="map-container"
    />
    <ul class="drawing-panel">
      <!-- 修改后 -->
      <!-- <li class="bmap-btn bmap-marker" id="marker" @click="draw('marker')"></li> -->
      <!-- <li
        id="polyline"
        class="bmap-btn bmap-polyline"
        @click="draw('polyline')"
      /> -->
      <!-- <li class="bmap-btn bmap-rectangle" id="rectangle" @click="draw('rectangle')"></li>
<li class="bmap-btn bmap-polygon" id="polygon" @click="draw('polygon')"></li>
<li class="bmap-btn bmap-circle" id="circle" @click="draw('circle')"></li> -->
    </ul>
  </div>
</template>


<script>
import { formatTime } from "../assets/js/index.js";
import darkStyle from '../assets/js/darkStyle.json';
import $ from "jquery";
// import BMapGL from 'bmapgl';

export default {
  name: "ReinsuranceBigScreen",
  data() {
    return {
      // 编辑状态管理
      isPointSelected:false,
      addRouter: false,
      editMode: true,
      setupPointDialog:false,
      configEditing: {
        title: "陕西重保OSS监控系统",
        mapCenter: [108.953418, 34.274803],
        mapZoom: 13
      },
      
      // 基础数据
      showTail: true,
      currentTime: "",
      
      // 场景控制
      scene: 1,
      
      // 地图相关
      map: null,
      mapLoaded: false,
      rotateTimer: null,
      rotateAngle: 0,
      
      // 基础配置
      basicConf: {
        title: "陕西重保OSS监控系统",
        mapConfig: {
          center: [108.953418, 34.274803],
          zoom: 13
        },
        autoRotate: {
          enable: true,
          interval: 30000
        }
      },
      
      // 线路选点相关
      pointsType:'',
      isSelectingRoute: false,
      routeSelectionStep: 0, // 0: 未开始, 1: 选择起始点, 2: 选择终止点
      routeStartPoint: null,
      routeEndPoint: null,
      routeMarkers: [], // 存储选点标记
      isNamingRoute: false,
      newRouteName: '',


      // 搜索功能相关
      searchKeyword: '',
      searchResults: [],
      searchPopupVisible: false,
      isSearching: false,
      searchError:null,
      
      // 地图服务相关（需引入百度地图API）
      localSearch: null,
      geocoder: null,

      // 线路绘制相关
      isDrawingRoute: false,
      currentRoute: null,
      routePolyline: null, // 临时线路
      savedRoutes: [], // 保存的线路数据
      drawingManager:{},
      // 线路相关
      routerForm: {
        "startPoint": {
          "pointName": "",
          "pointAlias":"",
          "longitude": "",
          "latitude": "",
          "iconUrl":"",
          "ImgUrl":"",
          "pointType":"",
        },
        "endPoint": { 
          "pointName": "",
          "pointAlias":"",
          "longitude": "",
          "latitude": "",
          "iconUrl":"",
          "ImgUrl":"",
          "pointType":"",
        },
        "lineName": "",
        "businessTypeList": "5",
      },
      routes: [], // 存储所有已创建的线路
      tableData: [{
            date: '高新T/高新J',
            name: '高新T(108.953418, 34.274803)',
            address: '高新J(108.953418, 34.274803)'
          }, {
            date: '高新T/高新J',
            name: '高新T(108.953418, 34.274803)',
            address: '高新J(108.953418, 34.274803)'
          }, {
            date: '高新T/高新J',
            name: '高新T(108.953418, 34.274803)',
            address: '高新J(108.953418, 34.274803)'
          }, {
            date: '高新T/高新J',
            name: '高新T(108.953418, 34.274803)',
            address: '高新J(108.953418, 34.274803)'
          }],
        form: {
          pointName:'',
          coordinates:''
        },
        formLabelWidth: '100px',
        uploadUrl: '/protect-api/api/file/upload',
      headers: {
        Authorization: `Bearer ${this.$store.state.token || ''}`
      },
      // 上传状态
      uploadingIcon: false,
      uploadingImage: false,
      uploadIconProgress: 0,
      uploadImageProgress: 0,
      // 服务器基础URL
      serverBaseUrl: '/api',
      currentMarker: null,
    };
  },
  computed: {
    // 筛选后的线路列表
    filteredRoutes() {
      let result = [...this.routes];
      
      // 应用搜索筛选
      if (this.routeSearchKeyword) {
        const keyword = this.routeSearchKeyword.toLowerCase();
        result = result.filter(route => 
          route.name.toLowerCase().includes(keyword) ||
          route.startPoint[0].toString().includes(keyword) ||
          route.startPoint[1].toString().includes(keyword) ||
          route.endPoint[0].toString().includes(keyword) ||
          route.endPoint[1].toString().includes(keyword)
        );
      }
      
      // 应用排序
      if (this.sortField) {
        result.sort((a, b) => {
          if (this.sortField === 'name') {
            return this.sortOrder === 'ascending' 
              ? a.name.localeCompare(b.name) 
              : b.name.localeCompare(a.name);
          } else if (this.sortField === 'length') {
            return this.sortOrder === 'ascending' 
              ? parseFloat(a.length) - parseFloat(b.length) 
              : parseFloat(b.length) - parseFloat(a.length);
          }
          return 0;
        });
      }
      
      // 应用分页
      const startIndex = (this.currentPage - 1) * this.pageSize;
      const endIndex = startIndex + this.pageSize;
      return result.slice(startIndex, endIndex);
    }
  },
  watch: {
    // 监听场景变化，重新加载图层
    scene() {
      this.map.clearOverlays();
      this.addMapLayers();
    },
    
    // 监听routes变化，重新绘制线路
    routes: {
      deep: true,
      handler() {
        // 当routes变化时，重新绘制所有线路
        if (this.map && this.mapLoaded) {
          this.map.clearOverlays();
          this.addMapLayers();
        }
      }
    }
  },
  mounted() {
    // 初始化地图
    this.initMap();
    
    // 启动自动旋转
    // this.startAutoRotate();
    
    // 更新时间
    this.updateCurrentTime();
    setInterval(() => this.updateCurrentTime(), 1000);
    
    // 编辑模式下添加地图监听
    if (this.editMode) {
      this.map.addEventListener('dragend', this.handleMapDrag);
      this.map.addEventListener('zoomend', this.handleMapZoom);
    }

    // 添加绘图完成事件监听
  this.drawingManager.addEventListener('overlaycomplete', (e) => {
    // e.overlay 是绘制完成的覆盖物对象
    const overlay = e.overlay;
    
    // 处理不同类型的覆盖物
    if (overlay.getPath) {
      // 线或多边形有getPath方法
      const path = overlay.getPath();
      console.log('绘制完成，路径点数量:', path.length);
      
      // 这里可以根据需要处理路径数据
      // 例如：保存线路、计算长度等
    } else if (overlay.getCenter) {
      // 圆有getCenter方法
      const center = overlay.getCenter();
      const radius = overlay.getRadius();
      console.log('绘制完成，圆心:', center, '半径:', radius);
    } else if (overlay.getPosition) {
      // 标记点有getPosition方法
      const position = overlay.getPosition();
      console.log('绘制完成，标记点位置:', position);
    }
    
    // 绘制完成后关闭绘图管理器
    this.drawingManager.close();
    
    // 重置绘图按钮样式
    const drawingButtons = document.querySelectorAll('.bmap-btn');
    drawingButtons.forEach(btn => {
      btn.style.backgroundPositionY = '0';
    });
  });
    
  },
  beforeDestroy() {
    // 清除定时器
    clearInterval(this.rotateTimer);
    
    // 移除编辑模式下的事件监听
    if (this.editMode) {
      this.map.removeEventListener('dragend', this.handleMapDrag);
      this.map.removeEventListener('zoomend', this.handleMapZoom);
    }
    
    // 移除选点事件监听
    this.map.removeEventListener('click', this.handleMapClick);
  },
  methods: {
    // 初始化地图
    initMap() {
      this.map = new BMapGL.Map('allmap');
      const center = new BMapGL.Point(
        this.configEditing.mapCenter[0], 
        this.configEditing.mapCenter[1]
      );
      this.map.centerAndZoom(center, this.configEditing.mapZoom);
      this.map.enableScrollWheelZoom(true);
      this.map.setTilt(45);
      this.changeMapStyle();
      
      // 监听地图加载完成
      this.map.addEventListener('tilesloaded', () => {
        this.mapLoaded = true;
        this.addMapLayers();
      });
      
      
      // 初始化搜索服务
      this.initSearchServices();

      var styleOptions = {
        strokeColor: '#5E87DB',   // 边线颜色
        fillColor: '#5E87DB',     // 填充颜色。当参数为空时，圆形没有填充颜色
        strokeWeight: 2,          // 边线宽度，以像素为单位
        strokeOpacity: 1,         // 边线透明度，取值范围0-1
        fillOpacity: 0.2          // 填充透明度，取值范围0-1
    };
    var labelOptions = {
        borderRadius: '2px',
        background: '#FFFBCC',
        border: '1px solid #E1E1E1',
        color: '#703A04',
        fontSize: '12px',
        letterSpacing: '0',
        padding: '5px'
    };
       // 实例化鼠标绘制工具
       this.drawingManager = new BMapGLLib.DrawingManager(this.map, {
         enableCalculate: false, // 绘制是否进行测距测面
         enableSorption: true,   // 是否开启边界吸附功能
         sorptiondistance: 20,   // 边界吸附距离
         circleOptions: styleOptions,     // 圆的样式
         polylineOptions: styleOptions,   // 线的样式
         polygonOptions: styleOptions,    // 多边形的样式
         rectangleOptions: styleOptions,  // 矩形的样式
         labelOptions: labelOptions,      // label样式
       });  

       this.addReguaranteeLogic();  //增加重保逻辑
    },
    addReguaranteeLogic() {
      let _this = this;
      let queryData = { "busType": '1' };
      _this.$axios.post("/protect-api/pointLine/getAllPointLineByBus", queryData).then((data) => {
        if (data.data.code === "0000") {
          _this.reguaranteeLogicList = data.data.data.zbPosList;
          _this.reguaranteeLogicList2 = data.data.data.zbLines;
          _this.markList = _this.reguaranteeLogicList;
          var data = [];
          let aaaaa = {};
          var randomCount = 2; // 模拟的飞线的数量
          var curve = new mapvgl.BezierCurve();
          let points = []
          _this.reguaranteeLogicList.map((item, index) => {
            let polylineArr1 = [];
            let polylineArr2 = [];
            let polylineArr3 = [];

            
            points.push([item.posLng, item.posLat])



            polylineArr1.push(new BMapGL.Point(_this.reguaranteeLogicList[0].posLng, _this.reguaranteeLogicList[0].posLat));
            let point = new BMapGL.Point(item.posLng, item.posLat);
            let myIcon = new BMapGL.Icon(item.posUrl, new BMapGL.Size(23, 33));
            let marker2 = new BMapGL.Marker(point, { icon: myIcon, "aa": item });  // 创建标注
             // 为 Marker 添加自定义数据
             marker2.myData = item;
            
            if ('1' != item.posType) {
              polylineArr1.push(new BMapGL.Point(item.posLng, item.posLat));
              marker2.customData = item;
              let color = ["#dfde3a", "#dfde3a", "#dfde3a"];
              let border = "2px";
              if (null != item.posId) {
                let colorArr = _this.reguaranteeLogicList2.filter(cc => cc.posIdTarget == item.posId);
                if (undefined != colorArr[0]) {
                  /*       color = colorArr[0].lineColor;*/
                  border = colorArr[0].lineBold;
                }
                if (undefined != colorArr && colorArr.length > 0) {
                  color = colorArr[0].lineColor.split(",");
                }
                //告警点打!
                if (undefined != colorArr[0] && colorArr[0].lineType === "event") {  //event
                  let ppp = colorArr[0].lineStr.split(";");
                  let ll = ppp[0].split(",");
                  let ll2 = ppp[1].split(",");
                  let pointArr = [new BMapGL.Point(Number(ll[0]), Number(ll[1])), new BMapGL.Point(Number(ll2[0]), Number(ll2[1]))];
                  let middenP = _this.getMidpoiont(pointArr);
                  let urlP = require("../assets/zxx/warning.png");
                  let myIcon = new BMapGL.Icon(urlP, new BMapGL.Size(25, 25));
                  let marker3 = new BMapGL.Marker(middenP, { icon: myIcon, "aa": item });  // 创建标注
                  _this.map.addOverlay(marker3);
                }
                //告警点打X
                if (undefined != colorArr[0] && colorArr[0].lineType === "alarm") {
                  let ppp = colorArr[0].lineStr.split(";");
                  let ll = ppp[0].split(",");
                  let ll2 = ppp[1].split(",");
                  let pointArr = [new BMapGL.Point(Number(ll[0]), Number(ll[1])), new BMapGL.Point(Number(ll2[0]), Number(ll2[1]))];
                  let middenP = _this.getMidpoiont(pointArr);
                  let urlP = require("../assets/zxx/quit.png");
                  let myIcon = new BMapGL.Icon(urlP, new BMapGL.Size(16, 16));
                  let marker3 = new BMapGL.Marker(middenP, { icon: myIcon, "aa": item });  // 创建标注
                  _this.map.addOverlay(marker3);
                }
              }
              let polyline1 = new BMapGL.Polyline(polylineArr1, { strokeColor: color[0], strokeWeight: border[0], strokeOpacity: 0.9 });   //创建折线
              if (item.posName === "联通省公司" || item.posName === "联通西安分公司") {
              }else {
                _this.map.addOverlay(polyline1);
              }
              /*              let polyline2 = new BMapGL.Polyline(polylineArr2, { strokeColor: color[1], strokeWeight: border[0], strokeOpacity: 0.9 });   //创建折线
                            _this.map.addOverlay(polyline2);
                            let polyline3 = new BMapGL.Polyline(polylineArr3, { strokeColor: color[2], strokeWeight: border[0], strokeOpacity: 0.9 });   //创建折线
                            _this.map.addOverlay(polyline3);*/
              _this.map.addOverlay(marker2);
              marker2.customData = item;
            }
            if (index === 0) {
              _this.map.addOverlay(marker2);
              marker2.setAnimation(BMAP_ANIMATION_BOUNCE);
            }
            _this.marker2 = marker2;
            marker2.customData = item;
            let lableText = _this.initTabchuang(item);
            marker2.setLabel(lableText);
            _this.clickInfomation = true;
            marker2.addEventListener("mouseover", async function (param) {
              let list2Length1 = _this.reguaranteeLogicList.length;
              if (_this.clickInfomation && undefined == document.getElementsByClassName("BMapLabel")[list2Length1]) {
                let lableText2 = await _this.tabchuang(item);
                marker2.setLabel(lableText2);
                document.getElementsByClassName("BMapLabel")[list2Length1].style.border = "1px #1ef1a5";
                document.getElementsByClassName("BMapLabel")[list2Length1].style.padding = "";
                _this.queryData = { "busType": '10', "posId": param.target.customData.posId };
                _this.getSTData(_this.queryData);
                _this.getYjData(_this.queryData);
                $("#openZhongbaoTeam").val(param.target.customData);
                document.getElementById("openZhongbaoTeam").onclick = function (e) {
                  let jj = e.target.value;
                  let p = {};
                  if(e.target.value.posName==="联通省公司"){
                    p.parentId = "2";
                  } else if (param.posName === "联通西安分公司") {
                    p.parentId = "3";
                  }else {
                    p.parentId = "4";
                  }
                  if (undefined != jj && null != jj) {
                    _this.$router.push({ path: "/home", query: { p } });
                  }
                };
                document.getElementById("informationExplains").onmouseover = function (e) {
                  $("#informationExplainsDetails").show();
                };
                document.getElementById("informationExplains").onmouseout = function (e) {
                  $("#informationExplainsDetails").hide();
                };
              }
            });
            marker2.addEventListener("mouseout", function () {
                  if (_this.clickInfomation) {
                    _this.queryData.busType = "10";
                    _this.queryData.posId ="";
                    _this.getSTData(_this.queryData);
                    _this.getYjData(_this.queryData);
                    let list2Length2 = _this.reguaranteeLogicList.length;
                    if (undefined != document.getElementsByClassName("BMapLabel")[list2Length2]) {
                      docume_thisnt.getElementsByClassName("BMapLabel")[list2Length2].remove();
                    }
                  }
                }
            );
            marker2.addEventListener("click", function (e) {
               // 清除上一个标记点（如果存在）
               if (this.currentMarker) {
                 this.map.removeOverlay(this.currentMarker);
               }
               _this.isPointSelected = true
               console.log(e.srcElement.myData);
               
              if (_this.addRouter) {
                  if (_this.routeSelectionStep === 1) {
                      // 设置起点坐标
                      _this.routerForm.startPoint.longitude = e.srcElement.myData.posLng
                      _this.routerForm.startPoint.latitude = e.srcElement.myData.posLat
                      _this.routerForm.startPoint.pointName = e.srcElement.myData.posName
                      _this.routerForm.startPoint.pointAlias = e.srcElement.myData.posName
                      
                      _this.routerForm.startPoint.iconUrl = e.srcElement.myData.posImgUrlXianLu
                      _this.routerForm.startPoint.ImgUrl = e.srcElement.myData.posRealUrl

                      
                  } else if (_this.routeSelectionStep === 2) {
                      // 设置终点坐标
                      _this.routerForm.endPoint.longitude = e.srcElement.myData.posLng
                      _this.routerForm.endPoint.latitude = e.srcElement.myData.posLat
                      _this.routerForm.endPoint.pointName = e.srcElement.myData.posName
                      _this.routerForm.endPoint.pointAlias = e.srcElement.myData.posName
                      _this.routerForm.endPoint.iconUrl = e.srcElement.myData.posImgUrlXianLu
                      _this.routerForm.endPoint.ImgUrl = e.srcElement.myData.posRealUrl
                  }
              }
              if (_this.clickInfomation) {
                marker2.removeEventListener("mouseout", function () { });
                _this.clickInfomation = false;
              } else {
                marker2.addEventListener("mouseout", function () {
                  _this.queryData.busType = "10";
                  _this.queryData.posId ="";
                  _this.getSTData(_this.queryData);
                  _this.getYjData(_this.queryData);
                  let list2Length2 = _this.reguaranteeLogicList.length;
                  if (undefined != document.getElementsByClassName("BMapLabel")[list2Length2]) {
                    document.getElementsByClassName("BMapLabel")[list2Length2].remove();
                  }
                });
                _this.clickInfomation = true;
              }
            });
          });
          setTimeout(() => {
            for (let i = 0; i < _this.reguaranteeLogicList.length; i++) {
              if (undefined != document.getElementsByClassName("BMapLabel")[i]) {
                document.getElementsByClassName("BMapLabel")[i].style.border = "1px dashed #1ef1a5";
                document.getElementsByClassName("BMapLabel")[i].style.padding = "";
              }
            }
            $(".BMapLabel").hide();
          }, 250);
          _this.map.addEventListener("zoomend", function (e) {
            if (_this.markShowSt) { return false; };
            let zoom = _this.map.getZoom(); // 获取缩放级别
            if (zoom < 14) {
              for (let i = 0; i < _this.reguaranteeLogicList.length; i++) {
                if (undefined != document.getElementsByClassName("BMapLabel")[i]) {
                  document.getElementsByClassName("BMapLabel")[i].style.display = "none";
                }
              }
            } else {
              for (let i = 0; i < _this.reguaranteeLogicList.length; i++) {
                if (undefined != document.getElementsByClassName("BMapLabel")[i]) {
                  document.getElementsByClassName("BMapLabel")[i].style.display = "";
                }
              }
            }
          });
        }
      });
    },
    getYjData(params) {
      let _this = this;
/*      let queryData = {
        "busType": params
      };*/
      _this.$axios.post("/protect-api/zb/getYjwz", params).then((data) => {
        if (data.data.code === '0000') {
          // console.log(data);
          this.yjData = data.data.data;
        }
      });
    },
    getSTData(queryData) {
      let _this = this;
      _this.$axios.post("/protect-api/zb/getSTData", queryData).then((data) => {
        if (data.data.code === '0000') {
          _this.sTDataInfo = data.data.data;
        }
      });
    },
    initTabchuang(param) {
      let url = param.posRealUrl;
      let bottom2 = require("../assets/zxx/bottom2.png");
      let locationTab = require("../assets/zxx/locationTab.png");
      let text = '<div style="z-index: 1; width:170Px;height: 112Px;background-image: url(' + locationTab + ') ;background-repeat: no-repeat;background-size: 100% 100%;">' +
          '<div style="z-index: 1;display: flex;' +
          'flex-direction: row;align-content: center;justify-content: space-between; align-items: center;">' +
          '            <img src="' + url + ' " id="imgDemo" alt="" style="height:90px;width:100%">' +
          '    </div>' +
          '<div style="width: 100%;height: 18px;font-size: 14px;text-align:center;font-family: Source Han Sans CN;font-weight: 400;color: #FFFFFF;line-height: 22px;">' + param.posName + '</div>' +
          '</div>' +
          '<div style="position: relative;"> <img src="' + bottom2 + ' "  alt="" style="position: absolute;left: 45%;"></div>';
      var label = new BMapGL.Label(text, { offset: new BMapGL.Size(-91, -150) });
      return label;
    },
    async tabchuang(param) {
      let _this = this;
      let bottom = require("../assets/zxx/bottom.png");
      let explains = require("../assets/zxx/explains.png");
      let query = {};
      query.dataType = "1";
      if (param.posLevel == 'X') {
        if (param.posName === "联通省公司") {
          query.userGroupType = "GROUP_SF";
          query.posId = "";
        } else if (param.posName === "联通西安分公司") {
          query.userGroupType = "GROUP_XF";
          query.posId = "";
        }
      } else {
        query.posId = param.posId;
        query.userGroupType = "GROUP_XC";
      }
      /*      let query={};*/
      await _this.$axios.post("/protect-api/person/getAllPersonList", query).then((data) => {
        if (data.data.code === "0000") {
          _this.zbPosiNFO = data.data.data;
          /*          param.target.customInfo = _this.zbPosiNFO;*/
        } else {
          _this.zbPosiNFO = null;
        }
      });
      let text1 = '<div id="information" style="z-index: 1003;width: 365Px;height: 265Px;border: 1Px solid;border-image: linear-gradient(0deg, #4AB38C, #105745) 10 10;background: linear-gradient(0deg, #0E2D28 0%, rgba(7,70,83,0.8) 100%);">' +
          '    <div>' +
          '        <div style="z-index:3;width: 365Px;height: 39Px;background: linear-gradient(90deg, #25BFAB 0%, rgba(25,146,156,0.5) 100%);opacity: 0.3;">' +
          '        </div>' +
          '        <span style="z-index:3;margin-top:-38px;margin-left:10px;position:fixed;width: 78Px;opacity: 1;height: 18Px;font-size: 18Px;font-family: Source Han Sans CN;' +
          '        font-weight: 500;color: #FFFFFF;line-height: 38Px;text-shadow: 0Px 2Px 8Px rgba(5,28,55,0.42);">重保团队</span>' +
          '        <span id="informationExplains" style="position: fixed;margin-top: -33px;margin-left: 85px;"><img src="' + explains + '" style="width: 16px;height:16px;"></span>' +
          '        <span id="informationExplainsDetails" style="position: fixed;z-index:4;margin-top: -70px;margin-left: 80px;' +
          'background: #07607c;position: absolute;border-radius: 4px;padding: 10px;font-size: 12px;line-height: 1.2;min-width: 10px;word-wrap: break-word;' +
          ' color: #FFF;display: none">注：再次点击图标隐藏此弹窗（点击定位图标）</span>' +
          '        <div id="openZhongbaoTeam" style="margin-top:-32px;margin-left:313px;position:fixed;width: 50px;height: 14px;font-size: 14px;font-family: Source Han Sans CN;font-weight: 500;color: #FFFFFF;line-height: 29px;cursor:pointer;">详情 ></div>' +
          '    </div>' +
          '    <div class="contont" style="z-index:1003; position:relative; display: inline-block;width: 360px;height: 217px;font-size: 14px;font-family: Source Han Sans CN;font-weight: 400;color: #FFFEFE;line-height: 28px; overflow-y:auto;overflow-x:hidden;">';
      let text3 = '    </div>' +
          '</div>' +
          '<div style="position: relative;margin-top: -1px;"> <img src="' + bottom + ' "  alt="" style="position: absolute;left: 45%;"></div>';
      let text2 = "";
      if (undefined != _this.zbPosiNFO && null != _this.zbPosiNFO&& _this.zbPosiNFO.length>0) {
        _this.zbPosiNFO.map(item => {
          text2 = text2 + ' <div style="width: 352px;height: 28px;background: #0E4950;margin: 7px 6px">' +
              '            <span style="width: 6Px;height: 6Px;margin-left: 10Px; background: #F19149;border-radius: 50%;display: inline-block;"></span>' +
              '            <span style="display: inline-block;margin-left:3px;width: 60px;height: 25px;font-size: 14px;font-family: Source Han Sans CN;font-weight: 400;color: #30DFE9;line-height:28px;' +
              '  ">' + item.userName + '</span>' +
              '            <span style="margin-left:13px;width: 41px;height: 14px;font-size: 14px;font-family: Source Han Sans CN;font-weight: 400;line-height:28px;">电话：</span>' +
              '            <span style="margin-left:-2px;width: 41px;height: 14px;font-size: 14px;font-family: Source Han Sans CN;font-weight: 400;color: #30DFE9;line-height:28px;">' + item.userPhone + '</span>' +
              '            <span style="margin-left:13px;width: 41px;height: 14px;font-size: 14px;font-family: Source Han Sans CN;font-weight: 400;line-height:28px;">专业：</span>' +
              '            <span style="width: 41px;height: 14px;font-size: 14px;font-family: Source Han Sans CN;font-weight: 400;color: #30DFE9;line-height:28px;">' + item.userProfession + '</span>' +
              '        </div>';
        });
      } else {
        text2 = '        <div style="width: 352px;height: 28px;background: #0E4950;margin: 7px 6px">' +
            '            <span style="width: 6Px;height: 6Px;margin-left: 10Px; background: #F19149;border-radius: 50%;display: inline-block;"></span>' +
            '            <span style="margin-left:3px;width: 41px;height: 14px;font-size: 14px;font-family: Source Han Sans CN;font-weight: 400;color: #30DFE9;line-height:28px;">暂无信息</span>' +
            '        </div>';
      }
      let label = new BMapGL.Label(text1 + text2 + text3, { offset: new BMapGL.Size(-177, -301) });
      return label;
    },

    // 修改后的draw方法
    draw(drawingType) {
      this.addRouter = true;
  // 获取所有绘图按钮
  const drawingButtons = document.querySelectorAll('.bmap-btn');
  
  // 重置所有按钮样式
  drawingButtons.forEach(btn => {
    btn.style.backgroundPositionY = '0';
  });
  
  // 获取当前点击的按钮
  const currentButton = document.getElementById(drawingType);
  if (currentButton) {
    // 设置当前按钮样式
    currentButton.style.backgroundPositionY = '-52px';
  }
  
  // 根据按钮ID确定绘图类型常量
  let drawingTypeConstant;
  switch(drawingType) {
    case 'marker':
      drawingTypeConstant = BMAP_DRAWING_MARKER;
      break;
    case 'polyline':
      drawingTypeConstant = BMAP_DRAWING_POLYLINE;
      break;
    case 'rectangle':
      drawingTypeConstant = BMAP_DRAWING_RECTANGLE;
      break;
    case 'polygon':
      drawingTypeConstant = BMAP_DRAWING_POLYGON;
      break;
    case 'circle':
      drawingTypeConstant = BMAP_DRAWING_CIRCLE;
      break;
    default:
      return;
  }
  
  // 控制绘图管理器
  if (this.drawingManager._isOpen && this.drawingManager.getDrawingMode() === drawingTypeConstant) {
    this.drawingManager.close();
    // 移除事件监听
    this.removeDrawingEvents();
  } else {
    this.drawingManager.setDrawingMode(drawingTypeConstant);
    this.drawingManager.open();
    // 添加事件监听
    this.addDrawingEvents(drawingType);
  }
},
openMapClicks(val){
  this.routeSelectionStep = val;
  this.isPointSelected = false;
  // 监听地图点击事件
  this.map.addEventListener('click', this.handleMapClick);
},

// 添加绘图事件监听
addDrawingEvents(drawingType) {
  // 清除之前的信息
  this.clearCoordinatesInfo();
  
  // 根据不同的绘图类型添加相应的事件监听
  switch(drawingType) {
    case 'marker':
      this.drawingManager.addEventListener('markercomplete', this.onMarkerComplete.bind(this));
      break;
    case 'polyline':
      this.drawingManager.addEventListener('polylinecomplete', this.onPolylineComplete.bind(this));
      this.drawingManager.addEventListener('lineupdate', this.onLineUpdate.bind(this));
      break;
    case 'rectangle':
      this.drawingManager.addEventListener('rectanglecomplete', this.onRectangleComplete.bind(this));
      break;
    case 'polygon':
      this.drawingManager.addEventListener('polygoncomplete', this.onPolygonComplete.bind(this));
      this.drawingManager.addEventListener('polygonupdate', this.onPolygonUpdate.bind(this));
      break;
    case 'circle':
      this.drawingManager.addEventListener('circlecomplete', this.onCircleComplete.bind(this));
      break;
  }
},

// 移除绘图事件监听
removeDrawingEvents() {
  this.drawingManager.removeEventListener('markercomplete', this.onMarkerComplete.bind(this));
  this.drawingManager.removeEventListener('polylinecomplete', this.onPolylineComplete.bind(this));
  this.drawingManager.removeEventListener('lineupdate', this.onLineUpdate.bind(this));
  this.drawingManager.removeEventListener('rectanglecomplete', this.onRectangleComplete.bind(this));
  this.drawingManager.removeEventListener('polygoncomplete', this.onPolygonComplete.bind(this));
  this.drawingManager.removeEventListener('polygonupdate', this.onPolygonUpdate.bind(this));
  this.drawingManager.removeEventListener('circlecomplete', this.onCircleComplete.bind(this));
},

// 清除坐标信息显示
clearCoordinatesInfo() {
  const infoDiv = document.getElementById('coordinates-info');
  if (infoDiv) {
    infoDiv.innerHTML = '';
  }
},

// 显示坐标信息
// showCoordinatesInfo(info) {
//   let infoDiv = document.getElementById('coordinates-info');
//   if (!infoDiv) {
//     infoDiv = document.createElement('div');
//     infoDiv.id = 'coordinates-info';
//     infoDiv.style.position = 'absolute';
//     infoDiv.style.bottom = '10px';
//     infoDiv.style.left = '10px';
//     infoDiv.style.backgroundColor = 'rgba(255, 255, 255, 0.8)';
//     infoDiv.style.padding = '10px';
//     infoDiv.style.borderRadius = '5px';
//     infoDiv.style.zIndex = '100';
//     document.body.appendChild(infoDiv);
//   }
//   infoDiv.innerHTML += `<p>${info}</p>`;
// },

// 标记点完成回调
onMarkerComplete(marker) {
  const point = marker.getPosition();
  // this.showCoordinatesInfo(`标记点坐标: ${point.lng}, ${point.lat}`);
},

// 折线完成回调
onPolylineComplete(polyline) {
    const path = polyline.getPath();
    console.log(path);
    const coordinates = path.map(point => `${point.lng}, ${point.lat}`).join('; ');
    // this.showCoordinatesInfo(`折线坐标: ${coordinates}`);
    
    // 获取每个点的地址信息
    this.getAddressesForPath(path);
  },
  
  // 获取路径上每个点的地址信息
  getAddressesForPath(path) {
    
    if (!this.geocoder) {
      this.geocoder = new BMapGL.Geocoder();
    }
    
    // 限制请求数量，避免超出API限制
    const MAX_POINTS = 10; // 最多获取10个点的地址信息
    const pointsToQuery = path.length <= MAX_POINTS ? path : 
                          path.filter((_, index) => index % Math.floor(path.length / MAX_POINTS) === 0 || 
                                                index === 0 || index === path.length - 1);
    
    // 显示加载状态
    // this.showCoordinatesInfo('正在获取地址信息...');
    
    // 串行处理地址请求，避免并发过多
    const processNextPoint = (index) => {
      if (index >= pointsToQuery.length) {
        return;
      }
      
      const point = pointsToQuery[index];
      this.$axios.post(`/protect-api/map/longitudeToAddress?lat=${point.lat.toFixed(8)}&lng=${point.lng.toFixed(8)}`,).then((data) => {
        if (data.data.code === "0000") {
          let result = data.data.data.result;
          if (index == 0) {
            this.routerForm.startPoint.pointName = result.formatted_address;
            this.routerForm.startPoint.longitude = point.lng.toFixed(8);
            this.routerForm.startPoint.latitude = point.lat.toFixed(8);
            this.routerForm.startPoint.pointAlias = result.formatted_address;
            processNextPoint(index + 1);
            // this.routerForm.startPoint.address = result.content.poi_desc ? result.content.poi_desc : result.formatted_address;
          } else {
            this.routerForm.endPoint.pointName = result.formatted_address;
            this.routerForm.endPoint.longitude = point.lng.toFixed(8);
            this.routerForm.endPoint.latitude = point.lat.toFixed(8);
            this.routerForm.endPoint.pointAlias = result.formatted_address;
            // this.routerForm.endPoint.address = result.content.poi_desc ? result.content.poi_desc : result.formatted_address;
          }
          this.routerForm.lineName = this.routerForm.startPoint.pointName+' / '+this.routerForm.endPoint.pointName;
        }
      });
    };
    // this.routerForm.startPoint = result.address;
    // 开始处理第一个点
    processNextPoint(0);
  },
  
  // 显示坐标信息
  showCoordinatesInfo(info) {
    let infoDiv = document.getElementById('coordinates-info');
    if (!infoDiv) {
      infoDiv = document.createElement('div');
      infoDiv.id = 'coordinates-info';
      infoDiv.style.position = 'absolute';
      infoDiv.style.bottom = '10px';
      infoDiv.style.left = '10px';
      infoDiv.style.backgroundColor = 'rgba(255, 255, 255, 0.8)';
      infoDiv.style.padding = '10px';
      infoDiv.style.borderRadius = '5px';
      infoDiv.style.zIndex = '100';
      infoDiv.style.maxWidth = '400px';
      infoDiv.style.maxHeight = '200px';
      infoDiv.style.overflowY = 'auto';
      document.body.appendChild(infoDiv);
    }
    
    // 创建新的信息项
    const infoItem = document.createElement('p');
    infoItem.style.margin = '5px 0';
    infoItem.style.wordBreak = 'break-all';
    infoItem.innerHTML = info;
    infoDiv.appendChild(infoItem);
    
    // 自动滚动到底部
    infoDiv.scrollTop = infoDiv.scrollHeight;
  },


// 折线更新回调（实时获取点位）
onLineUpdate(polyline) {
  const path = polyline.getPath();
  if (path.length > 0) {
    const lastPoint = path[path.length - 1];
    // this.showCoordinatesInfo(`当前点位: ${lastPoint.lng}, ${lastPoint.lat}`);
  }
},

    
    // 初始化搜索服务
    initSearchServices() {
      // 初始化本地搜索
      this.localSearch = new BMapGL.LocalSearch(this.map, {
        renderOptions: {
          map: this.map,
          autoViewport: true,
          panel: '' // 不使用默认面板，自定义结果展示
        },
        onSearchComplete: (results) => {
          this.handleSearchResults(results);
        }
      });
      
      // 初始化地理编码服务
      this.geocoder = new BMapGL.Geocoder();
    },

    
    // 搜索位置（带防抖）
    searchLocation(val) {
      // 清除之前的计时器
      if (this.debounceTimer) {
        clearTimeout(this.debounceTimer);
      }
      
      // 检查关键词
      const keyword = val.trim();
      if (!keyword) {
        this.searchError = '请输入搜索关键词';
        return;
      }
      
      // 设置防抖延迟
      this.debounceTimer = setTimeout(() => {
        this.performSearch(keyword);
      }, 300);
    },
    
    // 执行实际搜索
    performSearch(keyword) {
      this.isSearching = true;
      this.searchError = null;
      this.searchResults = [];
      
      // 检查是否是坐标格式
      const coordinateRegex = /^(\d+\.\d+),\s*(\d+\.\d+)$/;
      const match = keyword.match(coordinateRegex);
      
      if (match) {
        // 是坐标格式，直接定位
        try {
          const lng = parseFloat(match[1]);
          const lat = parseFloat(match[2]);
          const point = new BMapGL.Point(lng, lat);
          
          this.map.centerAndZoom(point, 15);
          
          // 添加标记
          this.addMarker(point);
          
          // 获取地址信息
          this.geocoder.getLocation(point, (result) => {
            if (result) {
              this.searchResults = [{
                title: `坐标点 (${lng.toFixed(6)}, ${lat.toFixed(6)})`,
                address: result.address,
                point: point
              }];
            } else {
              this.searchResults = [{
                title: `坐标点 (${lng.toFixed(6)}, ${lat.toFixed(6)})`,
                address: '未知地址',
                point: point
              }];
            }
            
            this.isSearching = false;
          });
        } catch (error) {
          this.isSearching = false;
          this.searchError = '坐标格式不正确，请使用"经度,纬度"格式';
        }
      } else {
        console.log('执行关键词搜索:', keyword);
        // 不是坐标格式，进行关键词搜索
        this.localSearch.search(keyword);
      }
    },
    
    // 处理搜索结果
handleSearchResults(results) {
  this.isSearching = false;
  this.searchResults = [];
  
  console.log('搜索结果对象:', results); // 添加调试信息
  
  // 检查结果是否有效
  if (!results) {
    this.searchError = '搜索结果无效';
    return;
  }
  console.log(results.getPoi(0));
  
  // 检查搜索是否成功 - 使用百度地图API的错误码
  if (results) {
    // 检查结果数量
    const count = results.getCurrentNumPois ? results.getCurrentNumPois() : 0;
    
    if (count > 0) {
      for (let i = 0; i < count; i++) {
        const poi = results.getPoi(i);
        if (poi) {
          // 使用 push 方法添加结果，而不是覆盖
          this.searchResults.push({
            title: poi.title,
            address: poi.address || '未知地址',
            point: poi.point
          });
        }
      }
      
      if (this.searchResults.length === 1) {
        // 如果只有一个结果，直接选择
        this.selectSearchResult(this.searchResults[0]);
      } else {
        // 显示多个结果的弹窗
        this.searchPopupVisible = true;
      }
    } else {
      this.searchError = '未找到相关结果';
    }
  } else {
    // 搜索失败
    const errorMsg = results && results.error ? 
      `搜索错误: ${results.error}` : '搜索失败，请重试';
    this.searchError = errorMsg;
    console.error('搜索错误:', errorMsg);
  }
},

    
    // 选择搜索结果
    selectSearchResult(result) {
      this.selectedResult = result;
      this.searchPopupVisible = false;
      
      // 定位到选中的位置
      this.map.centerAndZoom(result.point, 15);
      
      // 添加标记
      this.addMarker(result.point);
      
      // 隐藏搜索结果
      this.closeSearchResults();
      
      // 清空搜索框
      this.searchKeyword = '';
    },
    
    // 添加标记
    addMarker(point) {
      // 清除之前的标记
      // this.map.clearOverlays();
      
      // 添加新标记
      const marker = new BMapGL.Marker(point);
      this.map.addOverlay(marker);
      
      // 添加信息窗口
      const infoWindow = new BMapGL.InfoWindow(`
        <div style="font-size: 12px;">
          <p>位置: ${this.selectedResult ? this.selectedResult.title : '坐标点'}</p>
          <p>坐标: ${point.lng.toFixed(6)}, ${point.lat.toFixed(6)}</p>
          <p>地址: ${this.selectedResult ? this.selectedResult.address : '未知地址'}</p>
        </div>
      `);
      
      marker.addEventListener('click', () => {
        this.map.openInfoWindow(infoWindow, point);
      });
      
      // 自动打开信息窗口
      this.map.openInfoWindow(infoWindow, point);
    },
    
    // 关闭搜索结果
    closeSearchResults() {
      this.searchResults = [];
      this.searchError = null;
      this.isSearching = false;
    },
    
    // 清空搜索内容
    clearSearch() {
      this.searchKeyword = '';
    },
    // 处理地图点击事件
  handleMapClick(e) {
    if (!this.isPointSelected) {
      // this.map.clearOverlays();
    const point = e.latlng;
    const lng = point.lng.toFixed(8);
    const lat = point.lat.toFixed(8);
    // 清除上一个标记点（如果存在）
  if (this.currentMarker) {
    this.map.removeOverlay(this.currentMarker);
  }
    // 创建标记点
    this.currentMarker = new BMapGL.Marker(point, {
    // 标记点样式（可自定义图标）
    icon: new BMapGL.Icon('http://**********:9000/important-protection-test/2025/06/13/0e8a696085dd49c7af68f6a0acb7a691.png', new BMapGL.Size(26, 42), {
      anchor: new BMapGL.Size(13, 42)
    })
  });
  
  // 添加标记点到地图
  this.map.addOverlay(this.currentMarker);
    
    // 获取点击点位的地址信息
    this.$axios.post(`/protect-api/map/longitudeToAddress?lat=${lat}&lng=${lng}`,).then((data) => {
        if (data.data.code === "0000") {
          let result = data.data.data.result;
          const address = result.formatted_address;
          if (this.routeSelectionStep === 1) {
          // 显示信息窗口
          const infoWindow = new BMapGL.InfoWindow(`
                <div style="padding: 8px; font-size: 12px; width: 250px;">
                  <p><strong>地址:</strong> ${address}</p>
                  <p><strong>坐标:</strong> ${lng}, ${lat}</p>
                </div>
              `,{
            offset: new BMapGL.Size(0, -20) // 第一个参数是水平偏移，第二个参数是垂直偏移（负值表示向上）
          });
          this.map.openInfoWindow(infoWindow, point);
            this.routerForm.startPoint.pointName = result.formatted_address;
            this.routerForm.startPoint.longitude = point.lng.toFixed(8);
            this.routerForm.startPoint.latitude = point.lat.toFixed(8);
            this.routerForm.startPoint.pointAlias = result.formatted_address;
            this.routerForm.lineName = this.routerForm.startPoint.pointName+' / '+this.routerForm.endPoint.pointName;
            // this.routerForm.startPoint.address = result.content.poi_desc ? result.content.poi_desc : result.formatted_address;
          } else if (this.routeSelectionStep === 2) {
            // 显示信息窗口
          const infoWindow = new BMapGL.InfoWindow(`
                <div style="padding: 8px; font-size: 12px; width: 250px;">
                  <p><strong>地址:</strong> ${address}</p>
                  <p><strong>坐标:</strong> ${lng}, ${lat}</p>
                </div>
              `,{
            offset: new BMapGL.Size(0, -20) // 第一个参数是水平偏移，第二个参数是垂直偏移（负值表示向上）
          });
          this.map.openInfoWindow(infoWindow, point);
            this.routerForm.endPoint.pointName = result.formatted_address;
            this.routerForm.endPoint.longitude = point.lng.toFixed(8);
            this.routerForm.endPoint.latitude = point.lat.toFixed(8);
            this.routerForm.endPoint.pointAlias = result.formatted_address;
            this.routerForm.lineName = this.routerForm.startPoint.pointName+' / '+this.routerForm.endPoint.pointName;
            // 当终点选择完成后，连接起点和终点
            this.connectStartAndEndPoints();
            this.map.removeEventListener('click', this.handleMapClick);
            // this.routerForm.endPoint.address = result.content.poi_desc ? result.content.poi_desc : result.formatted_address;
          } else {
            // 未明确选择起点/终点时，提示用户
            this.$message({
              message: '请先点击"新增线路"按钮开始选择起点和终点',
              type: 'info'
            });
            // this.routerForm.lineName = this.routerForm.startPoint.pointName+' / '+this.routerForm.endPoint.pointName;
          };
        }
      });

    }
    
  },
      // 连接起点和终点的方法
      connectStartAndEndPoints() {
        // this.map.clearOverlays();
        if (this.currentMarker) {
          this.map.removeOverlay(this.currentMarker);
        }
        // 检查起点和终点是否都已选择
        if (this.routerForm.startPoint.longitude && this.routerForm.endPoint.longitude) {
          // 创建起点和终点的坐标点
          const startPoint = new BMapGL.Point(
            parseFloat(this.routerForm.startPoint.longitude),
            parseFloat(this.routerForm.startPoint.latitude)
          );
          
          const endPoint = new BMapGL.Point(
            parseFloat(this.routerForm.endPoint.longitude),
            parseFloat(this.routerForm.endPoint.latitude)
          );
          let aUrl = 'http://**********:9000/important-protection-test/2025/06/13/0e8a696085dd49c7af68f6a0acb7a691.png'
          // 创建起点标记
          const startMarker = new BMapGL.Marker(startPoint, {
            icon: new BMapGL.Icon(this.routerForm.startPoint.iconUrl?this.routerForm.startPoint.iconUrl:aUrl, 
                                 new BMapGL.Size(32, 32), {
              anchor: new BMapGL.Size(16, 32)
            })
          });
          
          // 创建终点标记
          const endMarker = new BMapGL.Marker(endPoint, {
            icon: new BMapGL.Icon(this.routerForm.endPoint.iconUrl?this.routerForm.endPoint.iconUrl:aUrl, 
                                 new BMapGL.Size(32, 32), {
              anchor: new BMapGL.Size(16, 32)
            })
          });
          
          // 创建连接起点和终点的折线
          const polyline = new BMapGL.Polyline([startPoint, endPoint], {
            strokeColor: "#FF3300",  // 折线颜色
            strokeWeight: 3,         // 折线宽度
            strokeOpacity: 0.8,      // 折线透明度
            strokeStyle: "solid",    // 折线样式
            enableEditing: false     // 禁用编辑
          });
          
          // 添加折线和标记点到地图
          this.map.addOverlay(polyline);
          this.map.addOverlay(startMarker);
          this.map.addOverlay(endMarker);
          
          // 计算并设置地图视图，确保起点和终点都在视野内
          // this.map.setViewport([startPoint, endPoint]);
          this.routeSelectionStep = 0;
        }
      },
    
    // 切换地图样式
    changeMapStyle() {
      this.map.setOptions({
        style: {
          styleJson: darkStyle
        },
        styleUrl: 'https://gis.10010.com:8219/dugis-baidu/baidumap/bmapgl/mapstyle/mapstyle.json'
      });
    },
    
    // 添加地图图层
    addMapLayers() {
      // 这里可以根据场景添加不同图层
      if (this.scene === 1) {
        this.addScene1Markers();
      } else if (this.scene === 2) {
        this.addScene2Topology();
      }
      
      // 绘制所有已保存的线路
      this.routes.forEach(route => {
        this.drawRoute(route);
      });
    },
    
    // 添加场景1标记
    addScene1Markers() {
      // 模拟数据
      const markers = [
        { id: 1, lng: 108.9, lat: 34.3, iconUrl: 'https://example.com/server-icon.png' },
        { id: 2, lng: 109.0, lat: 34.2, iconUrl: 'https://example.com/network-icon.png' }
      ];
      
      markers.forEach(item => {
        const point = new BMapGL.Point(item.lng, item.lat);
        const icon = new BMapGL.Icon(item.iconUrl, new BMapGL.Size(32, 32), {
          anchor: new BMapGL.Size(16, 32)
        });
        
        const marker = new BMapGL.Marker(point, { icon });
        this.map.addOverlay(marker);
        
        // 添加点击事件
        marker.addEventListener('click', () => {
          this.showDeviceDetail(item.id);
        });
      });
    },
    
    // 添加场景2拓扑图
    addScene2Topology() {
      // 拓扑图逻辑...
    },
    
    // 切换场景
    changeScene(sceneId) {
      this.scene = sceneId;
      this.map.clearOverlays();
      this.addMapLayers();
    },
    
    // 显示设备详情
    showDeviceDetail(deviceId) {
      // 模拟请求
      const device = {
        id: deviceId,
        name: `设备-${deviceId}`,
        type: '服务器',
        status: 'normal',
        ip: '192.168.1.' + deviceId,
        manager: '管理员',
        phone: '1380013800' + deviceId
      };
      
      this.showNoticePopover({
        title: "设备详情",
        content: `
          <div class="detail-item">设备名称: ${device.name}</div>
          <div class="detail-item">设备类型: ${device.type}</div>
          <div class="detail-item">当前状态: <span class="status-${device.status}">正常</span></div>
          <div class="detail-item">IP地址: ${device.ip}</div>
          <div class="detail-item">负责人: ${device.manager}</div>
          <div class="detail-item">联系电话: ${device.phone}</div>
        `
      });
    },
    
    // 显示弹窗
    showNoticePopover(obj) {
      this.noticePopoverObj = obj;
      this.noticePopoverShow = true;
    },
    
    // 关闭弹窗
    closeNoticePopover() {
      this.noticePopoverShow = false;
    },
    
    // 处理地图拖拽
    handleMapDrag() {
      const center = this.map.getCenter();
      this.configEditing.mapCenter = [center.lng, center.lat];
    },
    
    // 处理地图缩放
    handleMapZoom() {
      this.configEditing.mapZoom = this.map.getZoom();
    },
    
    // 切换编辑模式
    toggleEditMode() {
      this.editMode = !this.editMode;
      
      if (this.editMode) {
        // 进入编辑模式
        this.map.addEventListener('dragend', this.handleMapDrag);
        this.map.addEventListener('zoomend', this.handleMapZoom);
      } else {
        // 退出编辑模式
        this.map.removeEventListener('dragend', this.handleMapDrag);
        this.map.removeEventListener('zoomend', this.handleMapZoom);
      }
    },
    
    // 取消编辑
    cancelEdit() {
      this.configEditing = {
        title: this.basicConf.title,
        mapCenter: [...this.basicConf.mapConfig.center],
        mapZoom: this.basicConf.mapConfig.zoom
      };
      this.editMode = false;
    },
    
    // 保存配置
    saveConfig() {
      this.basicConf.title = this.configEditing.title;
      this.basicConf.mapConfig.center = this.configEditing.mapCenter;
      this.basicConf.mapConfig.zoom = this.configEditing.mapZoom;
      
      // 模拟保存到后端
      this.$axios.post('/api/config/save', this.basicConf).then(res => {
        if (res.data.code === '0000') {
          this.$message({
            message: '配置保存成功',
            type: 'success'
          });
          this.editMode = false;
        }
      });
    },
    
    // 新增场景
    addNewScene() {
      this.$router.push('/new-scene-config');
    },
    
    // 启动自动旋转
    startAutoRotate() {
      if (this.basicConf.autoRotate.enable) {
        this.rotateTimer = setInterval(() => {
          this.rotateAngle = (this.rotateAngle + 1) % 360;
          this.map.setRotation(this.rotateAngle);
        }, this.basicConf.autoRotate.interval);
      }
    },
    
    // 停止自动旋转
    stopAutoRotate() {
      clearInterval(this.rotateTimer);
      this.rotateTimer = null;
    },
    
    // 更新当前时间
    updateCurrentTime() {
      this.currentTime = formatTime(new Date(), "yyyy-MM-dd HH:mm:ss");
    },
    
    // 线路列表相关方法
    toggleRouteList() {
      this.showRouteList = !this.showRouteList;
    },
    
    // 获取线路状态
    getRouteStatus(route) {
      switch(route.status) {
        case 'normal': return 'success';
        case 'warning': return 'warning';
        case 'error': return 'danger';
        default: return 'info';
      }
    },
    
    // 获取线路状态文本
    getRouteStatusText(route) {
      switch(route.status) {
        case 'normal': return '正常';
        case 'warning': return '警告';
        case 'error': return '故障';
        default: return '未知';
      }
    },
    
    // 处理线路点击
    handleRouteClick(route) {
      // 高亮显示选中的线路
      this.highlightRoute(route);
      
      // 显示线路详情
      this.showRouteDetail(route);
    },
    
    // 处理线路双击
    handleRouteDblClick(route) {
      // 编辑线路
      this.editRoute(route);
    },
    
    // 高亮显示线路
    highlightRoute(route) {
      // 清除之前的高亮
      this.routes.forEach(r => {
        if (r.polyline) {
          r.polyline.setStrokeWeight(3);
          r.polyline.setStrokeOpacity(0.8);
        }
      });
      
      // 高亮当前线路
      if (route.polyline) {
        route.polyline.setStrokeWeight(5);
        route.polyline.setStrokeOpacity(1.0);
      }
      
      // 居中显示线路
      const startPoint = new BMapGL.Point(route.startPoint[0], route.startPoint[1]);
      const endPoint = new BMapGL.Point(route.endPoint[0], route.endPoint[1]);
      
      // 计算中心点
      const centerPoint = new BMapGL.Point(
        (startPoint.lng + endPoint.lng) / 2,
        (startPoint.lat + endPoint.lat) / 2
      );
      
      // 缩放级别可以根据线路长度动态调整
      const distance = this.map.getDistance(startPoint, endPoint);
      let zoom = 13;
      
      if (distance > 10000) {
        zoom = 11;
      } else if (distance < 1000) {
        zoom = 15;
      }
      
      this.map.centerAndZoom(centerPoint, zoom);
    },
    
    // 编辑线路
    editRoute(route, event) {
      // 阻止事件冒泡，避免触发表格行点击
      if (event) {
        event.stopPropagation();
      }
      
      // 保存当前编辑的线路ID
      this.editingRouteId = route.id;
      
      // 填充编辑表单
      this.editRouteForm = {
        name: route.name,
        startPoint: `${route.startPoint[0]},${route.startPoint[1]}`,
        endPoint: `${route.endPoint[0]},${route.endPoint[1]}`,
        color: route.color,
        status: route.status
      };
      
      // 显示编辑弹窗
      this.editRouteDialogVisible = true;
    },
    
    // 取消编辑线路
    cancelEditRoute() {
      this.editRouteDialogVisible = false;
      this.editRouteForm = {};
      this.editingRouteId = null;
    },
    
    // 保存编辑的线路
    saveEditRoute() {
      // 验证表单
      if (!this.editRouteForm.name) {
        this.$message.error('请输入线路名称');
        return;
      }
      
      // 解析坐标
      const startPointParts = this.editRouteForm.startPoint.split(',');
      const endPointParts = this.editRouteForm.endPoint.split(',');
      
      if (startPointParts.length !== 2 || endPointParts.length !== 2) {
        this.$message.error('坐标格式不正确，请使用"经度,纬度"格式');
        return;
      }
      
      const startPoint = [parseFloat(startPointParts[0]), parseFloat(startPointParts[1])];
      const endPoint = [parseFloat(endPointParts[0]), parseFloat(endPointParts[1])];
      
      // 查找并更新线路
      const routeIndex = this.routes.findIndex(r => r.id === this.editingRouteId);
      
      if (routeIndex !== -1) {
        // 保存旧的线路引用以便移除
        const oldPolyline = this.routes[routeIndex].polyline;
        
        // 更新线路数据
        this.routes[routeIndex] = {
          ...this.routes[routeIndex],
          name: this.editRouteForm.name,
          startPoint,
          endPoint,
          color: this.editRouteForm.color,
          status: this.editRouteForm.status,
          length: this.calculateRouteLength({ startPoint, endPoint }).toFixed(2)
        };
        
        // 移除旧线路
        if (oldPolyline) {
          this.map.removeOverlay(oldPolyline);
        }
        
        // 绘制新线路
        this.drawRoute(this.routes[routeIndex]);
        
        // 提示成功
        this.$message({
          message: `线路 "${this.editRouteForm.name}" 更新成功`,
          type: 'success'
        });
        
        // 关闭弹窗
        this.cancelEditRoute();
      }
    },
    
    // 删除线路
    deleteRoute(route, event) {
      // 阻止事件冒泡，避免触发表格行点击
      if (event) {
        event.stopPropagation();
      }
      
      // 保存当前要删除的线路ID和名称
      this.deleteRouteId = route.id;
      this.deleteRouteName = route.name;
      
      // 显示确认弹窗
      this.deleteRouteDialogVisible = true;
    },
    
    // 确认删除线路
    confirmDeleteRoute() {
      // 查找并删除线路
      const routeIndex = this.routes.findIndex(r => r.id === this.deleteRouteId);
      
      if (routeIndex !== -1) {
        // 移除地图上的线路
        if (this.routes[routeIndex].polyline) {
          this.map.removeOverlay(this.routes[routeIndex].polyline);
        }
        
        // 从数组中删除
        this.routes.splice(routeIndex, 1);
        
        // 提示成功
        this.$message({
          message: `线路 "${this.deleteRouteName}" 删除成功`,
          type: 'success'
        });
      }
      
      // 关闭弹窗
      this.deleteRouteDialogVisible = false;
      this.deleteRouteId = null;
      this.deleteRouteName = '';
    },
    
    // 筛选线路
    filterRoutes() {
      this.currentPage = 1;
    },
    
    // 排序线路
    sortRoutes(field) {
      if (this.sortField === field) {
        // 切换排序方向
        this.sortOrder = this.sortOrder === 'ascending' ? 'descending' : 'ascending';
      } else {
        // 设置新的排序字段，默认升序
        this.sortField = field;
        this.sortOrder = 'ascending';
      }
      
      // 重置分页
      this.currentPage = 1;
    },
    
    // 处理每页数量变化
    handleSizeChange(newSize) {
      this.pageSize = newSize;
    },
    
    // 处理当前页变化
    handleCurrentChange(newPage) {
      this.currentPage = newPage;
    },
    openSetupPoint(type,point){
      this.pointsType = type;
      this.form = point;
      this.setupPointDialog = true;
    },
    // 站点图片上传
    // 处理图标上传成功
   // 自定义上传方法
   customUpload(params) {
      const { file, onSuccess, onError, onProgress } = params;
      const formData = new FormData();
      formData.append('file', file);
      
      // 添加额外参数
      const isIcon = file.name.includes('icon') || file.name.includes('logo');
      formData.append('fileType', isIcon ? 'icon' : 'image');
      formData.append('bucket', 'site-resources');
      
      // 创建XMLHttpRequest对象
      const xhr = new XMLHttpRequest();
      
      // 监听上传进度
      xhr.upload.addEventListener('progress', (e) => {
        if (e.total > 0) {
          const percentage = Math.round((e.loaded / e.total) * 100);
          if (isIcon) {
            this.uploadIconProgress = percentage;
          } else {
            this.uploadImageProgress = percentage;
          }
          onProgress({ percent: percentage });
        }
      });
      
      // 监听请求完成
      xhr.addEventListener('load', () => {
        if (xhr.status >= 200 && xhr.status < 300) {
          try {
            const response = JSON.parse(xhr.responseText);
            onSuccess(response);
          } catch (e) {
            onError(new Error('解析响应数据失败'));
          }
        } else {
          onError(new Error(xhr.statusText));
        }
        
        // 重置上传状态
        if (isIcon) {
          this.uploadingIcon = false;
        } else {
          this.uploadingImage = false;
        }
      });
      
      // 监听请求错误
      xhr.addEventListener('error', () => {
        onError(new Error('上传请求失败'));
        if (isIcon) {
          this.uploadingIcon = false;
        } else {
          this.uploadingImage = false;
        }
      });
      
      // 监听请求取消
      xhr.addEventListener('abort', () => {
        onError(new Error('上传已取消'));
        if (isIcon) {
          this.uploadingIcon = false;
        } else {
          this.uploadingImage = false;
        }
      });
      
      // 配置请求
      xhr.open('POST', this.uploadUrl, true);
      
      // 设置请求头（注意：不要设置Content-Type，让浏览器自动设置为multipart/form-data）
      Object.keys(this.headers).forEach(key => {
        xhr.setRequestHeader(key, this.headers[key]);
      });
      
      // 发送请求
      xhr.send(formData);
    },
    
    // 处理上传进度
    handleUploadProgress(event, file, fileList) {
      const percentage = Math.round((event.loaded / event.total) * 100);
      if (file.name.includes('icon')) {
        this.uploadIconProgress = percentage;
      } else {
        this.uploadImageProgress = percentage;
      }
    },
    
    // 处理图标上传成功
    handleIconSuccess(response, file, fileList) {
      this.uploadingIcon = false;
      this.uploadIconProgress = 0;
      
      if (response && response.code === "0000") {
        this.form.iconUrl = response.data.fileUrl || `${this.serverBaseUrl}/files/${response.data.fileId}`;
        this.$message.success('图标上传成功');
      } else {
        this.$message.error('图标上传失败: ' + (response.message || '未知错误'));
      }
    },
    
    // 处理图片上传成功
    handleImageSuccess(response, file, fileList) {
      if (response && response.code === "0000") {
        this.form.ImgUrl = response.data.fileUrl || `${this.serverBaseUrl}/files/${response.data.fileId}`;
        this.$message.success('图标上传成功');
      } else {
        this.$message.error('图标上传失败: ' + (response.message || '未知错误'));
      }
    },
    
    // 处理上传错误
    handleUploadError(error, file, fileList) {
      this.$message.error('上传出错，请稍后再试');
      console.error('上传错误:', error);
    },
    
    // 上传前检查图标
    beforeIconUpload(file) {
      const isJPG = file.type === 'image/jpeg' || file.type === 'image/png';
      const isLt2M = file.size / 1024 / 1024 < 2;
      
      if (!isJPG) {
        this.$message.error('图标只能是JPG/PNG格式!');
      }
      if (!isLt2M) {
        this.$message.error('图标大小不能超过2MB!');
      }
      return isJPG && isLt2M;
    },
    
    // 上传前检查图片
    beforeImageUpload(file) {
      const isJPG = file.type === 'image/jpeg' || file.type === 'image/png';
      const isLt5M = file.size / 1024 / 1024 < 5;
      
      if (!isJPG) {
        this.$message.error('图片只能是JPG/PNG格式!');
      }
      if (!isLt5M) {
        this.$message.error('图片大小不能超过5MB!');
      }
      return isJPG && isLt5M;
    },
    
    // 移除图标
    removeIcon() {
      this.$axios.post(`/protect-api/api/file/delete?imgUrl=${this.form.iconUrl}`,).then((data) => {
        if (data.data.code === '0000') {
          this.form.iconUrl = '';
          this.$message.success('图标删除成功');
        }  else {
          this.$message.error('图标删除失败: ' + data.data.message);
        }
      });
    },
    
    // 移除图片
    removeImage() {
      this.$axios.post(`/protect-api/api/file/delete?imgUrl=${this.form.ImgUrl}`,).then((data) => {
        if (data.data.code === '0000') {
          this.form.ImgUrl = '';
          this.$message.success('图标删除成功');
        }  else {
          this.$message.error('图标删除失败: ' + data.data.message);
        }
      });
    },
    cancelAdd(){
      this.addRouter = false;
      if (this.form.ImgUrl && this.form.ImgUrl !== '') {
        this.removeImage();
      }
      if (this.form.iconUrl&&this.form.iconUrl !== '') {
        this.removeIcon();
      }
      this.form = {};
      this.map.clearOverlays();
    },
    cancelSiteChanges(){
      // console.log(this.form);
      // console.log(this.startPoint);
      this.form = {};
      this.setupPointDialog = false;
      
    },
    confirmSiteChanges(val){
      if (this.pointsType == '起点') {
        this.routerForm.startPoint = val;
      } else {
        this.routerForm.endPoint = val;
      }
      this.setupPointDialog = false;
    },
    addRouters(){
      const formData =new FormData();
      formData.append('lineCreateReq',JSON.stringify(this.routerForm));
      this.$axios.post("/protect-api/pointLine/createPointLine", formData).then((data) => {
        console.log(data);
        if (data.data.code === '0000') {
          this.$message.success('添加成功');
          this.addRouter = false;
          this.getRouterList();
        } else {
          this.$message.error('添加失败: ' + data.data.message);
        }
        
      });
    }


  },
  
};
</script>




<style lang="less" scoped>
#allmap {
  width: 100%;
  height: 100%;
  overflow: hidden;
  z-index: 1;
  font-family: "微软雅黑";
}

.numberStyle {
  color: red;
}

.fixed-search-box {
  width: 100%;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 2;
  box-shadow: 0 2Px 10Px 0 rgb(0 0 0 / 10%);
}

.header-long {
  position: fixed;
  width: 100%;
  height: 70Px;
  line-height: 70Px;
  z-index: 2;
  background-image: url('../assets/img_slices/bg.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
}

.header-right-middle {
  position: fixed;
  width: 100%;
  height: 63Px;
  text-align: center;
  // background-image: url('../assets/zxx/middleText2.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
  font-size: 36Px;
  font-weight: 600;
  font-family: Alimama ShuHeiTi;
  color: #DAFFFC;
}

.header-right-middle-span {
  width: 356Px;
  height: 34Px;
  font-size: 36Px;
  font-family: Alimama ShuHeiTi;
  font-weight: bold;
  color: #C6F6FF;
  line-height: 50Px;
  opacity: 0.89;
  text-shadow: 0Px 4Px 6Px rgba(7, 46, 26, 0.7);

  background: linear-gradient(0deg, rgba(119, 255, 253, 0.45) 0%, rgba(233, 248, 255, 0.45) 73.3154296875%, rgba(255, 255, 255, 0.45) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;

}

.imgLT {
  background-image: url('../assets/zxx/lt.png');
  background-repeat: no-repeat;
  background-size: 81% 81%;
  width: 84Px;
  height: 46Px;
  margin: 4Px 10Px;
}

.dateSpan {
  margin-left: 1740Px;
  margin-top: 12Px;
  position: absolute;
}

.date {
  text-align: left;
  width: 200Px;
  line-height: 14Px;
  color: #07697f;
  font-size: 14Px;
}

.buttonZ1 {
  z-index: 2;
  position: fixed;
  margin-left: 678Px;
  margin-top: 88Px;
  width: 126Px;
  height: 38Px;
  box-shadow: 0 0 2Px #107093;
  text-align: center;
  background-image: url('../assets/zxx/button2.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
}

.buttonZ2 {
  z-index: 2;
  position: fixed;
  margin-left: 819Px;
  margin-top: 88Px;
  width: 126Px;
  box-shadow: 0 0 2Px #107093;
  height: 38Px;
  text-align: center;
  background-image: url('../assets/zxx/button2.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
}

.buttonZ3 {
  z-index: 2;
  position: fixed;
  margin-left: 959Px;
  margin-top: 88Px;
  width: 136Px;
  height: 38Px;
  box-shadow: 0 0 2Px #107093;
  text-align: center;
  background-image: url('../assets/zxx/button2.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
}

.buttonZ4 {
  z-index: 2;
  position: fixed;
  margin-left: 1109Px;
  margin-top: 88Px;
  box-shadow: 0 0 2Px #107093;
  width: 136Px;
  height: 38Px;
  text-align: center;
  background-image: url('../assets/zxx/button2.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
}

.buttonSpanCur {
  z-index: 2;
  width: 82Px;
  cursor:pointer;
  height: 19Px;
  font-size: 20Px;
  font-family: Alimama ShuHeiTi;
  font-weight: bold;
  color: #DAFFFC;
  line-height: 35Px;
  /*  background: linear-gradient(0deg, rgba(239,251,255,0.34) 0%, rgba(0,0,0,0.34) 11.9140625%, rgba(255,254,254,0.34) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;*/
}

.buttonSpan {
  cursor:pointer;
  width: 82Px;
  height: 19Px;
  font-size: 20Px;
  font-family: Alimama ShuHeiTi;
  font-weight: bold;
  color: #8DE8EE;
  line-height: 35Px;
  opacity: 0.85;
}

.mapLeftTop1 {
  z-index: 2;
  position: fixed;
  margin-left: 32Px;
  margin-top: 191Px;
  width: 420Px;
  height: 180Px;
  background-image: url('../assets/zxx/mapLeftTop3.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
}

/*滚动条样式*/
.mapLeftTop1div::-webkit-scrollbar {
  width: 4px;
  /*height: 4px;*/
}

.mapLeftTop1div::-webkit-scrollbar-thumb {
  border-radius: 10px;
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  background: rgba(0, 0, 0, 0.2);
}

.mapLeftTop1div::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  border-radius: 0;
  background: rgba(0, 0, 0, 0.1);
}

.mapLeftTopTitle {
  margin-left: 43Px;
  margin-top: 99Px;
  width: 95Px;
  height: 23Px;
  font-size: 18Px;
  font-family: Alimama ShuHeiTi;
  font-weight: bold;
  color: #FFFFFF;
  line-height: 44Px;
  text-shadow: 0Px 2Px 8Px rgba(14, 197, 236, 0.36);
}

.mapRightTopTitle {
  z-index: 2;
  width: 18Px;
  height: 18Px;
  line-height: 44Px;
  margin: 13Px 13Px;
  float: right;
  background-image: url('../assets/zxx/reset3.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
  display: inline-block;
  transition: all 0.3s ease;
}

.mapRightTopTitle:hover {
  transform: rotate(360deg) scale(2.0);

  -webkit-transform: rotate(360deg) scale(2.0);

  -moz-transform: rotate(360deg) scale(2.0);

  -o-transform: rotate(360deg) scale(2.0);

  -ms-transform: rotate(360deg) scale(2.0);
}

.mapRightTopTitle:active {
  transform: scale(0.8);
}

.mapLeftTop1div {
  margin-left: 16Px;
  margin-top: 25Px;
  width: 388Px;
  height: 97Px;
  overflow: auto;
  z-index: 1;
  background: #0C3038;
  display: inline-block;
  opacity: 0.8;
}

.mapLeftTop1Radis {
  width: 8Px;
  height: 8Px;
  margin-left: 16Px;
  background: #39D4CD;
  border-radius: 50%;
  display: inline-block;
}

.mapLeftTopName {
  width: 100%;
  // height: 100px;
  font-size: 15Px;
  font-family: Source Han Sans CN;
  font-weight: 500;
  color: #39D4CD;
  line-height: 35Px;
  margin-left: 7Px;
}

.mapLeftTopText {
  width: 335Px;
  height: 50Px;
  font-size: 14Px;
  font-family: Source Han Sans CN;
  font-weight: 400;
  color: #FFFFFF;
  line-height: 20Px;
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
}

.mapLeftTop2 {
  z-index: 2;
  position: fixed;
  float: left;
  margin-left: 32Px;
  margin-top: 397Px;
  width: 420Px;
  height: 390Px;
  /*  height: 282Px;*/
  background-image: url('../assets/zxx/mapLeftTop23.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
  display: inline-block;
}

.mapLeftTop2Img1 {
  margin-top: 17Px;
  margin-left: 16Px;
  width: 66Px;
  height: 66Px;
  background-image: url('../assets/zxx/mapLeftTop2Img1.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
  display: inline-block;
}

.mapLeftTop2Img2 {
  margin-top: 17Px;
  margin-left: 16Px;
  width: 66Px;
  height: 66Px;
  background-image: url('../assets/zxx/mapLeftTop2Img2.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
  display: inline-block;
}

.mapLeftTop2Text {
  margin-top: 41Px;
  margin-left: 45Px;
  width: 75Px;
  font-size: 16Px;
  font-family: Source Han Sans CN;
  font-weight: 400;
  color: #FFFFFF;
  position: absolute;
  display: inline-block;
}

.mapLeftTop2Text2 {
  position: absolute;
  margin-left: 200Px;
  margin-top: 27Px;
  width: 62Px;
  height: 27Px;
  font-size: 28Px;
  font-family: D-DIN-PRO;
  font-weight: 500;
  color: #FABB61;
  line-height: 50Px;
  display: inline-block;
}

.mapLeftTop2Text3 {
  position: absolute;
  width: 62Px;
  height: 27Px;
  font-size: 28Px;
  font-family: D-DIN-PRO;
  font-weight: 500;
  color: #FABB61;
  line-height: 50Px;
  display: inline-block;
}

.mapLeftTop2Div {
  width: 388Px;
  height: 97Px;
  z-index: 1;
  background: #0C3038;
  display: inline-block;
  opacity: 0.8;
}

.mapLeftTop3 {
  z-index: 2;
  position: fixed;
  margin-left: 32Px;
  margin-top: 812Px;
  width: 420Px;
  height: 175Px;
  background-image: url('../assets/zxx/mapLeftTop3.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
}

.mapLeftTop2Img3 {
  margin-top: 17Px;
  margin-left: 16Px;
  width: 66Px;
  height: 66Px;
  background-image: url('../assets/zxx/mapLeftTop2Img3.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
  display: inline-block;
}

.mapLeftTop2Text2Top {
  width: 140Px;
  height: 24Px;
  font-size: 28Px;
  font-family: D-DIN-PRO;
  font-weight: 500;
  text-align: center;
  /*  color: #2DC3BA;*/
  position: absolute;
}

.mapLeftTop2Text2Bottom {
  width: 140Px;
  height: 38Px;
  font-size: 14Px;
  text-align: center;
  font-family: Source Han Sans CN;
  font-weight: 400;
  color: #FFFFFF;
  position: absolute;
}

.mapRightTop1 {
  z-index: 2;
  position: fixed;
  margin-left: 1473Px;
  margin-top: 617Px;
  width: 420Px;
  height: 175Px;
  background-image: url('../assets/zxx/mapLeftTop3.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
}

.mapRightTop2 {
  z-index: 2;
  position: fixed;
  margin-left: 1473Px;
  margin-top: 815Px;
  width: 420Px;
  height: 175Px;
  background-image: url('../assets/zxx/mapLeftTop3.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
}

.mapRightTop3 {
  z-index: 2;
  position: fixed;
  margin-left: 1473Px;
  margin-top: 180Px;
  width: 420Px;
  height: 420Px;
  background-image: url('../assets/zxx/glbg.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
}


.mapLeftTopTitleDetalis {
  margin-left: 4Px;
  margin-top: 15Px;
  width: 17Px;
  height: 16Px;
  background-image: url('../assets/zxx/details.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
  display: inline-block;
  position: absolute;
}

/deep/.BMap_bubble_pop {
  background-color: transparent;
}
.mapLeftTopz {
  z-index: 3;
  position: fixed;
  margin-left: 32Px;
  margin-top: 191Px;
  width: 420Px;
  height: 50Px;
}
.left {
  z-index: 11;
  width: 20Px;
  height: 20Px;
  background-image: url('../assets/zxx/details.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
  position: relative;
  float: left;
  margin: 12Px 12Px;
}

.right {
  z-index: 11;
  width: 20Px;
  height: 20Px;
  background-image: url('../assets/zxx/left.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
  position: relative;
  float: right;
  margin: 12Px 12Px;
}

.topologyBackground {
  width: 100%;
  height: 100%;
  z-index: 1;
  width: 1920Px;
  height: 1080Px;
  position: relative;
  background-image: url('../assets/zxx/topologyBack.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
}

.topology {
  z-index: 1;
  width: 100%;
  // margin: 0 auto;
  height: 100%;
  display: flex;
  justify-content: center;
  // background-image: url('../assets/zxx/topology.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
  // position: absolute;

}

.item-list {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
}
.item-list2 {
  width: 100%;
  height: 100%;
  position: relative;
  top: 0;
}

.item {
  position: relative;
  display: flex;
  align-items: center;
  flex-direction: column;
  padding-right: .2rem;
  // color: #333;
  // line-height: .17rem;
  overflow: hidden;
  width: 100%;
  height: 7.35rem;
  // width: 388Px;
  // height: 97Px;
  z-index: 1;
  background: #0C3038;
  display: inline-block;
  opacity: 0.8;

  .mapLeftTop1Radis {
    width: 8Px;
    height: 8Px;
    margin-left: 16Px;
    background: rgba(252, 164, 58, 1);
    border-radius: 50%;
    display: inline-block;
  }

  .mapLeftTop1Text {
    font-size: 16px;
    font-family: Source Han Sans CN;
    font-weight: 400;
    color: #FFFFFF;
    // line-height: 50px;
  }

  .mapLeftTopText {
    font-size: 14px;
    font-family: Source Han Sans CN;
    font-weight: 400;
    color: #FFFFFF;
  }

}

.fix_content {
  position: absolute;
  width: 98%;
  height: 310px;
  overflow: hidden;
  top: 53px;
  left: 0;
}

.alog {
  width: 100%;
  height: 53%;
}

/* 定义滚动条样式 */
/deep/.contont::-webkit-scrollbar {
  width: 3px;
  height: 3px;
  background-color: rgba(240, 240, 240, 1);
}

/deep/.contont::-webkit-scrollbar:hover {
  width: 6px !important;
}

/*定义滚动条轨道 内阴影+圆角*/
/deep/.contont::-webkit-scrollbar-track {
  box-shadow: inset 0 0 0px rgba(240, 240, 240, .5);
  border-radius: 10px;
  background-color: rgba(240, 240, 240, .5);
}

/deep/.contont:hover::-webkit-scrollbar-corner {
  width: 6px;
  border: 5px solid #06363b;
  background-color: #06363b;
  /*background-color: red !important;*/
}

/*定义滑块 内阴影+圆角*/
/deep/.contont::-webkit-scrollbar-thumb {
  border-radius: 10px;
  box-shadow: inset 0 0 0px #317179;
  background-color: #317179;
}

/deep/.contont::-webkit-scrollbar-thumb:hover {
  border: 5px solid #06363b;
  background-color: #06363b;
}

.mapRightTop1Img1 {
  margin-top: 17Px;
  margin-left: 16Px;
  width: 66Px;
  height: 66Px;
  background-image: url('../assets/zxx/mapRightTop1Img1.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
  display: inline-block;
}

.mapRightTop1Img2 {
  margin-top: 17Px;
  margin-left: 16Px;
  width: 66Px;
  height: 66Px;
  background-image: url('../assets/zxx/mapRightTop1Img2.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
  display: inline-block;
}

.mapRightTop1Img3 {
  margin-top: 17Px;
  margin-left: 147Px;
  width: 66Px;
  height: 66Px;
  background-image: url('../assets/zxx/mapRightTop1Img3.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
  display: inline-block;
}

/deep/.stDataDiv1 {
  width: 112px;
  height: 40px;
  background: linear-gradient(-51deg, #035fd5 0%, #4FA1FF 100%);
  border: 2px solid;
  border-image: linear-gradient(-55deg, rgba(145, 202, 254, 0.5), rgba(59, 134, 220, 0.5)) 2 2;
  opacity: 0.9;
  text-align: center;
}

/deep/.stDataDiv2 {
  width: 112px;
  height: 40px;
  background: linear-gradient(-51deg, #306747 0%, #47F195 100%);
  border: 2px solid;
  border-image: linear-gradient(-55deg, rgba(133, 255, 189, 0.5), rgba(90, 219, 149, 0.5)) 2 2;
  opacity: 0.9;
  text-align: center;
}

/deep/.stDataDiv3 {
  width: 141px;
  height: 40px;
  display: inline-block;
  background: linear-gradient(-51deg, #734c1c 0%, #ECB26A 100%);
  border: 2px solid;
  border-image: linear-gradient(-55deg, rgba(253, 215, 169, 0.5), rgba(255, 172, 71, 0.5)) 2 2;
  opacity: 0.9;
  text-align: center;
}

/deep/.stDataDiv4 {
  width: 130px;
  height: 40px;
  background: linear-gradient(-51deg, #035fd5 0%, #4FA1FF 100%);
  border-image: linear-gradient(-55deg, rgba(145, 202, 254, 0.5), rgba(59, 134, 220, 0.5)) 2 2;
  opacity: 0.9;
  text-align: center;
  border-radius: 20px;
}

/deep/.stDataDiv5 {
  width: 112px;
  height: 40px;
  background: linear-gradient(-51deg, #035fd5 0%, #1668c4 100%);
  border: 2px solid;
  border-image: linear-gradient(-55deg, rgba(145, 202, 254, 0.5), rgba(59, 134, 220, 0.5)) 2 2;
  opacity: 0.9;
  text-align: center;
}

/deep/.stDataDiv6 {
  width: 141px;
  height: 40px;
  display: inline-block;
  background: linear-gradient(-51deg, #734c1c 0%, #ec9a37 100%);
  border: 2px solid;
  border-image: linear-gradient(-55deg, rgba(253, 215, 169, 0.5), rgba(255, 172, 71, 0.5)) 2 2;
  opacity: 0.9;
  text-align: center;
}

/deep/.stDataSpan1 {
  width: 91px;
  height: 18px;
  font-size: 16px;
  font-family: Source Han Sans CN;
  font-weight: 500;
  color: #091220;
  display: inline-block;
  line-height: 40px;
}

/deep/.stDataSpan4 {
  width: 91px;
  height: 18px;
  font-size: 16px;
  font-family: Source Han Sans CN;
  font-weight: 500;
  background: transparent;
  color: #55A4FE;
  display: inline-block;
  line-height: 40px;
}
.noticePopover{
  z-index: 6;
  margin-left: 560Px;
  margin-top: -730Px;
  position: absolute;
  width: 800Px;
  height: 312px;
  background-image: url('../assets/zxx/noticePopover.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
}
.alarmPopover{
  z-index: 6;
  margin-left: 500Px;
  margin-top: -730Px;
  position: absolute;
  width: 929Px;
  height: 450Px;
  background-image: url('../assets/zxx/alarm.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
}
.noticePopoverTitle{
  position: relative;
  margin-top: 17Px;
  margin-left: 35Px;
  width: 198Px;
  height: 19Px;
  font-size: 18Px;
  font-family: Alimama ShuHeiTi;
  font-weight: bold;
  color: #FFFFFF;
  line-height: 50Px;
}
.alarmPopoverTitle{
  position: relative;
  margin-top:7Px;
  margin-left: 35Px;
  width: 900Px;
  height: 19Px;
  font-size: 18Px;
  font-family: Alimama ShuHeiTi;
  font-weight: bold;
  color: #FFFFFF;
  line-height: 50Px;
}
.alarmPopoverText4{
  position: relative;
  text-align: center;
  width: 900Px;
  font-size: 16Px;
  margin-top:25Px;
  margin-left: 18Px;
  font-family: Source Han Sans CN;
  font-weight: 400;
  color: #FFFFFF;
  height: 24Px;
  line-height: 24Px;
}
.alarmPopoverX{
  z-index: 6;
  position: fixed;
  margin-top: 0Px;
  margin-left: 883Px;
  width: 14Px;
  height: 14Px;
  cursor:pointer;
  background-image: url('../assets/zxx/X.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
}
.noticePopoverX{
  z-index: 6;
  position: fixed;
  margin-top: 0Px;
  margin-left: 750Px;
  width: 14Px;
  height: 14Px;
  cursor:pointer;
  background-image: url('../assets/zxx/X.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
}
.noticePopoverText{
  position: relative;
  margin-top: 10Px;
  margin-left: 30Px;
  width: 757Px;
  min-height: 88Px;
  max-height: 190Px;
  font-size: 16Px;
  font-family: Source Han Sans CN;
  font-weight: 400;
  color: #FFFFFF;
  line-height: 24Px;
  overflow-y:auto ;
}
.noticePopoverText2{
  position: relative;
/*  margin-top: 12Px;*/
  text-align: right;
  font-size: 16Px;
  width: 770Px;
  height: 24Px;
  font-family: Source Han Sans CN;
  font-weight: 400;
  color: #FFFFFF;
  line-height: 24Px;
}
.noticePopoverText3{
  position: relative;
  text-align: right;
  width: 770Px;
  font-size: 16Px;
  font-family: Source Han Sans CN;
  font-weight: 400;
  color: #FFFFFF;
  height: 24Px;
  line-height: 24Px;
}
.noticePopoverText4{
  position: relative;
  text-align: center;
  width: 770Px;
  font-size: 16Px;
  margin-top: 46Px;
  font-family: Source Han Sans CN;
  font-weight: 400;
  color: #FFFFFF;
  height: 24Px;
  line-height: 24Px;
}
/* 定义滚动条样式 */
/deep/.noticePopoverText::-webkit-scrollbar {
  width: 3px;
  height: 3px;
  background-color: rgba(240, 240, 240, 1);
}

/deep/.noticePopoverText::-webkit-scrollbar:hover {
  width: 6px !important;
}

/*定义滚动条轨道 内阴影+圆角*/
/deep/.noticePopoverText::-webkit-scrollbar-track {
  box-shadow: inset 0 0 0px rgba(240, 240, 240, .5);
  border-radius: 10px;
  background-color: rgba(240, 240, 240, .5);
}

/deep/.noticePopoverText:hover::-webkit-scrollbar-corner {
  width: 6px;
  border: 5px solid #06363b;
  background-color: #06363b;
  /*background-color: red !important;*/
}

/*定义滑块 内阴影+圆角*/
/deep/.noticePopoverText::-webkit-scrollbar-thumb {
  border-radius: 10px;
  box-shadow: inset 0 0 0px #317179;
  background-color: #317179;
}

/deep/.noticePopoverText::-webkit-scrollbar-thumb:hover {
  border: 5px solid #1f5c65;
  background-color: #1f5c65;
}

.el-button.is-plain:focus, .el-button.is-plain:hover {
  // width: 58px;
  height: 32Px;
  font-size: 14Px;
  font-family: Source Han Sans CN;
  font-weight: 400;
  color: #2AB8B7;
  line-height: 29Px;
  padding: 0px;
  padding-left: 10px;
  padding-right: 10px;
  /*      padding-top: 10px;*/
  padding-bottom: 10px;
  border: 1px solid #1a9695;
  background-color: #038793;
  box-shadow: 0 0 5px #1a9695;
  color: #fff;
}
.route-selector-tip {
  position: absolute;
  top: 100px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 99;
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 8px 16px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  font-size: 14px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

/* 线路命名输入框样式 */
.route-naming {
  // position: absolute;
  // top: 100px;
  // left: 50%;
  // transform: translateX(-50%);
  // z-index: 99;
  background-color: rgba(0, 0, 0, 0.7);
  padding: 8px 16px;
  border-radius: 4px;
  // display: flex;
  align-items: center;
  gap: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  /deep/.el-form-item--mini .el-form-item__label {
    color: #fff !important;
  }
  .el-input {
    width: 60%;
  }
}
.search-results-container {
  position: absolute;
  top: 50px;
  left: 20px;
  width: 300px;
  max-height: 500px;
  background-color: #fff;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  z-index: 100;
  display: flex;
  flex-direction: column;
}

.search-results-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  border-bottom: 1px solid #f5f7fa;
  background-color: #fafafa;
  border-top-left-radius: 4px;
  border-top-right-radius: 4px;
}

.search-results-header span {
  font-weight: bold;
  color: #303133;
}

.search-results-content {
  flex: 1;
  overflow-y: auto;
  padding: 10px 0;
}

.search-loading,
.search-error,
.search-no-results {
  padding: 15px;
  text-align: center;
  color: #606266;
  font-size: 14px;
}

.search-results-list {
  padding: 0 10px;
}

.search-result-item {
  padding: 10px 15px;
  cursor: pointer;
  border-radius: 4px;
  margin-bottom: 5px;
  transition: background-color 0.2s;
}

.search-result-item:hover,
.search-result-item.active {
  background-color: #f5f7fa;
}

.result-title {
  font-weight: bold;
  color: #606266;
  margin-bottom: 5px;
  display: flex;
  align-items: center;
}

.result-address {
  font-size: 12px;
  color: #606266;
  margin-bottom: 3px;
  display: flex;
  align-items: center;
}

.result-coordinates {
  font-size: 12px;
  color: #909399;
}

.search-results-footer {
  padding: 8px 15px;
  border-top: 1px solid #f5f7fa;
  background-color: #fafafa;
  border-bottom-left-radius: 4px;
  border-bottom-right-radius: 4px;
  font-size: 12px;
  color: #909399;
  text-align: center;
}
.drawing-panel {
            z-index: 999;
            position: fixed;
            bottom: 3.5rem;
            margin-left: 2.5rem;
            padding-left: 0;
            border-radius: .25rem;
            height: 47Px;
            box-shadow: 0 2px 6px 0 rgba(27, 142, 236, 0.5);
        }
        .bmap-btn {
            border-right: 1px solid #d2d2d2;
            float: left;
            width: 64Px;
            height: 100%;
            background-image: url(//api.map.baidu.com/library/DrawingManager/1.4/src/bg_drawing_tool.png);
            cursor: pointer;
        }
        .drawing-panel .bmap-marker {
            background-position: -65Px 0;
        }
        .drawing-panel .bmap-polyline {
            background-position: -195Px 0;
        }
        .drawing-panel .bmap-rectangle {
            background-position: -325Px 0;
        }
        .drawing-panel .bmap-polygon {
            background-position: -260Px 0;
        }
        .drawing-panel .bmap-circle {
            background-position: -130Px 0;
        }
        .el-select{
          width: 100%;
        }
        .upload-preview {
  margin-top: 10px;
  position: relative;
  display: inline-block;
}

.thumbnail-image {
  width: 80px;
  height: 80px;
  object-fit: cover;
  border-radius: 4px;
  cursor: pointer;
  transition: transform 0.2s;
  box-shadow: 0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24);
}

.thumbnail-image:hover {
  transform: scale(1.05);
  box-shadow: 0 3px 6px rgba(0,0,0,0.16), 0 3px 6px rgba(0,0,0,0.23);
}

.remove-btn {
  position: absolute;
  top: -8px;
  right: -8px;
  opacity: 0.8;
}

.remove-btn:hover {
  opacity: 1;
}

.upload-progress {
  margin-top: 8px;
  width: 100%;
}
</style>
