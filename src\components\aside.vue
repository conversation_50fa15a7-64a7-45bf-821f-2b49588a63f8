<template>
  <div id="aside" class="aside">
    <el-scrollbar style="height: 100%">
      <!-- <el-menu
        :default-active="this.$route.path"
        class="el-menu-vertical-demo"
        :collapse="isCollapse"
        text-color="#333333"
        :router="true"
        active-text-color="#fff"
      >
        <div
          v-for="(item, index) in asideList"
          :key="index"
          class="item"
        >
          <el-submenu
            v-if="item.children && item.children.length > 0"
            :index="item.path"
            :route="{ path: item.path }"
          >
            <template slot="title">
              <span class="span_p">{{ item.name }}</span>
            </template>
            <el-menu-item
              v-for="children in item.children"
              :key="children.name"
              :index="children.path"
              :route="{ path: children.path }"
            >
              <template slot="title" class="span_p">
                <span
                  class="span_p"
                  :route="{ path: children.path }"
                  :index="children.path"
                  >{{ children.name }}</span
                >
              </template>
            </el-menu-item>
          </el-submenu>

          <el-menu-item
            v-else
            :ref="item.path"
            :index="item.path"
            :route="{ path: item.path }"
            :class="className"
            @click="changeActive(item.name, item.path)"
          >
            <span class="span_p"> {{ item.name }}</span>
          </el-menu-item>
        </div>
      </el-menu> -->

      <el-menu :default-active="this.$route.path" class="el-menu-vertical-demo" text-color="#333333"
        active-text-color="#fff" :collapse-transition="false" :unique-opened="true">
        <div v-for="(item, index) in asideList" :key="index" class="item">
          <el-submenu v-if="item.children && item.children.length > 0" :index="item.menuId + ''">
            <template slot="title">
              <span>{{ item.name }}</span>
            </template>
            <div v-for="(items, indexs) in asideList[index].children" :key="indexs" class="itemChildren">
              <el-submenu v-if="items.children && items.children.length > 0" :index="items.menuId + ''"
                @click="jumpMenu(items)">
                <template slot="title">
                  <span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;{{ items.name }}</span>
                </template>
                <el-menu-item v-for="(i, indexd) in items.children" :key="indexd" :index="i.menuId + ''"
                  @click="jumpMenu(i)">
                  <span>{{ i.name }}</span>
                </el-menu-item>
              </el-submenu>
              <el-menu-item v-else :index="items.menuId + ''" @click="jumpMenu(items)">
                <span>{{ items.name }}</span>
              </el-menu-item>
            </div>
          </el-submenu>
          <el-menu-item v-else :index="index + '1'" @click="jumpMenu(item)">
            <span>{{ item.name }}</span>
          </el-menu-item>
        </div>
      </el-menu>
    </el-scrollbar>
  </div>
</template>

<script>
import { Auth, qryPermisionInfoData } from "@/apis/api-login";
import { Notification } from "element-ui";
import { mapState } from "vuex";
import bus from "@/eventBus/bus.js";
export default {
  name: "",
  components: {},
  props: {},
  data() {
    return {
      component: "User",
      pmenu: [],
      className: "",
      isCollapse: false,
      asideList: {},
      perms: [],
      length: 0,
      // isCollapse: true,
    };
  },
  computed: {
    ...mapState(["pers"]),
  },
  watch: {
    // 监测路由变化,只要变化了就调用获取路由参数方法将数据存储本组件即可
    $route: "onRouteChanged",
  },
  created() {
    bus.$on("length", (msg) => {
      console.log(msg);
      this.length = msg;
    });
    this.onRouteChanged();
    this.getqryPermisionInfoData();

  },
  mounted() {
    // this.usid = sessionStorage.getItem("usid");
    // this.getAccess({
    //   sessionId: this.usid,
    // });
    this.index = this.$route.path;
  },
  activited() { },
  update() { },
  beforeRouteUpdate() { },
  methods: {
    jumpMenu(item) {

      if (item.children.length == 0) {
        // console.log(item);
        bus.$emit("routerTag", item);

        // console.log(this.length);
        // this.length = msg;
        // if (this.length > 8) {

          // return;
        // } else {


          if (item.jumpType == "0") {
            this.$router.push({
              path: item.path,
              // params: { iframeData: item },
            });
            // this.$router.push(item.path);
          } else if (item.jumpType == "1") {
            // // this.$router.replace("/externalSystems");
            // this.$router.push({
            //   name: "externalSystemsTy",
            //   params: { iframeData: item },
            // });
            sessionStorage.setItem("path", item.path);
            let tokenValue = sessionStorage.getItem("tokenValue");
            const aaa = sessionStorage.getItem('path') + '?' + 'token' + '=' + tokenValue + '?' + 'refresh_token' + '=' + tokenValue;
            sessionStorage.setItem("path", aaa);

            this.$router.push({
              path: "externalSystemsTy" + item.menuId,
              // query: { token: tokenValue, refresh_token: tokenValue },
              // params: { iframeData: item },
            });
          } else {
            let routeData = this.$router.resolve({
              path: item.path,
            });
            window.open(routeData.href, "_blank");
          }
        // }



      }
    },

    onRouteChanged() {
      //渲染导航选中方法，子菜单打开菜单选中显示父菜单
      let that = this;
    },
    changeActive(name, path) {
      // // this.$refs.menuItem+path
      // if (path == "customerManage") {
      //   let div = this.$refs.customerManage;
      //   // 使用方式
      //   this.addClass(div, "active");
      // }
    },
    hasClass(el, className) {
      let reg = new RegExp("(^|\\s)" + className + "(\\s|$)");
      return reg.test(el.className);
    },
    addClass(el, className) {
      if (this.hasClass(el, className)) {
        return false;
      }
      // 通过空格将原有类名字符串组装成数组
      // let newClass = el.className.split(" ");
      // newClass.push(className);
      // 添加完毕后 通过空格将数组组装为字符串
      el.className = className;
    },
    async getAccess(requestData) {
      const _this = this;
      const result = await Auth(requestData);
      // console.log(result, _this.pmenu, result.data.data);
      if (result.data.success && result.data.data != null) {
        try {
          // this.saveUserInfo(result, requestData);
        } catch (error) {
          // _this.$notify({
          //     title: "提示",
          //     message: "服务器错误",
          //     type: "error",
          // });
          Notification({
            title: "提示",
            message: error,
            type: "error",
          });
        }
        _this.pmenu = result.data.data.permlist;
        // console.log(_this.pmenu);
      } else {
        // _this.$notify({
        //     title: "授权",
        //     type: "error",
        //     message: result.error_msg,
        //     duration: 2500,
        // });
        Notification({
          title: "提示",
          message: "请重新登录",
          type: "error",
        });
        this.$router.push({ path: "/login", replace: true });
      }
    },

    handleOpen(key, keyPath) { },
    handleClose(key, keyPath) { },
    handleSelect(key, keyPath) { },
    async getqryPermisionInfoData() {
      const res = await qryPermisionInfoData({});
      this.asideList = res.data.data.menus;
      this.perms = res.data.data.perms;
      this.$store.dispatch("pers", this.perms);
      sessionStorage.setItem("cityId", res.data.data.userInfo.cityId);
      sessionStorage.setItem("cityName", res.data.data.userInfo.cityName);
      sessionStorage.setItem("userGrade", res.data.data.userInfo.userGrade);
      sessionStorage.setItem("userType", res.data.data.userInfo.userType);


      // this.$store.dispatch("menus", res.data.data.menus);
      // this.asideList = this.$store.state.menus;
      // this.asideList.unshift({
      //   path: "/DataSys/platHome",
      //   name: "平台首页",
      //   component: "platHome",
      //   meta: {
      //     // requireAuth: true,
      //     requireAuth: false,
      //     title: "平台首页",
      //   },
      //   redirect: "#",

      //   children: [],

      //   menuType: "M",
      // });
      //获取当前router
      const router = this.$router;

      //获取路由数组
      const routerArray = router.options.routes;

      //获取路由数组中本画面路由对象
      const no = routerArray.findIndex((item) => {
        return item.name == "o域管理";
      });
      //获取路由数组中本画面路由对象
      const number = routerArray[no].children.findIndex((items) => {
        return items.name == "externalSystemsTy";
      });
      // console.log(routerArray[no].children[number].meta);
      // //获取路由数组中本画面路由对象
      // const noList = this.asideList.findIndex((item) => {
      //   return item.jumpType == "1";
      // });
      // console.log(this.asideList[noList].children);
      // //获取路由数组中本画面路由对象
      // const numberList = this.asideList[noList].children.findIndex((items) => {
      //   return items.name == "externalSystemsTy";
      // });
      // console.log(this.asideList[noList].children[numberList].meta);
      // // routerArray[no].children[number].meta.name =
    },
  },
  filter: {},
};
</script>
<!-- <style src="../style/index.scss" /> -->
<style  lang='scss' scoped>
.el-menu-vertical-demo:not(.el-menu--collapse) {
  width: 200px;
  min-height: 400px;
}

.aside .el-scrollbar__wrap {
  background-color: transparent !important;
  overflow-x: hidden !important;
}

.aside .span_p {
  color: #000;
}

/deep/.el-submenu__title {
  padding: 0;
  text-align: left;
  padding-left: 44px !important;
  font-size: 16px;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: #ffffff;
}

/deep/.el-menu-item-group__title {
  padding-left: 0px !important;
}

/deep/.el-menu-item-group__title {
  text-decoration: none !important;
  // height: 48px;
  line-height: 30px;
}

/deep/.el-menu-item-group__title:hover {
  background: rgba(24, 144, 255, 0.1);
  // opacity: 0.1;
  color: #1890ff;
}

/deep/a:hover {
  text-decoration: none !important;
  color: #1890ff !important;
}

/deep/a {
  text-decoration: none !important;
  color: #333333;
}

/deep/.el-submenu__title:hover {
  background: #fe7d17;
}

/deep/.el-menu-item:focus,
.el-menu-item:hover {
  background: #fe7d17;
}

/deep/.el-menu {
  background-color: transparent;
}

.aside .el-menu-item.is-active {
  color: rgb(0, 112, 255) !important;
  background-color: #fe7d17;
  border-right: 3px solid #fe7d17 !important;
}

.active {
  color: rgb(0, 112, 255);
  background-color: rgba(229, 235, 244, 1);
  border-right: 1px solid rgba(0, 112, 255, 1);
}

.aside {
  // height: calc(100% - 50px);
  width: 210px;
  min-width: 210px;
  max-width: 210px;
  background: #001529;
  color: #fff;
  box-shadow: 0 0 8px 3px rgba(6, 0, 1, 0.02);
  border: none;
  position: relative;

  // text-align: left;
  .itemChildren {
    background: #000d17;
  }

  .item {
    background: #001529;

    span {
      font-size: 16px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #ffffff;
    }
  }
}
</style>

<style lang="sass" scoped>
</style>
