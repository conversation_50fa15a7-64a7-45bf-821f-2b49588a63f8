export function UPFimg () {
   var UPFimg = 'image://data:image/png;base64,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'

    return UPFimg;
}
var demoSvg = 'path://M24.0153876,21.0381143 L24.0102536,21.0381143 C22.6548973,21.0381143 21.2944071,20.7131394 20.0519971,20.0631896 L9.72253925,47 L14.6151702,47 L15.4468661,44.8335005 L23.9281108,41.3980513 L32.5017662,44.8077088 L33.34373,47 L38.2363609,47 L27.906903,20.0683479 C26.6952966,20.7131394 25.3553421,21.0381143 24.0153876,21.0381143 Z M23.9794501,22.5804557 L25.4631545,26.4543631 L22.4957456,26.4543631 L23.9794501,22.5804557 Z M21.5151659,29.0025792 L26.4386003,29.0025792 L27.0906089,30.6996704 L24.0615929,32.4586617 L20.9247645,30.5449205 L21.5151659,29.0025792 L21.5151659,29.0025792 Z M20.0006579,32.9590199 L21.5562373,33.9133114 L19.0816852,35.3473277 L20.0006579,32.9590199 Z M31.2644902,41.5734346 L24.3901641,38.8395186 L23.4506558,38.8446769 L16.6944099,41.5785929 L17.6339183,39.1283852 L24.0256554,35.4195443 L30.3917229,39.3089268 L31.2644902,41.5734346 Z M28.9028845,35.414386 L26.5361449,33.9648947 L28.0198494,33.1034532 L28.9028845,35.414386 L28.9028845,35.414386 Z M12.5616,19.3152314 C13.0493229,19.3152314 13.5370458,19.1295315 13.9066884,18.7581315 C14.6511076,18.0101734 14.6511076,16.8031237 13.9066884,16.0551655 C12.9466444,15.0853991 12.4691893,13.8370827 12.4691893,12.568133 C12.4691893,11.2991833 12.9466444,10.0508669 13.9066884,9.08110044 C14.6511076,8.33314228 14.6511076,7.12609256 13.9066884,6.3781344 C13.1622693,5.63017624 11.9609307,5.63017624 11.2165115,6.3781344 C9.51718223,8.08038401 8.65981669,10.3345752 8.6649277,12.568133 C8.6649277,14.8016908 9.51718223,17.0558819 11.2165115,18.7581315 C11.5861542,19.1295315 12.0738771,19.3152314 12.5616,19.3152314 L12.5616,19.3152314 Z M34.098417,6.37297607 C33.3539979,7.12093423 33.3539979,8.32798395 34.098417,9.07594211 C35.0584611,10.0457086 35.5359161,11.2940249 35.5359161,12.5629746 C35.5359161,13.8319243 35.0584611,15.0802407 34.098417,16.0500072 C33.3539979,16.7979653 33.3539979,18.005015 34.098417,18.7529732 C34.4680597,19.1243731 34.9557826,19.3100731 35.4435055,19.3100731 C35.9312284,19.3100731 36.4189513,19.1243731 36.7885939,18.7529732 C38.4879232,17.0455653 39.3401549,14.7965325 39.3401549,12.5629746 C39.3401549,10.3294168 38.4879232,8.07522568 36.7885939,6.37297607 C36.0441748,5.62501791 34.8377023,5.62501791 34.098417,6.37297607 Z M7.81785293,19.8671729 C5.80535417,17.8451067 4.80937264,15.2143574 4.80423872,12.5629746 C4.80937264,9.91675025 5.80535417,7.28084253 7.81785293,5.26393466 C8.56227211,4.5159765 8.56227211,3.30892678 7.81785293,2.56096862 C7.07343374,1.81301046 5.8720952,1.81301046 5.12767602,2.56096862 C2.38102593,5.31551798 1,8.94698381 1,12.5629746 C1,16.1789655 2.38102593,19.8104313 5.12254209,22.570139 C5.49218472,22.9415389 5.97990764,23.1272389 6.46763055,23.1272389 C6.95535346,23.1272389 7.44307637,22.9415389 7.812719,22.570139 C8.55713818,21.8221808 8.55713818,20.6151311 7.81785293,19.8671729 L7.81785293,19.8671729 Z M42.8774295,2.56096862 C42.1330103,1.81301046 40.9316717,1.81301046 40.1872526,2.56096862 C39.4428334,3.30892678 39.4428334,4.5159765 40.1872526,5.26393466 C42.1997513,7.28600086 43.1957328,9.91675025 43.2008668,12.568133 C43.2008668,15.2143574 42.1997513,17.8451067 40.1872526,19.8671729 C39.4428334,20.6151311 39.4428334,21.8273392 40.1872526,22.570139 C40.5568952,22.9415389 41.0446181,23.1272389 41.532341,23.1272389 C42.0200639,23.1272389 42.5077868,22.9415389 42.8774295,22.5649807 C45.6240796,19.8104313 47.0051055,16.1738071 46.9999858,12.5629746 C47.0051055,8.95214214 45.6240796,5.31551798 42.8774295,2.56096862 Z M23.9794501,18.2474567 C27.1008767,18.2474567 29.6319019,15.7043989 29.6319019,12.568133 C29.6319019,9.43186703 27.1008767,6.88880928 23.9794501,6.88880928 C20.8580234,6.88880928 18.3269982,9.43186703 18.3269982,12.568133 C18.3269982,15.6992406 20.8580234,18.2474567 23.9794501,18.2474567 Z';
var STNimg = 'image://data:image/png;base64,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'
var AMFimg = 'image://data:image/png;base64,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'
var SMFimg = 'image://data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAFAAAABQCAYAAACOEfKtAAAAAXNSR0IArs4c6QAAGI5JREFUeF7tnA2QZFV1x//n3vve656ZXdjdmV1EvkQEBaMp8duYCCQRSUo0KUxipVJWARqwCFAgIGhYAqJiDIhCJAYhETWC0Yjxs0QRYxSJqYpGY4UIrAu7wM5+Tne/r3vvSZ37Xs/0dPfM9OzOIli8rdnufu/16/d+738+7jm3m/D0sk8EaJ/e/fSbse8A23w5gI2/kizHaUk+S+6wKJg2Xx5tyjdu+MuHodoO4XYoVI8EkJL/5l5X2+p1PfvNW99zjOHvr4839NhyeAKr6mPmzqfnPOZ97uD67JAEj712Er6pgf0KUOA9lG/ccNnD0C0HjuZOWE6eA4jqBOdfzBCIfWC776fZi+2/0L6b0Adl8P0L3bj5xw3wTp7Ehq9vx9ZT1+9HgALvwXzjQRdvhmp7wABsavX1Xky/ChdS4Kzq5sDMQVhAPf1KDqqbD1Zu4ryb0HdulUKr92SHJnjsdZPY8LXtaG7J8cDZh+4ngDW8Z1ywGarlgaiCFxTYf4KK5tTXD6nP3LsXEo4hiwDpBd6/v8AacAM1EBFcd1uvioecX4AnyjtF4E0HeAL+wbMP2w8ABd4D+cZnnLcZuuXBhsERVQrsmnB9ocEXzT7v80cDF9JjYj3vqRQ0uv+SuxWUO4oPlOOCkR3WqOB9dRqNLXntQxkPnrXSAAXez/ONB5+zGWqm9nkBXB/AeXAoKCGAmKegYeoZBFW9Z1ggWmS9QBymzCHBK8D7vUls+EoFrzf4PXDWSppwF95Zm6EFnvi7YLpUKa/rA3VPxK3VMwtvMRPuM7NZ/9cPcF5Q4SFw66DVe9N6j9EDNj0sweO/P4X1X55G85EK3pzZE1YaIP8q5nkH3fEomo8Us+nObPaw4j6wzTx5Yqdy2qIy+et93r+u34E/Ua+7Kh8IID0Brt42/dYxPOvaX8zlqfX6KuWiFY7CwwAOg9iFuhCwXj8o+yx1oftx+0IAq7x1fwA8oRN83azy+hUor7vblwuqXzn9798P2wPAv9k050dnI3flR1c2D1yuAhe64P7o+Et8vZgJr3weuLc+8EkMcvrMQQV2zZfVSueBAlBMuBssFgoko/rApXzkE7A9ABwSRLpFkBVPYwYADvOBvWAX8mO/jPVDovMAwP2dBw6kMXsThVcqnVlukBoSzSsfuGleee3pPPDpPLAvN3w6D+yrEQ4b5PdCegK3P6nzQG2Ai/4owqfutti0jedK6/sh7zt8HeHNLzK45tslnAzYe0v5i3zekzYPfMYU4abzY7ziWI2XnpviwcelajL6hYV9l7H/syYJPzi/ie9tcnjb5wpsbY32eU/KPPDEFyvceF6CdatFBsC1ny+wfQb48g8dNm9nRBFw8vEa37vfYXpmkbHwkGg9uQp4xbM1vvo/DiUDh64lnHKcxrpx4PzfisPnbW8zzv5Cjm8+6Oc3uIbckCddHvjOP4tw3psM1JBO4B9fk+GuH3kcPAn813VjcJ7xuXsd3v2ZAtvbQ0D2AFw3AVz5hhh/8CINrQgvvLqDLTPAScco/NNbGgNVNs+M675r8d57yoWVrIAnXR74kuMUPvaOGM+ckqufv3QBvuBZhLuubM5uvPXuEu/4ZM+FDlHeB06L8JZXSdW2Wk66PsWPtjBOeu5wgI/s9jjz8wXu29KjwqdKHrjmAODD58Z47YulLAO8/ooMm7cxpluMzAKNBDjxhQpnnRwhLRh/+pEChXj+RRLsOAJuOyNGMyb87T0lvnm/R+aARgRMrqJgyneeXinxa/9rcc6dBXbmvT2Yvuf1Zz2p64Fnn2rwrjdHeNWFGR58bNCpK43gD3OBN9Cd61lXX2wSA6UHfN2tm32PAiSIfPecBq66q8SN37fDo/CQ9OhJXw88/hiFTdv84sFioSHZMuqBk6uBw9co/FBMdhl55Gh54GyTVe7q0LZGFS6XWlawHvj6l2ocvr7uyvXmbV119a7rnl2d223awbjzv3umkCwj3elPj0bKA8d6AIZzGYS49wD7qzG9xYVFLuzTFyb47RfKzstfvvEzhz+5JV9WnrhQXjlSHnhNKOBVc6eu2CgjggEVjg5wheqBKwpwobHyCPXEkfLAXoAbN/LcXLY5kCMAZEIbfqXqgZ++oFKg+MfXvTcbDCJD/N9Xzm3g8HUKAwoctTy2l/XA11yDaOo48B0/AQcVCsSui6nVuAjAMDGjWgRgf1tzL+uBXYAPPObxssv6AC4A5N5LGzhyqgfgE1QP7AIUBAHiRgFZL7VP3HuAo/jAIUAWA3jQmqoj9uhMTwGCgAGAI5jooukRAaPkgcffhnjV0eCpbeA77gBwbA/EfQa4NwokoOsDexUYx8Dbf9fg3JOrEceHvl7ihrttlWQPAzhq4XSReuIoeeBRN6MRbwevB/zdcmI/Bc9CXBxgj/leDsJFcMEHrkBfeJ4CL81CRH3JUQpfvnj+uPaUazPc91CV2y1pwnvRNx4lDzzidjTNDPiABH7VFnCAuBE+TGgWcybqATUvqwjrK/M+7Q7CrafZZfnARdqZw0x4QYCbqvHsggD3cx74ghsxsXYS7uEeiLNBJQDsQhqEB4jyKtkSbsUgwFF84BCQ80y4VuChU4QvXJDg0HVVIWLzdo9TP5xj885qGLigD1zkRi1VWB0lDzzmI1g9cSzcbg0X3wtuHgw+cg38HaeFYLIoQILMvRd420D4EsqV6gsP84ECqREDZ/+OCbf0xm/ZUHzoDs32OYjsZT3wqM/jgHg3/AFTD7ttjx7iZ025a8ZXVHNI+5bafE+r4c2AcA+Klc4D9ymNGTX/W8jER6wHHnE71iQebmI93PZp+LFx+PVT8CEq12lND8C+wNFV39RPFG49Ll+WD1zkAp9KeeDxt2Gq2AGXjsN1Etg4hh97CL65FfzDnfASkRcH+AAU1kPhs8hWan5gF2ArY9z147owMJvd9/ROehLWk56nMZHQ0iORUdObEfPAo2/CIYVtu9KOl1EO1xqD09vgm9NwD8Vw2Ak/CFDc45ugUMM7+HHoLfegvSIK7MkDB13H0mtmh3KjgtrHPPDwG3Fk0UqdAdsyGSs72+AUwycG5WObQnnSDQKU6CvmW08lWtvaEe347NrdK5UH3vC2GK8+drDsPzfGXAAkAd/5P4+33z43JXfeaEMOucL1wIM/hOeVlFvjkjLfA5czLGVwRqPcJQosYQcBnlar78iHDfKGWZ3E0Z5bV29flgIJCAXVaY/pPYMV5t6K8lLpxlLbpVu3YEF1kTxxlHrg+g+Wv25jtrqMi8LC5S048rBao2zthAWj7AdIeE24jwZTMIh2R+OlSdq3jm8d2Qca4OzX1yX9C7KhfeHZkn43VVkiIR4o6ffsH0r6f9FX0h8hwR4lD0wuw+cc2MMxM5SHD/mfZw8/xrigRXMA5espAO7WeN0hGuYofaBGbKkVu6RI0pvX/mKUPHBoU2maMT0zYlNpiG9bkabSXuaBGz5YvtyVsBaw1kUOFk5lsK3CX9Ew6sJOgYLATPWYQ+HYn2gcd5xGjGiiQOLiNPFcxPnHDvj5UnngE9XWPPFDKX78KC/YFw5tzX8pcN8jQ3okPUFllL7wQR/ByyiHc4AtS/iygCeGbbf9xiRRF6Ud5F2ACsdDYRU0jkQEi3jMI/YmS0qtXu6ujz+9lA/cb431VcCVp/Y11vdgwb6wNNav/a7F+6Sxvki0HmV+4KHXly+1WeR8E2XRLlAWsScF196Dy2ONizMrCjyNdTdlwQSi8G8McTNGXDj3SvY4yV+nzxzFBy44teM/HTZPz03t+P79DtuGTe1YJAGfWgW8vHdqxxrCKc8fcWrHkCr3KPXAw6/Hi2xZeofIFg5cSAKg4do7sTFu4JJsRgBezgY/gMZxMJhGBEIEnSaamq9kwokqxyfsjfi3pRTY/fLNvMlF56VD+8KLKWNek32RPK7bLAqTi87rm1w0Qp44Sj3wyI/jBTaHswV8XgCF/OeY2+2kAugE4Dmc4CFoHAGDHDJLJwLcbyjoE7THP1KO3cVH8dPl5IED09uWArEX9bwu6MMnF5netlDpvzsSWeJ7Is+5FcfaVu5LnziZ7JDtyRVZcKuT/FUwYSVR+Hxu4nFEWA+DnRI+8CoonBh53KqQ70aaUH4TfjaqAoMS96HMtFTet1LbR8kDn3sLjrEerswzn2UNZHmugAQzO3Bl0sDFaSYAz+YJ7EKE1Yjg8GoonACHWxKV74IDIQfym5L7R/GB877F1C3JDhshjJCnrRSovekL23GFX7zlmXj+zTiqJPiiBtjJoGBBM3v8VUlD1QDfygcEszX4TXicAODjsNiVuIxIg2EbnN2AB0fJAwcA7kPfdmCYNuyG7MPxF+oLuzGNrW+YApL4+ud9BtcVLXBB8GkKdNrQMP0AT+e1IXBE+GtYfBAldslotCHwCjDtSTn9VHMwke6vSPe+Xm69bn/vPySoDMsD/bjGljdOAXF849FfzK91M4nNHTgXgHtS4rypWBTYxlUJ4ZIUYsJv5Ul0EGECH4DGZcgAAdcch6MCjF3gziewZVk+8IkCMkK0nWfCPa6jPw904wpb37geysQ3HftVXF+28twVic0tOLPgdCalNG8qUWDRxlWecAkCwHN4Cm1EiHENSlwGGfHplMeypiMHTxm4dRse2ysfuL9BjmrCS+SBbkxh6x+uhyZz8zH/mt1glElL5fPW9tRltsEKTd+egU7zygcWTgCmlwC+JPw5rw8KHMP74XFpAOg7fjxlR5Y92Qk/cxumV0SBC6UVC5Whlkp/9mF7NwoH5Qk8ZW45+s787zx0xzQ5zR0XZafjxPdRuZY7aao6WVNJYC1yvGcO4Bm8AQVMAKjwTrTgEcOuUqgU+Ch49+3YsZw8cME63TLqdUNnra7g+wXgYR99GFtPWy+R4R+e/SX/McWcOc+ZWR13kKMsW3CtokPojKFjQaJCGWoUbVztLS6uTLgLsIn3w+GSoMASdlULTjXhqQPe9UnsXJYCnyJ5YDQtyjGfOOor/u+Jylwbk7ncZ4o4T+NGobO277TH0W4DnEK1LTTnUIXrB+gD2WtAuDgoMIJdLQDHd3n12IF+x2exe+mC+1Nvj+Y2f9tzvljcosmkLuIcngsXccYtXyDyhVHjfmYHqFWA2ikUR9C8C6rwuNqXvQqcB7Dj4cbK1RZeA07n8C7ZqRzWaL8bJonTKJqgqMxtU4NixypRhhKyrukj3yCYmBUlRL6hCQkRJ0wqIuaYjDLM3oBIk/aGlFJQIAr/g5SWnyuQ7957EhNWITVS1aOs1h6kVdV3VaGxHf5IwUPBkyanjLLM3pFSpYK4fBRMpSWoHMy5VyqLlMxT55wM55Rzwc1mh1GUZLjQnGRIOyV4rJxpSRI4QTNRgKhZFJhBFR1c7RkXwUpJX0y4AY2yVmAGhzx8sIxD3FQBz/k0lROR8TMH6IaDMY0ssQXFKqLEkY01bEx6LPE+b5CmmEGxJ4GJmBTFiigiqIi1jUhprZSS3oxWymvSXY+ppAtDpCt4Qq0LTh7Dc1kisCZVTTOrIQaAGlJq8qSUVeQcwTuWmGlMAS9eXoVH1dQZSls4hcL4srDg3JLJYy37+KKRcZmuaRbFDFxrq/y3SvEaUGs3dFBgBpWleA98rwKHAUxgsQNuQwaPDUCRwThAjwG6TNNYVGjzRuxQxEZRRCpraIoiZxAr+NiziZmLhIgiRdoQ+QgKmkSBWitSggsyO0eJ8kSD8lNbMoEpsJK6v3EE0twLEsoJZZZHpcBklBTagwpJyu1GOVLeMZQlUhYobaRVKf0LEAdwojx4UzAXZdkYK2JfFAwuEySlBAaft2zamvB7CpAtoNsRyO9IDVNTOYu1pceFMLgIoaQ/TIESRtQuhx0HuoMfgcPBkLqNtgraEXQjb0cuUpGjIjZaRUapyIFiBYo1ucgrF3mOYkWIlDLGk4sUe8NKB69AijSMIkVeQWnBJiApEmCyaF0pTkzZKNZd9dVT6pSYrREVelGt3ANPWjoWjtnAkdJOMTvS2jIXjrUqNZvSRVywRRk1Tc6utAItgDVcCDzbTp1TTSsNpFYLcGgpzxMq+D8LnXYwCY3T4XAPPL4zF4W7CkxxMSTTEYAppOXj8DDcIYc8jHzbIdo9E8oxdCNtRc4pY00RsW+a8SgxokTnbKRFccpGTBR5TUYAsndGa6cZRjOcZqOVhldK4EVhrmxApE3dXNCVAGVtmOXd/SmByiXKRpZTgwHLb5yJKZNWQYFKaee0C881aetV7ljHpXawDJSaSmsTU2jLFnFcMvKgPIFXpG1fxA2X0oT320G+A+UddDuGth7rygxnQuPbyPHvAq/qyvUqsMRFSOEwhhIZLMRrbIM96tfAnYegC5rWVhk9ESlV2tXGORiXpCZyhfHcMF5RZExptLh7ooi1EBN7kzzVafYwXIcMkY3WCUBOaWVJyZfsRKD1vHitROtzkZ0izT2/msQSH0LlTMtH1RCN8R42wCNir7xxSmcOjUbpCnaGZCOHdqRRsY0VrJSqkPgyT9nnnXFPcYtbMxPw1FYtkOKZMW0dJh3jDK9wDxjfk+ARAM7mgV0FdgFKV0Taxo9s80BbdsYRRxyB1D2m3W6tXDKpxsUX2uCltHepiZuknSsMI9HGl8b7xJgEYmdaWqkmhvJsNVSkWIGEMitDRlkSawvkJBbLJE5xf/Kh1TfGKquuX5MyDBNOCRQJOCP+j4Mf9GLapVeA9w0jlsTad5yrjMEyweqInS/Yljl8rBo2OrDjyj1jrlO2uBNPsHsUSnz+zM6O5nRM2RxTzgk8922Q/n6wUAFI4lO7CjThVN8Pi0vCRonCdRAJj9vAh+SgchWUa0wr15lUbg30WEt8YluzIeU8GU+FTnRDySNPkPYFdCRRgEixk3F6CCPEbIjJKuMtITKAicKwkrUwqcH1wJPNXZikonqidwmMRxU8zxU8ea7Dzwd68iVbRF5rdtbEEju89pkrkfhIsS1jdqVrOirBugPfkgl/UZvQHkcLUDyTauuak87jTGJ8y2ncGwYZwkf85phEeAF4Lm+oUucwEnknJI2Ja4BtuEC8hMduKGG+ZgIk0ZgNlIR0Tx0teUVTN5VHpplzzbah4mahBFxkoViRYooV+6CwAAoeZChVAaAAEkMUStGg+mTd3Pc1hXCtwISZokg8dpUXSkARgHHkxWYDSIp9qQtWBfvSJU4h8zpqOEXwufjJDrxkfQKPy3GSIZsEDFti0pU4YwBeUVvneGDkKoAlYjDeB4VL0ZFLqyHqagZSADguKSlodRPEnQpegKg7inWmEK8lGeZA71aJSSguJVkrNJuEuCwUTCxpIZjkOYgFmIDxBSGKEcmnVnne3DKfmrx9dqmAiRrFFQlAZjFZASjbBBwJOMReZrWI8goJMnLryjRUWKS/IQpMDTiAk5baTiirMSlmSx7fjDzuy0RQFQsbguvhtZWu7QdY4l1BoAY2yFXUJ3/SgpSlARLrH8/aCuMAZ6S4IMUaChER5KvABVTixL+RCuCimCBAXUEcomwMmIKCGnVJkKkHQPdhjlAPrGqHalM/U9Ixy4cGoGWBQsVMJmZyBUMzKySeEniStqQocjpcH+cSQwV8E57LlFLbDAJwwLoAT+EbDrgvcBA3Jv8eh0UDFgcGgL6a3iblLKkGigkD7xYzQFoVtVCkDGo6+cDgNuWvuphqnBKDGrIuvM4IrlFB8jmhTBRMTnF4TXPwnBTIi+o4E3OU4l559cPrAVhhLHpkWj8V1TmBGSJOUCKQg5B45NVjHloUEmjkx1OBTF4LSLmWCOQ81rkSpxPV8EQ8orwF4OF2ASgFVQ4NpQ+ED6++EVadiMS28FymSdTZrO/OKZTBqezkSUncC9Pl5Lc366GYpMfhC75hqX/YtHuxIr/6eLJ/WLrHqJ8OIqqP1LNfOHI9rBvYP5xPdQVyIsHGe65q7tyqqw25jwQk/DMU/mM22grAIcoTeNWFnc9rQz84tEugxATDx8id6d4hpECwz/rBphRei8+QuydLnslUcYj6EnkdzDWBqDA8dn2cq17HYae+RdfKXAierB+rVTZsH3E/ITesBSCP0tAVJZqEM2lXiDpFga25r23J6nCtCTz2hJyyGzxtyIfrgNE12y686nsiF/MB2IkIzSDWalTahSI9EfmTD5RFTLW7hHKDgKypBpgZCcTwlRk5hjyx1TruHrMRTjc41IElwK2XIYDDO3SywO+5ynEboaXTNdHZ8w5ru6JoVABlqVen3WuUx0bbozlu0YHDnvBncUQosASf1wsv3KzQF5YYMw4dHiU9THpAya9qyF//0rtP1zfaTg1gDBgL2cYskKDfLsTeY839BsWQD9mLVV049VtDY6y71Ns68lqeyxN5lD9JyvPgD0WFDlNwkqaEcLoWLnzNqzZbUV73kP8PnQA1mbMDqpAAAAAASUVORK5CYII='
// var UPFimg = 'image://data:image/png;base64,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'
var fhqimg = 'image://data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADoAAAA6CAYAAADhu0ooAAAAAXNSR0IArs4c6QAADjlJREFUaEPlm3uMXcddx7+/mTmPe+/GubZ37XjjpG5oJOQIqbQgKIQ0EqI0oRTUxpFY29AYtUBF4qA0RYAqOyFCaiMk/kD9g0IMgaat+QcJl5akKHVfYCdVm0Is+gdVaruW47Xj3b2Pc8480cw99+691/ex3rXrSj2r63POzJzHZ37PmTMm/Jhs9GPCiasDdY5jxT6bvtJeIAf0ru7exe+pPBko6+vO0KY8D3saPO9/o27bgbLV+3cfpeoCanP0Mmr0s+MEt3bQEnL2L88vVP+rCQgAzP8o7CkclxBl2Wq963RAr13neLX96n06bQiu29a/Yd9zBo4JyG5PsPzWTZDbohNmLvr5jYF2IT9+fqH2jQZcTH2gfbAjX6qUMu+wdgAmgw503gTQ7LYEyz+9CdtfuIgz+3ZsELSEnHvq/ELt6yVkDLieRMsX773QGAmU9V7yA7ChvA+8lHTojLEdB2RvSrH8tpuw/flLYAb4/gc2AlpCbnvi/ELtuIcEXEJhv6q6nRcNqjvuxfpeuqPiw2D9UNPv14P84iWQdUFDNgZ6yRytnmztqR1vwomOunpJDkiz32GUdhjspAvtywba9Nlm13l1pe2v63bY8HWhnGBqhHxniu1f6EAGX7ZRUHbKLFaO6dnwcN5Vyb69Lxt4uX7A8rgH3HfeXzZ8fQ909PXFHRzbv3QB8SVVQnZM4fQH5tdvo+KkOVd/JN8RIL1NDv98+agO6HrUcR3UrV/Htc27I8y+vIjosu557g1LNIA+nO8YCdmF7odaK+C0DphQ3w/a79g2ZKNTQSdJ1Nd5FR0ntXXW90CX9DVWXS/RqHzhUep7oyRagnYTj43b6CTVnWajk2xwHfbpobxEt760iHi5I9FuJnV9VbcvaeildGu10w2ABmfUJ9Eb64wmOZx12mdXoj2vW97n+oOuR3W7kJNiczeWjpD69XNGa4ijx55M8HM/2c0eNjbCPXHa4D1/X3Ri5CTQGxFHF49WN0Y3dPXck+2xYemGxtEu6Ny+9uo4kwOP/0aEj743wieOKTz9BbVa15XWUJxdfKrTYT3QEXH4+qnuGuLoVNDPKzz9byNAh2LwFaAjYvQw6A81jo4EZcDjvzlFousEHR9HxTuAMMlzxTZxKmVqCljG0R826Ng4Oive0ZmPCv8MAF9T0Gvlkeb+vD02Tx4YvQzE0fkT5q/5L+BwCUiDkp0A6kictD+YOHop4+g197prAb0iqb/1xPte5L/4z6/CBdiOZHtSHQPqQnkAXUMcHae6o2Lh4/dH+Oh9ET7xvMLTXxp0UAPO6Grj6G/vOPnWf3nl7js2v90OwnaAJ4GSOGF+UH+kuGXaeHSc1x2V/64ZdEwuPD6Ozp9882f4PTcnsN/cDAsv2ScOAzjs51tGgZZlh0Di/imgQ6o7HEdHjUcHQF9QA4nBhuLo78y/9EufF/dceA12G2C/DNjgM4IajwP1mv0gmPiIORskOmU8ekMkOjQePbN//uXdz1+6d+X/MrN1fpfpSbW01xGq6wiHQDgOJj5uz9Qfycer7vVyRlNSwFFx9PT+HS/vOrryy+03tui6gz7lJXo4SDU4piHQVbXFMXDxSXt6IuhQHL1m4WUtue7QePT0/vlv3vJc6101JVWxbVZVX3vVnsJdHtTiCQzbaDgn7AGhCSEO29euBnTARidlRmPGoz0bXUt48aOXvjh6et+t37rl74pft2mlaDeWdASlF78/pxHDYAfMlRL1ansMfH4e0YU/M9+rHyy2j/W6k5xRP2h/rjthPHpVoENx9Mz+na9sPsIeUE0oodoq5yuqaW+RuAwFQF0JugcMZxFv/inEjQP2u/VH8+3T5nWnpoCjkvoRcXJDcfSB+e+ln8QTVnNtjNEGXBmNRcT4HxCKPtBSbe9DhC1IahaV4qD9Tv1gvm29cXTqMG2dSX13KmVgXnePB+VPGgNtHbTfO4XdFeBTLY52F5SAQ4TdewTuuivGDCqpzmf0h+OX6o/mW9equtfcGV3FePTswvypzUfEB/MMWhsYJaFNjvcnwKcyhybBOYL/ezs4diMGIU0daqpi3kX78Vf1jxS1aXH0ukylTMuMhuLo2YX5V7f+jTuQWRiVR04pWCPxQBzjb/O2Bz3kmHc+mEeEnUjQQpUn+FVHdoYdwMfqB/PZaRIdOdU57bPDBqY7R8XRM/tu/e/tz7B9LQUrM5CSgJZ4MCY8k1MHVOAUItSQIEYFZN7NLK+IQn7N/oH4j/qjE0B/hOZ1z+zb8Z2dz4oHG+3CFZJYIWOmc/zWKujDLgGQIEMFAvfBIY3bPi+SzP6+eLH+aD43zRldtUSv8byuX2pwdu+t337zP8n3N4rUtpcLkatE6AwLcYxncu4l+mE3A40UhF+Bw03QOJ5wOGSw5g/t1yd63R+BeV0P+fr9szlj8e/d/oI6vrSUot0EzyWEyrA3TnCkA/ohdzOAKggfhMVnoeASCUMKWj9mvdfdNi2OjpXodbDT/mGahzz/a3N5gujh276qX8x0ItsXQG0DnreLqMiTBRCOIEWL8EduCxqoQuB34fAcZG4qMpVMtnTxWOXb9YP5+Mxoko2u09n0Om2K1xUNg9ffM1ukJnls1zf0i0bZLFepvOjXbhS5yCkVRQt7V0E/5LxXrcDiABQ+DQlZyyGZbqjsj2uvTgS9Qd9Ht3zrIi7fXS8SVfmTN/2n+jKqtqVym5slpxfbxNoFCWcoKlpJH+hjbjZIlOEhFPhHGBTImrKutGr86ab/3fQXZa7robwE+/fDvd5bldK3zqEb9MfVjfu+OrDKZfV+rZ8RcJtdMXNRfOy2E8VXFfFmpOMWB4qllZZpSOLNZjVyGaIC2BckqrzqdkGBhyDxD2CQ0Ci2LL6hlp+sP+W2YBYWnBzxsFc2goWA8dhMkDYROc5hrQhtevPxlnXW2VgiYn6aikInlatvVhd5hJrO7CTBgZEFDMC5AcGAwziyfv2JdsL/GGYumH/feRJfUZFrCp1nekVkLLVF3q7Z5cvgjXaeOE1xESV74fIjUGkJqlCFxEPIS1BCPrcC6c5dJFWPROxEolKecGIVErrKHFXgWBWCahamwuFqnCi1HAkBkeMUMYLgDAwRmPPrRiJLjDGIsitY1745OcbgwOE4gyXOjCOmCUpTLAqQlUQsZ4y1HSFzTrc5521OLpORawsdZ5yhaDSgms0GliSPbMHiXFEMm+wFwzPIfa7rJdoPGqNAE/ncOT+8WYSem+MJmrHCTMxVkWpOKYOuEKOUwCrETcUSpR6UmI0ZceG4jRwD9yIHB2MizE5R0IEI4L1VKcxD+oYuTPEwWCaEBreWMyjDnOKMFSBIy1zOBM+sQca4y4lcYZXLeGRzpioyazbsUoPYipmJbBtJXuAnEOFtAD4DGgnaLtBUOc7drHbOwRQAl9FypJ2IkzaLuWOJqamEFUnCmE4ZM4nhlDBHMRMUMQ7uGOfklAB1IBGOiHqQvKPfXpVJMMc5d4jgiIwjwQwxY0CxZjDacadAXGqrZSxcobnIGaKCUhRuKVOsWimkgVm5CFqW4OYiEh3jLdriXjA8B2AFYhQokMP/zr2udsntRt4Elm+CUO1GlAoW6YzFkWCRIRXzOIkZVGxFHDPSkddMZo2AEMzCcC68RL0YNfmTnjPzjF2PzYNE/aIwB6Ydi7ifDbBOcM2cNo4LxUlpMCfBhCTjJHgircy0Ta2yrqZWcpB+A7zRQKSMvlOTuBcan4NGCxEySGRXqm4X9LsosBNm1yJYC4tCzwmu9WaRFq3IpEwYRVF8UypM0YyFIME5CQfi1oGLGMzxiARp5lhHZQXv7Lub8MeRcBA6FBGHIyYcCVgPyrgzBspyirQjpwUV2sWxstYqUzgdJc5IXTMrfqUcgQVIwp3a4p1QOOrVFRYFqsg6A++H3RwIFUgcAOFZtFAEiWZhb7D8OsNyxOs7tjBD4Na0hKsSTwwJa6RwKfEZl3ILcCsVh4jJJR4UFJHyzsgTQfByNsOfC18CII3KTwYKxPyxAkWR9dAMzmpIyynWGoVlJjaGEh35QUncMhnVrLvcolZUI+shJe7UBu8EKyHhwyRy3Oa100+leFCLakgYGD6Nlg8v7QKsKsFg0FphqGyimQzMCTDnwB0y7hgx8JTFruAxJHfeTL2CeXvkMUFIcryzRtovBnUV2TsOIvSkLF794hXLDqyFD1KOXGxJSKcKZ1UCS9oZHqWGMvi4ZbM20K4CLofQOd5itb3HWnYUAhkcCnDkYT8HiVN+csxnRn6Q5kEJn4XxURQKChpJiJgOLAuONi28zeXBWhARQSQU+xBCYE5JFpyOBUU+AvpjxHBdScZBhqtb71QFaOK+C0twLh0x75xiCwNHunCExErvmjUcmp0vZjmBGYs7rMXdVsujEDaDSwu0S43c3YHEUW8OPtctUAPwbjjcXk7kW/i+9aMYZ/3LAz4O+BWjfufLfJgPod4vqfaA3rR8s9AOwXIGtrK8W+bTiV595+tBZ2M9KYdSv1bV/xELT7ShsGzvZUtoWYl/RRqcaIGVoLIF+iA7nyT86KWT66bwTt5LyAfwvOw9f18/Yu3kKYTI7wuClRT7itgrZng2gcsg0aCstqOq4dhLr2zWxRk6BSDRk6hvFMFKX9YupSw7Diu8m984XOG9iBdIBI1lSBTBr8hhyNBfOOyqOIM0zC/421PZ0f6mvo/6N1/rNw/sN13uI5Dvi4G6/ut6lUP3G3fqn11u5FXVb8KbY3CTATKUeUjvR5ahsRUSdUg46K66ekn27oNDzk+ICZwLlsrRAkNSAsTlDRtlc9X3CWPGTwuP+O8kXfg1Mo1tFrKlvi0aOm+GDrfYAoMVGNwcoqbGZVhvk/2Q/i7/DyfyqiFtD7UGAAAAAElFTkSuQmCC'
var jhjimg = 'image://data:image/png;base64,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'
var fiveimg = 'image://data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAFAAAABQCAYAAACOEfKtAAAAAXNSR0IArs4c6QAAHPNJREFUeF7tnAmYHGWZx//v931V1T0zuS9IAkQWwymCeHCsXIIIgoIL64rsCquggMiysiGAyIDII6AGsshyGBK55FIUlnUBVw5FFxFFH2GRI1w5gNyZPur4jn3er6pnenp6ZjohWWUfKs+ku6uqq6t+/X+P732/asLby5siQG/q3W+/GW8eYNWdD6D3/yXLbhqVz6g7jAim6s4PXk56p311CUTVwH8dAvkjAST4v4HX+bZiXdN+g9Y3HaP9+4vjtT02H57gRP4xA+fTdB6DPnfo+nhmhNcPmQxblsBmBcjwXkp6p527BLJi4IKBE+aTdx5EfoKDL6YNxBawjfdT/8W2XmjLl9ACZej7h/viBh/Xw/vIZEy7fxWWf3zqZgTI8F5Merc461WIqgUU4FShvuaLaVXhcArsV90AmAEIw6inVcledYPB8pc46EtoObdcofl74q0ivH7oZEy7bxXKyxIsPmWrzQSwgLfll1+FqFggyOF5BbaeoKAB9bVCajH3xoX4Y/DCQJqBt+7PsIa4gQIIC66xrVnFbc7Pw2PlHcbwVnp4DP7FU7beDAAZ3uKkd8t/ehWyYuGUgwsoV2DDhIsL9b6o/3mLPxpyIU0m1vSeXEGd+y/+trxyO/GBfFw4xFuXcnj/uRKlZUnhQx1ePHlTA2R4LyS90097FaKv8HkeXAvAQXDIK8GDGKSgduoZCip/T7tANMJ6hthOmW2Cl4f30cmY9pMcXnPwW3zypjThBryTX4VkeOzvvOlSrryGD5RNEbdQTz+8kUy4xcz6/V8rwEFBxbWBWwSt5i+t+RhNYOtbR3jj8CmY+h8rUV6awxswe8KmBuj+P+Z5W9zxGspL0/50pz972OQ+sOrc5ANrudNmlfFf8/PWda0O/P/qdUPlQwJIU4Artq08qQvvmPfKQJ5arM9TLtrEUbgdwHYQG1CHA9bsB3mf0S50M24fDmCet24OgAfUvK/rV16rAvl1Y/uGgmpVTuv7N8N2D/DbLw/40f7InfvRTZsHbqgCh7vg1uj4Z3w9kglv+jxwY33gXzDIlScOVWDDfJ3Y1HkgA2QTbgSL4QJJpz5wNB/5f7DdA2wTRBpFkE2exgwB2M4HNoMdzo/9Oda3ic5DAG7uPHBIGrMxUXhTpTMbGqTaRPPcB748qLz2dh74dh7Ykhu+nQe21Aib0papE4CDdpXYa3uJHWYQtpokMKYEGAusrTm8sd7hySUWT7xscd/TBqvrLeWydu6gXRGheb+m7W/ZPHCfHQVOPSzAgbsISMk2N/qSaoefPmNw5cMaj79iB0r1byKPfMvlgdtuSfjGZwIc8C4eruQLg/nNYovfvWTx4gqLdTEgJTC+m7DtFMLuswR230pAFaCdc1j43xpn3Z0NHsNuBMi3VB74yf0kLj0hRFeUK+7ZZRZX35/hR08Y9MVtTLPJ7KaMBY56j8Tn9w0wqYdw0PwYz69yHmB3CbAE1HVL46qDPPItkwee83cKZxwZenBrqw4X3Jbi5l8Y+PrYBqQ1gQK235Lwx+UOSgGX/02Io3fjXAq4+bca/3Jv5mEO6gwOp0wBvCXywHM+pXDGUTm8Py21OG5egpdWcCF04EJLEfDBHQV22Upg0hjC829YLHq0pUXaAvrkfRUu/Gh+3Mby5XsS3PC74n0d5Il/8XngJ/eXuPLUyF/fI380OH5+kptrcXHvnE6Y8/EAH3637DftBoxdv1LD8vXDK/T640Ic0eRL+X23/17j1B+l+********************************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'
var wlimg = 'image://data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAFAAAABQCAYAAACOEfKtAAAAAXNSR0IArs4c6QAAHBVJREFUeF7tnAm8HFWd73//c05Vdd97s3KTIARkRwERdcYFdCDgyCLOIBgFRhlnWN7TGZcRSEDmmcsMi0QWjQwaI8IAgiRBHMGRoG9YHGBwGRQHnz6fLJINEpLc3F5qOct8/qeqby+3b27fLDPIs/K5qe6q7uqqb/3+y/n/Tzfh98sOEaAdevfv34wdB1h1iwAMvSJZ9tOEfCZ8wTbBVN2i4LlkaM5nVkNUDfztEMjXBJDg/5rP833FtpbXtW1vOUb39xfH63psPjzBifxjmufTch5tnzt2ezw3wgvHD8KWJbBLATK8Z5OhOZeshqwYuKB5wnzyzoPIT7D9YrpA7ADbeD+NXmznhXbchA4oY98/3o1rP66Hd8Ig5tz/Etb96exdCJDhPZMM7b7weYiqBRTgVKG+1ovpVOF4ChxVXRNME8I46ulUslddO1i+iW03oePccoXm74n3ivDCiYOYs+ollNcmePqje+0igAW8V53/PETFAkEOzyuw8wQFNdXXCanD3BsX4o/BCwNpBd75eoY1xg0UQFhwjX2tKu5yfh4eK+8khrfRw2Pwz3x0710AkOE9nQy96pPPQ1YsnHJwAeUKbJhwcaHeF40+7vBHYy6kxcRa3pMrqHf/xXfLK7cXH8jHhUO8dymHd99GlNYmhQ91eOYjOxsgw/tNMrTHx56HGCl8ngfXAbANDnkleBBtCuqmnrGg8vd0C0Tb2M4QuymzS/Dy8N49iDnfzeG1Br+nP7IzTbgB7yPPQzI89nfedClXXsMHypaIW6hnFN62TLjDzEb9XyfAtqDiusAtglbrTWs9RgvY+t4RXjx5Fmb/80aU1+TwmmZP2NkA3Ssxz9t9xXqU16Sj6c5o9rDTfWDVucFja7nTZpXxX+vjzm2dDvy/6nlD5WMCSEuAK/ZtPK8P+*************************************************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'
   
// UPF 数量为1
var UPFinfo1 = [{
    x: 90,
    y: 30,
    name: 'UPF',
    symbol: UPFimg,
    symbolSize: 55,
    id: '13'
},
{
    x: 81,
    y: 30,
    name: 'UPF2bp',
    symbolSize: 1,
    label: {
        normal: {
            show: false,
        }
    },
},
{
    x: 100,
    y: 30,
    name: 'UPF2bp1',
    symbolSize: 1,
    label: {
        normal: {
            show: false,
        }
    },
},
{
    x: 100,
    y: 18,
    name: 'UPF2bp1t',
    symbolSize: 1,
    label: {
        normal: {
            show: false,
        }
    },
},
{
    x: 100,
    y: 42,
    name: 'UPF2bp1b',
    symbolSize: 1,
    label: {
        normal: {
            show: false,
        }
    },
},
{
    x: 80,
    y: 18,
    name: '边缘节点',
    symbol: 'rect',
    symbolSize: [32, 14],
    id: '15',
    itemStyle: {
            color: '#47AD3A00'
    },
    label: {
        normal: {
            show: true, //是否显示标签。
            position: 'inside', //标签的位置。['50%', '50%'] [x,y]   'inside'
            textStyle: { //标签的字体样式
                color: '#01F1F3', //字体颜色
                fontStyle: 'normal', //文字字体的风格 'normal'标准 'italic'斜体 'oblique' 倾斜
                fontWeight: 'bolder', //'normal'标准'bold'粗的'bolder'更粗的'lighter'更细的或100 | 200 | 300 | 400...
                fontFamily: 'sans-serif', //文字的字体系列
                fontSize: 16, //字体大小
            }
        },
    }
}]
// UPF 数量为3
var UPFinfo3 = [{
    x: 85,
    y: 25,
    name: 'UPF',
    symbol: UPFimg,
    symbolSize: 45,
    id: '13'
},{
    x: 95,
    y: 30,
    name: 'UPF',
    symbol: UPFimg,
    symbolSize: 45,
    id: '30'
},{
    x: 85,
    y: 37,
    name: 'UPF',
    symbol: UPFimg,
    symbolSize: 45,
    id: '31'
},
{
    x: 81,
    y: 30,
    name: 'UPF2bp',
    symbolSize: 1,
    label: {
        normal: {
            show: false,
        }
    },
},
{
    x: 100,
    y: 30,
    name: 'UPF2bp1',
    symbolSize: 1,
    label: {
        normal: {
            show: false,
        }
    },
},
{
    x: 100,
    y: 18,
    name: 'UPF2bp1t',
    symbolSize: 1,
    label: {
        normal: {
            show: false,
        }
    },
},
{
    x: 100,
    y: 42,
    name: 'UPF2bp1b',
    symbolSize: 1,
    label: {
        normal: {
            show: false,
        }
    },
},
{
    x: 80,
    y: 18,
    name: '边缘节点',
    symbol: 'rect',
    symbolSize: [32, 14],
    id: '15',
    itemStyle: {
            color: '#47AD3A00'
    },
    label: {
        normal: {
            show: true, //是否显示标签。
            position: 'inside', //标签的位置。['50%', '50%'] [x,y]   'inside'
            textStyle: { //标签的字体样式
                color: '#01F1F3', //字体颜色
                fontStyle: 'normal', //文字字体的风格 'normal'标准 'italic'斜体 'oblique' 倾斜
                fontWeight: 'bolder', //'normal'标准'bold'粗的'bolder'更粗的'lighter'更细的或100 | 200 | 300 | 400...
                fontFamily: 'sans-serif', //文字的字体系列
                fontSize: 16, //字体大小
            }
        },
    }
}]
// UPF 数量为2
var UPFinfo2 = [{
    x: 90,
    y: 25,
    name: 'UPF',
    symbol: UPFimg,
    symbolSize: 45,
    id: '13'
},{
    x: 90,
    y: 37,
    name: 'UPF',
    symbol: UPFimg,
    symbolSize: 45,
    id: '31'
},
{
    x: 81,
    y: 30,
    name: 'UPF2bp',
    symbolSize: 1,
    label: {
        normal: {
            show: false,
        }
    },
},
{
    x: 100,
    y: 30,
    name: 'UPF2bp1',
    symbolSize: 1,
    label: {
        normal: {
            show: false,
        }
    },
},
{
    x: 100,
    y: 18,
    name: 'UPF2bp1t',
    symbolSize: 1,
    label: {
        normal: {
            show: false,
        }
    },
},
{
    x: 100,
    y: 42,
    name: 'UPF2bp1b',
    symbolSize: 1,
    label: {
        normal: {
            show: false,
        }
    },
},
{
    x: 80,
    y: 18,
    name: '边缘节点',
    symbol: 'rect',
    symbolSize: [32, 14],
    id: '15',
    itemStyle: {
            color: '#47AD3A00'
    },
    label: {
        normal: {
            show: true, //是否显示标签。
            position: 'inside', //标签的位置。['50%', '50%'] [x,y]   'inside'
            textStyle: { //标签的字体样式
                color: '#01F1F3', //字体颜色
                fontStyle: 'normal', //文字字体的风格 'normal'标准 'italic'斜体 'oblique' 倾斜
                fontWeight: 'bolder', //'normal'标准'bold'粗的'bolder'更粗的'lighter'更细的或100 | 200 | 300 | 400...
                fontFamily: 'sans-serif', //文字的字体系列
                fontSize: 16, //字体大小
            }
        },
    }
}]
// UPF 数量为4
var UPFinfo4 = [{
    x: 85,
    y: 25,
    name: 'UPF',
    symbol: UPFimg,
    symbolSize: 45,
    id: '13'
},{
    x: 95,
    y: 25,
    name: 'UPF',
    symbol: UPFimg,
    symbolSize: 45,
    id: '30'
},{
    x: 85,
    y: 37,
    name: 'UPF',
    symbol: UPFimg,
    symbolSize: 45,
    id: '31'
},{
    x: 95,
    y: 37,
    name: 'UPF',
    symbol: UPFimg,
    symbolSize: 45,
    id: '32'
},
{
    x: 81,
    y: 30,
    name: 'UPF2bp',
    symbolSize: 1,
    label: {
        normal: {
            show: false,
        }
    },
},
{
    x: 100,
    y: 30,
    name: 'UPF2bp1',
    symbolSize: 1,
    label: {
        normal: {
            show: false,
        }
    },
},
{
    x: 100,
    y: 18,
    name: 'UPF2bp1t',
    symbolSize: 1,
    label: {
        normal: {
            show: false,
        }
    },
},
{
    x: 100,
    y: 42,
    name: 'UPF2bp1b',
    symbolSize: 1,
    label: {
        normal: {
            show: false,
        }
    },
},
{
    x: 80,
    y: 18,
    name: '边缘节点',
    symbol: 'rect',
    symbolSize: [32, 14],
    id: '15',
    itemStyle: {
            color: '#47AD3A00'
    },
    label: {
        normal: {
            show: true, //是否显示标签。
            position: 'inside', //标签的位置。['50%', '50%'] [x,y]   'inside'
            textStyle: { //标签的字体样式
                color: '#01F1F3', //字体颜色
                fontStyle: 'normal', //文字字体的风格 'normal'标准 'italic'斜体 'oblique' 倾斜
                fontWeight: 'bolder', //'normal'标准'bold'粗的'bolder'更粗的'lighter'更细的或100 | 200 | 300 | 400...
                fontFamily: 'sans-serif', //文字的字体系列
                fontSize: 16, //字体大小
            }
        },
    }
}]

//  series 中 graph data 基础数据
export function graphData () {
    var data = [
        {
            x: 1,
            y: 5,
            name: '5G基站',
            symbol: demoSvg,
            symbolSize: 45,
            id: '0'
        },
        {
            x: 1,
            y: 20,
            name: '5G基站',
            symbol: demoSvg,
            symbolSize: 45,
            id: '1'
        },
        {
            x: 1,
            y: 35,
            name: '5G基站',
            symbol: demoSvg,
            symbolSize: 45,
            id: '2',
            label: {
                normal: {
                    show: true, //是否显示标签。
                    position: 'inside', //标签的位置。['50%', '50%'] [x,y]   'inside'
                    textStyle: { //标签的字体样式
                        color: 'red', //字体颜色
                        fontStyle: 'normal', //文字字体的风格 'normal'标准 'italic'斜体 'oblique' 倾斜
                        fontWeight: 'bolder', //'normal'标准'bold'粗的'bolder'更粗的'lighter'更细的或100 | 200 | 300 | 400...
                        fontFamily: 'sans-serif', //文字的字体系列
                        fontSize: 16, //字体大小
                    }
                },
            }   
        },
        {
            x: 21,
            y: 20,
            name: 'STN',
            symbol: STNimg,
            symbolSize: 60,
            id: '3'
        },
        {
            x: 50,
            y: 4,
            name: 'AMF',
            symbol: AMFimg,
            symbolSize: 55,
            id: '4'
        },
        {
            x: 70,
            y: 4,
            name: 'SMF',
            symbol: SMFimg,
            symbolSize: 55,
            id: '5'
        },
        {
            x: 90,
            y: 4,
            name: 'UPF',
            symbolSize: 1,
            label: {
                normal: {
                    show: false,
                }
            },
            id: '6'
        },
        {
            x: 60,
            y: -3,
            name: '5G SA核心网',
            symbol: 'rect',
            symbolSize: [32, 14],
            itemStyle: {
                    color: '#47AD3A00'
            },
            label: {
                normal: {
                    show: true, //是否显示标签。
                    position: 'inside', //标签的位置。['50%', '50%'] [x,y]   'inside'
                    textStyle: { //标签的字体样式
                        color: 'red', //字体颜色
                        fontStyle: 'normal', //文字字体的风格 'normal'标准 'italic'斜体 'oblique' 倾斜
                        fontWeight: 'bolder', //'normal'标准'bold'粗的'bolder'更粗的'lighter'更细的或100 | 200 | 300 | 400...
                        fontFamily: 'sans-serif', //文字的字体系列
                        fontSize: 16, //字体大小
                    }
                },
            }
        },
        {
            x: 59.5,
            y: 6,
            name: 'N11',
            symbol: 'rect',
            symbolSize: [32, 14],
            id: '7',
            itemStyle: {
                color: '#47AD3A'
            },
            label: {
                show: true, //是否显示标签。
                position: 'inside', //标签的位置。['50%', '50%'] [x,y]   'inside'
                textStyle: { //标签的字体样式
                    color: '#fff', //字体颜色
                    fontStyle: 'normal', //文字字体的风格 'normal'标准 'italic'斜体 'oblique' 倾斜
                    fontWeight: 'bolder', //'normal'标准'bold'粗的'bolder'更粗的'lighter'更细的或100 | 200 | 300 | 400...
                    fontFamily: 'sans-serif', //文字的字体系列
                    fontSize: 14, //字体大小
                }
            }
        },
        {
            x: 82,
            y: 6,
            name: 'N4',
            symbol: 'rect',
            symbolSize: [32, 14],
            id: '8',
            itemStyle: {
                    color: '#47AD3A'
            },
            label: {
                normal: {
                    show: true, //是否显示标签。
                    position: 'inside', //标签的位置。['50%', '50%'] [x,y]   'inside'
                    textStyle: { //标签的字体样式
                        color: '#fff', //字体颜色
                        fontStyle: 'normal', //文字字体的风格 'normal'标准 'italic'斜体 'oblique' 倾斜
                        fontWeight: 'bolder', //'normal'标准'bold'粗的'bolder'更粗的'lighter'更细的或100 | 200 | 300 | 400...
                        fontFamily: 'sans-serif', //文字的字体系列
                        fontSize: 14, //字体大小
                    }
                },
            }
        },

        



        // point
        {
            name: '5G1',
            symbolSize: 0,
            label: {
                show: false,
            },
            x: 6,
            y: 0,
        },
        {
            name: '5G2',
            symbolSize: 0,
            label: {
                normal: {
                    show: false,
                }
            },
            x: 6,
            y: 20,
        },
        {
            name: '5G3',
            symbolSize: 0,
            label: {
                normal: {
                    show: false,
                }
            },
            x: 6,
            y: 41,
        },
        {
            name: 'STNp',
            symbolSize: 1,
            label: {
                normal: {
                    show: false,
                }
            },
            x: 17,
            y: 20,
        },
        {
            name: 'STN1',
            symbolSize: 1,
            label: {
                normal: {
                    show: false,
                }
            },
            x: 25,
            y: 0,
        },
        {
            name: 'STN1.1',
            symbolSize: 1,
            label: {
                normal: {
                    show: false,
                }
            },
            x: 25,
            y: 6,
        },
        {
            name: 'STN2',
            symbolSize: 1,
            label: {
                normal: {
                    show: false,
                }
            },
            x: 25,
            y: 41,
        },
        {
            x: 43,
            y: 6,
            name: 'AMFp',
            symbolSize: 1,
            label: {
                normal: {
                    show: false,
                }
            },
        },
        {
            x: 55,
            y: 6,
            name: 'AMFp1',
            symbolSize: 1,
            label: {
                normal: {
                    show: false,
                }
            },
        },
        {
            x: 65,
            y: 6,
            name: 'SMFp',
            symbolSize: 1,
            label: {
                normal: {
                    show: false,
                }
            },
        },
        {
            x: 75,
            y: 6,
            name: 'SMFp1',
            symbolSize: 1,
            label: {
                normal: {
                    show: false,
                }
            },
        },
        {
            x: 90,
            y: 6,
            name: 'UPFp',
            symbolSize: 1,
            label: {
                normal: {
                    show: false,
                }
            },
        },
        {
            x: 90,
            y: 20,
            name: 'UPFpt',
            symbolSize: 1,
            label: {
                normal: {
                    show: false,
                }
            },
        },

        //  顶部虚线盒子
        {
            x: 44,
            y: -6,
            name: 'topBoxlt',
            symbolSize: 1,
            label: {
                normal: {
                    show: false,
                }
            },
        },
        {
            x: 44,
            y: 12,
            name: 'topBoxlb',
            symbolSize: 1,
            label: {
                normal: {
                    show: false,
                }
            },
        },
        {
            x: 95,
            y: -6,
            name: 'topBoxrt',
            symbolSize: 1,
            label: {
                normal: {
                    show: false,
                }
            },
        },
        {
            x: 95,
            y: 12,
            name: 'topBoxrb',
            symbolSize: 1,
            label: {
                normal: {
                    show: false,
                }
            },
        },

        

        // 底部 UPF 相关  动态concat

        // 网络状态
        
        {
            x: 110,
            y: -5,
            name: '网络状态',
            symbol: 'rect',
            symbolSize: [32, 14],
            itemStyle: {
                    color: '#47AD3A00'
            },
            label: {
                normal: {
                    show: true, //是否显示标签。
                    position: 'inside', //标签的位置。['50%', '50%'] [x,y]   'inside'
                    textStyle: { //标签的字体样式
                        color: '#01F1F3', //字体颜色
                        fontStyle: 'normal', //文字字体的风格 'normal'标准 'italic'斜体 'oblique' 倾斜
                        fontWeight: 'bolder', //'normal'标准'bold'粗的'bolder'更粗的'lighter'更细的或100 | 200 | 300 | 400...
                        fontFamily: 'sans-serif', //文字的字体系列
                        fontSize: 16, //字体大小
                    }
                },
            }
        },
        {
            x: 120,
            y: -5,
            symbolSize: [16, 16],
            itemStyle: {
                    color: '#47AD3A'
            },
            label: {
                normal: {
                    show: false, //是否显示标签。
                },
            }
        },

        

    ]

    return data;
}

//  series 中 graph links 基础数据
export function graphLinks () {
    var data = [
        {
            source: '5G1',
            target: '5G3',
        },
        {
            source: '5G2',
            target: 'STNp',
            symbol:["none","none"],
            symbolSize:12,
        },
        {
            source: 'STN1',
            target: 'STN2',
        },
        {
            source: 'STN1.1',
            target: 'AMFp',
            symbol:["none","arrow"],
            symbolSize:12,
            // emphasis: {
            //     label: {
            //         show: true,
            //         color: '#fff',
            //         fontSize: 14,
            //         formatter: () => {
            //             return '测试文字'
            //         }

            //     }
            // }
        },
        {
            source: 'AMFp1',
            target: 'SMFp',
            symbol:["none","arrow"],
            symbolSize:12,
        },
        {
            source: 'SMFp1',
            target: 'UPFp',
            symbol:["none","none"],
            symbolSize:12,
        },
        {
            source: 'UPFp',
            target: 'UPFpt',
            symbol:["none","arrow"],
            symbolSize:12,
        },
        {
            source: 'FHQ1p',
            target: 'FHQ1p1',
        },
        {
            source: 'FHQ2p',
            target: 'FHQ2p1',
        },
        {
            source: 'FHQ1p1',
            target: 'FHQ2p1',
        },
        {
            source: 'JHJ1p',
            target: 'JHJ1p1',
        },
        {
            source: 'JHJ2p',
            target: 'JHJ2p1',
        },
        {
            source: 'JHJ1p1',
            target: 'JHJ2p1',
        },

        
        {
            source: 'topBoxlt',
            target: 'topBoxlb',
            lineStyle: {
                    type: 'dashed',
                    dashOffset: 10
            },
        },
        {
            source: 'topBoxlb',
            target: 'topBoxrb',
            lineStyle: {
                    type: 'bold',
                    dashOffset: 10
            },
        },
        {
            source: 'topBoxrb',
            target: 'topBoxrt',
            lineStyle: {
                    type: 'boid',
                    dashOffset: 10,
                    color: "#00F5FF",
            },
        },
        {
            source: 'topBoxrt',
            target: 'topBoxlt',
            lineStyle: {
                    type: 'boid',
                    dashOffset: 10,
                    color: "red",
                    curveness: 0,
            },
        },
        {
            source: 'topBoxrt',
            target: 'topBoxlt',
            lineStyle: {
                    type: 'dashed',
                    dashOffset: 0,
                    color: "red",
                    curveness: 0.03,
            },
        },

        
    ]

    return data;
}

//  根据 UPF 数量 渲染不同UPF数量的node
function getUPFdata (num) { 
    if (num > 4 || num == 4) {
        return UPFinfo4;
    }else if (num < 1 || num == 1) {
        return UPFinfo1;
    } else if (num == 2) {
        return UPFinfo2;
    } else {
        return UPFinfo3;
    }
}

// 边缘节点node 数据
export function getEdgeNodes (n) {
    var data = [
        {
            x: 55,
            y: 22,
            name: '防火墙',
            symbol: fhqimg,
            symbolSize: 40,
            id: '9'
        },
        {
            x: 55,
            y: 35,
            name: '防火墙',
            symbol: fhqimg,
            symbolSize: 40,
            id: '10'
        },
        {
            x: 65,
            y: 22,
            name: '交换机',
            symbol: jhjimg,
            symbolSize: 40,
            id: '11'
        },
        {
            x: 65,
            y: 35,
            name: '交换机',
            symbol: jhjimg,
            symbolSize: 40,
            id: '12'
        },

        {
            x: 50,
            y: 18,
            name: 'FHQ1lt',
            symbolSize: 1,
            label: {
                normal: {
                    show: false,
                }
            },
        },
        {
            x: 50,
            y: 29,
            name: 'FHQ1lb',
            symbolSize: 1,
            label: {
                normal: {
                    show: false,
                }
            },
        },
        {
            x: 50,
            y: 31,
            name: 'FHQ2lt',
            symbolSize: 1,
            label: {
                normal: {
                    show: false,
                }
            },
        },
        {
            x: 46,
            y: 30,
            name: 'FHQ12p',
            symbolSize: 1,
            label: {
                normal: {
                    show: false,
                }
            },
        },
        {
            x: 50,
            y: 42,
            name: 'FHQ2lb',
            symbolSize: 1,
            label: {
                normal: {
                    show: false,
                }
            },
        },


        {
            name: 'STNtor',
            symbolSize: 1,
            label: {
                normal: {
                    show: false,
                }
            },
            x: 25,
            y: 30,
        },



        {
            x: 70,
            y: 18,
            name: 'JHJ1rt',
            symbolSize: 1,
            label: {
                normal: {
                    show: false,
                }
            },
        },
        {
            x: 70,
            y: 29,
            name: 'JHJ1rb',
            symbolSize: 1,
            label: {
                normal: {
                    show: false,
                }
            },
        },
        {
            x: 70,
            y: 31,
            name: 'JHJ2rt',
            symbolSize: 1,
            label: {
                normal: {
                    show: false,
                }
            },
        },
        {
            x: 70,
            y: 42,
            name: 'JHJ2rb',
            symbolSize: 1,
            label: {
                normal: {
                    show: false,
                }
            },
        },
        {
            x: 72,
            y: 30,
            name: 'JHJ12p',
            symbolSize: 1,
            label: {
                normal: {
                    show: false,
                }
            },
        },
        //  底部虚线盒子
        {
            x: 46,
            y: 15,
            name: 'bBoxlt',
            symbolSize: 1,
            label: {
                normal: {
                    show: false,
                }
            },
        },
        {
            x: 46,
            y: 45,
            name: 'bBoxlb',
            symbolSize: 1,
            label: {
                normal: {
                    show: false,
                }
            },
        },
        {
            x: 100,
            y: 15,
            name: 'bBoxrt',
            symbolSize: 1,
            label: {
                normal: {
                    show: false,
                }
            },
        },
        {
            x: 100,
            y: 45,
            name: 'bBoxrb',
            symbolSize: 1,
            label: {
                normal: {
                    show: false,
                }
            },
        },
    ]

    data = data.concat(getUPFdata(n)) ;

    return data;
}

// 边缘节点link 数据
export function getEdgeLines (params) {
    var data = [
        {
            source: 'FHQ1lt',
            target: 'FHQ1lb',
            lineStyle: {
                    type: 'dashed'
            },
        },
        {
            source: 'FHQ1lb',
            target: 'JHJ1rb',
            lineStyle: {
                    type: 'dashed'
            },
        },
        {
            source: 'JHJ1rt',
            target: 'JHJ1rb',
            lineStyle: {
                    type: 'dashed'
            },
        },
        {
            source: 'FHQ1lt',
            target: 'JHJ1rt',
            lineStyle: {
                    type: 'dashed'
            },
        },

        {
            source: 'FHQ2lt',
            target: 'FHQ2lb',
            lineStyle: {
                    type: 'dashed'
            },
        },
        {
            source: 'FHQ2lb',
            target: 'JHJ2rb',
            lineStyle: {
                    type: 'dashed'
            },
        },
        {
            source: 'JHJ2rt',
            target: 'JHJ2rb',
            lineStyle: {
                    type: 'dashed'
            },
        },
        {
            source: 'FHQ2lt',
            target: 'JHJ2rt',
            lineStyle: {
                    type: 'dashed'
            },
        },

        {
            source: 'JHJ12p',
            target: 'UPF2bp',
            symbol:["none","arrow"],
            symbolSize:12,
        },


        {
            source: 'FHQ1p1',
            target: 'JHJ1p1',
            lineStyle: {
                    type: 'dashed',
                    dashOffset: -10
            },
        },
        {
            source: 'FHQ2p1',
            target: 'JHJ2p1',
            lineStyle: {
                    type: 'dashed',
                    dashOffset: 10
            },
        },{
            source: 'bBoxlt',
            target: 'bBoxlb',
            lineStyle: {
                    type: 'dashed',
                    dashOffset: 10
            },
        },
        {
            source: 'bBoxlb',
            target: 'bBoxrb',
            lineStyle: {
                    type: 'dashed',
                    dashOffset: 10
            },
        },
        {
            source: 'bBoxrb',
            target: 'bBoxrt',
            lineStyle: {
                    type: 'dashed',
                    dashOffset: 10
            },
        },
        {
            source: 'bBoxrt',
            target: 'bBoxlt',
            lineStyle: {
                    type: 'dashed',
                    dashOffset: 10
            },
        },
        {
            source: 'STNtor',
            target: 'FHQ12p',
            symbol:["arrow","arrow"],
            symbolSize:12,
        },]

    return data;
}

var lineInfo1 = [
    {
        x: 120,
        y: 30,
        name: '5G专线',
        symbol: fiveimg,
        symbolSize: 45,
        id: '14'
    },
    {
        x: 116,
        y: 30,
        name: '5Gp',
        symbolSize: 1,
        label: {
            normal: {
                show: false,
            }
        },
    },
]

var lineInfo2 = [
    
    {
        x: 120,
        y: 18,
        name: '5G专线',
        symbol: fiveimg,
        symbolSize: 45,
        id: '141'
    },
    {
        x: 116,
        y: 18,
        name: '5Gpt',
        symbolSize: 1,
        label: {
            normal: {
                show: false,
            }
        },
    },
    {
        x: 120,
        y: 42,
        name: '5G专线',
        symbol: fiveimg,
        symbolSize: 45,
        id: '142'
    },
    {
        x: 116,
        y: 42,
        name: '5Gpb',
        symbolSize: 1,
        label: {
            normal: {
                show: false,
            }
        },
    },
]

var lineInfo3 = [
    {
        x: 120,
        y: 30,
        name: '5G专线',
        symbol: fiveimg,
        symbolSize: 45,
        id: '14'
    },
    {
        x: 116,
        y: 30,
        name: '5Gp',
        symbolSize: 1,
        label: {
            normal: {
                show: false,
            }
        },
    },
    {
        x: 120,
        y: 18,
        name: '5G专线',
        symbol: fiveimg,
        symbolSize: 45,
        id: '141'
    },
    {
        x: 116,
        y: 18,
        name: '5Gpt',
        symbolSize: 1,
        label: {
            normal: {
                show: false,
            }
        },
    },
    {
        x: 120,
        y: 42,
        name: '5G专线',
        symbol: fiveimg,
        symbolSize: 45,
        id: '142'
    },
    {
        x: 116,
        y: 42,
        name: '5Gpb',
        symbolSize: 1,
        label: {
            normal: {
                show: false,
            }
        },
    },
]

// 结束节点node （5G/物理）
export function graphEndNodes (v) {
    var lineInfo1 = [
        {
            x: 120,
            y: 30,
            name: '5G专线',
            symbol: fiveimg,
            symbolSize: 45,
            id: '14'
        },
        {
            x: 116,
            y: 30,
            name: '5Gp',
            symbolSize: 1,
            label: {
                normal: {
                    show: false,
                }
            },
        },
    ]

    

    var lineInfo2 = [
        {
            x: 120,
            y: 30,
            name: '物理线路',
            symbol: wlimg,
            symbolSize: 55,
            id: '14'
        }
    ]

    if (v == '5g') {
        return lineInfo1;
    } else {
        return lineInfo2;
    }

}

// 结束节点link
export function graphEndLinks (v) {
    var data1 = [
        {
            source: 'UPF2bp1',
            target: '5Gp',
            symbol:["none","arrow"],
            symbolSize:12,
        },
        {
            source: 'UPF2bp1t',
            target: '5Gpt',
            symbol:["none","arrow"],
            symbolSize:12,
        },
        {
            source: 'UPF2bp1b',
            target: '5Gpb',
            symbol:["none","arrow"],
            symbolSize:12,
        },
    ]
    var data2 = [
        {
            source: 'UPF2bp1',
            target: '5Gp',
            symbol:["none","arrow"],
            symbolSize:12,
        },
    ]

    if (v == '5g') {
        return data1;
    } else {
        return data2;
    }

}