<template>
  <div class="network-container">
    <div
      class="network-item"
      @click="viewDeviceDetail('1')"
    >
      <div class="mapLeftTop2Img1" />
      <div class="mapLeftTop2Text">
        防火墙
      </div>
      <div class="mapLeftTop2Text2">
        <span style="color: #55A4FE;">{{ data.fhqNum || 0 }}</span>
      </div>
    </div>
      
    <div
      class="network-item"
      @click="viewDeviceDetail('2')"
    >
      <div class="mapLeftTop2Img2" />
      <div class="mapLeftTop2Text">
        交换机
      </div>
      <div class="mapLeftTop2Text2">
        <span style="color: #5ADB95;">{{ data.jhjNum || 0 }}</span>
      </div>
    </div>
      
    <div
      class="network-item"
      @click="viewDeviceDetail('3')"
    >
      <div class="mapLeftTop2Img3" />
      <div class="mapLeftTop2Text">
        安全设备
      </div>
      <div class="mapLeftTop2Text2">
        <span style="color: #FABB61;">{{ data.posAqNum || 0 }}</span>
      </div>
    </div>
  </div>
</template>
  
  <script>
  export default {
    props: {
      data: {
        type: Object,
        default: () => ({})
      }
    },
    methods: {
      viewDeviceDetail(type) {
        // console.log(`查看${type}详情`);
        // 实际项目中这里可能会打开详情弹窗或跳转页面
        const data = {
          // type:'数通设备',
          message: type,
        };
        
        // 触发自定义事件，并传递数据
        this.$emit('customEvent', data);
      }
    }
  };
  </script>
  
  <style scoped>
  .network-container {
    display: flex;
    flex-direction: column;
    gap: 10px;
  }
  
  .network-item {
    display: flex;
    align-items: center;
    cursor: pointer;
    padding: 8px 0;
    border-radius: 4px;
    transition: background-color 0.2s;
  }
  
  .network-item:hover {
    background-color: rgba(255, 255, 255, 0.05);
  }
  
  .mapLeftTop2Img1 {
  margin-top: 17Px;
  margin-left: 16Px;
  width: 66Px;
  height: 66Px;
  background-image: url('../../assets/zxx/mapLeftTop2Img1.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
  display: inline-block;
}

.mapLeftTop2Img2 {
  margin-top: 17Px;
  margin-left: 16Px;
  width: 66Px;
  height: 66Px;
  background-image: url('../../assets/zxx/mapLeftTop2Img2.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
  display: inline-block;
}
.mapLeftTop2Img3 {
  margin-top: 17Px;
  margin-left: 16Px;
  width: 66Px;
  height: 66Px;
  background-image: url('../../assets/zxx/mapLeftTop2Img3.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
  display: inline-block;
}
.mapLeftTop2Text {
  margin-top: 2%;
  margin-left: 22%;
  width: 75Px;
  font-size: 16Px;
  font-family: Source Han Sans CN;
  font-weight: 400;
  color: #FFFFFF;
  position: absolute;
  display: inline-block;
}

.mapLeftTop2Text2 {
  position: absolute;
  margin-top: -3%;
  margin-left: 70%;
  width: 62Px;
  height: 27Px;
  font-size: 28Px;
  font-family: D-DIN-PRO;
  font-weight: 500;
  color: #FABB61;
  line-height: 50Px;
  display: inline-block;
}
  </style>
  