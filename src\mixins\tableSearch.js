/*******************
 * 需要 tableSearch type promise
 * @return  query
 */
export default {
  data() {
    return {
      loading: false,
      checkWidth: 0,
      empty_title: "",
      total: 0,
      pagination: {
        page: 0,
        pageSize: 10
      },
      data: [],
    };
  },
  methods: {
    /***
     * @returns {Object} {rows:Array,next:bool}
     */
    switchTableData(result) {
      switch (this.tableSource) {
        case 'vpdnsys':
          return this.ctTable(result);
        case 'iotp':
          return this.iotpTable(result);
        default:
          return { rows: [], next: false };
      }
    },

    iotpTable(_result) {
      if (!_result?.code == 200) {
        throw new Error(!_result.error_msg);
      }
      if (_result.data.page_info) {
        _result.data = _result.data.page_info;
      }
      const result = _result.data || {};
      // const { total_elements, result, content, totalElements } = _result.data || {};
      const totalElements = _result.total || {};
      return {
        rows: result || content,
        total: totalElements || total_elements
      };
    },

    ctTable(_result) {
      if (!_result?.success) {
        throw new Error(!_result.error_msg);
      }
      return {
        rows: _result.data,
        total: 1000
      };
    },
    _action() {
      return true;
    },
    async query(firstpage) {
      if (!this._action()) return null;
      if (firstpage) this.pagination.page = 0;
      const { page, pageSize } = this.pagination;
      if (this.allData) {
        this.data = this.__tableData.slice(page * pageSize, (page + 1) * pageSize);
        this.total = this.__tableData.length || 0;
        return null;
      }
      this.loading = true;
      try {
        const result = await this.tableSearch({ page, size: pageSize });
        this.loading = false;
        this.empty_title = "暂无数据";
        this.checkWidth = 55;
        const { rows, total } = this.switchTableData(result.data || {});
        if (rows instanceof Array) {
          this.data = rows;
          this.total = total || 0;
        } else {
          this.data = rows.records;
          this.total = rows.total || 0;
        }

        return result;
      } catch (err) {
        this.empty_title = "暂无数据";
        this.checkWidth = 55;
        this.loading = false;
        console.error(err);
      } finally {

      }
    },
  }
};
