// 核心文件
// import mock from "../mock/mock_lvq.js"
import axios from "axios"; //引入axios模块
import qs from 'qs';
// import store from '@/store/store.js'
import API from "./config.js"; //解构赋值获取到METHODS的值(请求类型)
import {
    message
} from '../message.js';
import {
    Notification
} from 'element-ui';
import router from '../router/router';

// var subtoken = `${store.state.subtoken }` 
// var usid = sessionStorage.getItem("usid") 

// 创建axios实例返回一个promise对象（） 完成配置项

const instance = axios.create({
    "baseURL": `/portal-api`,
    // "baseURL": `/`,
    // "baseURL": `${SERVER_IP_PORT}/`,
    // 它可以通过设置一个 `baseURL` 便于为 axios 实例的方法传递相对 URL
    //   baseURL: 'esa-ofms', //公共路径
    //   timeout: 50000, //超时时间
    // headers: { 'Content-Type': 'application/json',} //设置请求头
    //   headers: {'X-Custom-Header': 'foobar',
    // } //设置请求头
});

instance.interceptors.request.use(config => {
    // 动态设置请求头
    config.headers.common = {
        'Content-Type': 'application/json',
    };
    let tokenName,
        tokenValue;
    tokenName = sessionStorage.getItem("tokenName");
    tokenValue = sessionStorage.getItem("tokenValue");
    if (config.url == '/system/menu/menuTreeTableData'  //获取菜单
    ) {
        config.headers.common = {
            // 'Content-Type': 'application/json',
            tokenName: tokenValue

        };
    }


    return config;
}, error => {
    return Promise.reject(error);

});

// http response 拦截器
instance.interceptors.response.use(
    response => {
        let response_Result = response;
        if (response_Result.data.code == "401") { //token认证过期，请重新登录
            message({
                message: response_Result.data.msg,
                type: "error",
            });
            localStorage.clear();
            sessionStorage.clear();
            router.push('/login');
        }
        if (response_Result.code) { //导出这类接口没有code success
            switch (response_Result.data.code) {
                case "0000": //请求成功不管  要想谈成功提示框可以在界面进行提示   接口访问和读取数据正常
                    return response_Result;
                case 500: //用户信息访问移动apitoken超时
                    return response_Result;
                case 1111: //请求成功不管  要想谈成功提示框可以在界面进行提示   接口访问和读取数据正常
                    return response_Result;
                case 3333: //请求成功不管  要想谈成功提示框可以在界面进行提示   接口访问和读取数据正常
                    return response_Result;
                case 1030: //10011   请检查登录是否携带token

                    Notification({
                        title: "提示",
                        type: "error",
                        message: "登录认证失败：用户名或密码错误",
                        duration: 2500,
                    });
                    response_Result.data = {};
                    return response_Result;
                case 10011: //10011   请检查登录是否携带token
                    message({
                        duration: 1000,
                        message: response_Result.data.msg,
                        type: 'error',
                        onClose: res => {
                            //清空token并跳转
                            localStorage.clear();
                            sessionStorage.clear();
                            //   if(store.state.token){//如果是单点登录退回到login.vue就直接给转接到门户的登录页去
                            //window.location.href=global_variable.relogin_baseUrl
                           
                            this.$router.push('/login');
                            //   }else{
                            //     router.replace({
                            //       path: '/login',
                            //       query: {redirect: router.currentRoute.fullPath}//登录成功后跳入浏览的当前页面
                            //     })
                            //   }
                        }
                    });
                    response_Result.data = {};
                    return response_Result;
                case 10024: //10011   请检查登录是否携带token
                    message({
                        duration: 1000,
                        message: response_Result.data.msg,
                        type: 'error',
                        onClose: res => {
                            //清空token并跳转
                            localStorage.clear();
                            sessionStorage.clear();
                            //   if(store.state.token){//如果是单点登录退回到login.vue就直接给转接到门户的登录页去
                            //window.location.href=global_variable.relogin_baseUrl
                           
                            router.push('/login');
                            //   }else{
                            //     router.replace({
                            //       path: '/login',
                            //       query: {redirect: router.currentRoute.fullPath}//登录成功后跳入浏览的当前页面
                            //     })
                            //   }
                        }
                    });
                    response_Result.data = {};
                    return response_Result;
                case 10012: // 10012   token不能为空
                    //   message({
                    //     duration: 1000,
                    //     message: response_Result.data.msg,
                    //     type: 'error',
                    //     onClose: res => {
                    //       //清空token并跳转
                    //       localStorage.clear()
                    //       sessionStorage.clear()
                    //       //   if(store.state.token){//如果是单点登录退回到login.vue就直接给转接到门户的登录页去
                    //       //window.location.href=global_variable.relogin_baseUrl
                    //  
                    //       router.push('/login');
                    //       //   }else{
                    //       //     router.replace({
                    //       //       path: '/login',
                    //       //       query: {redirect: router.currentRoute.fullPath}//登录成功后跳入浏览的当前页面
                    //       //     })
                    //       //   }
                    //     }
                    //   })

                    Notification({
                        title: "提示",
                        type: "error",
                        message: response_Result.data.msg,
                        duration: 2500,
                    });
                    this.$router.push('/login');
                    response_Result.data = {};
                    return response_Result;
                case 10013: // 10013   会话超时，需重新登录
                    message({
                        duration: 1000,
                        message: response_Result.data.msg,
                        type: 'error',
                        onClose: res => {
                            //清空token并跳转
                            localStorage.clear();
                            sessionStorage.clear();
                            //   if(store.state.token){//如果是单点登录退回到login.vue就直接给转接到门户的登录页去
                            //window.location.href=global_variable.relogin_baseUrl
                            
                            this.$router.push('/login');
                            //   }else{
                            //     router.replace({
                            //       path: '/login',
                            //       query: {redirect: router.currentRoute.fullPath}//登录成功后跳入浏览的当前页面
                            //     })
                            //   }
                        }
                    });
                    response_Result.data = {};
                    return response_Result;
                case 10014: // 10014   当前用户无权限内容
                    message({
                        duration: 1000,
                        message: response_Result.data.msg,
                        type: 'error'
                    });
                    response_Result.data = {};
                    return response_Result;
                case 10015: // 10015   当前用户权限获取失败
                    message({
                        duration: 1000,
                        message: response_Result.data.msg,
                        type: 'error'
                    });
                    this.$router.push('/login');
                    response_Result.data = {};
                    return response_Result;
                default:
                    //默认接口请求报错  除code值为0000/10011/10012/10013/10014/10015之外  默认走这块
                    //先将默认接口给返回的信息msg获取到，用于做提示框信息，然后给准备返回的response数据其他请选择滞空，将status修改为40001
                    //这样接口就直接在第一个if判断code或者msg或者success字段时直接抛异常走catch方法
                    //就可以将页面当中其他员工写的请求接口错误的弹框信息和拦截器的判断重叠多次弹相同信息的问题完美解决
                    let message_content = response_Result.data.msg;
                    // response_Result = {
                    //     status: 40001
                    // };
                    //   message({
                    //     duration: 1000,
                    //     message: message_content,
                    //     type: 'error'
                    //   })
                    Notification({
                        title: "提示",
                        type: "error",
                        message: message_content,
                        duration: 2500,
                    });
                    return response_Result;
            }
        }
        return {
            response_Result,
            data: response_Result.data
        };
    },
    error => {
        if (error.response) {
            switch (error.response.status) {
                case 400:
                    message({
                        duration: 1000,
                        message: '请重新登录,正在前往登录页...',
                        type: 'warning',
                        onClose: res => {
                            //清空token并跳转
                            localStorage.clear();
                            sessionStorage.clear();
                            //   isShowMessage = true
                            // if(store.state.token){//如果是单点登录退回到login.vue就直接给转接到门户的登录页去
                            //window.location.href=global_variable.relogin_baseUrl
                    
                            router.push('/login');
                            // }else{
                            //   router.replace({
                            //     path: '/login',
                            //     query: {redirect: router.currentRoute.fullPath}//登录成功后跳入浏览的当前页面
                            //   })
                            // }
                        }
                    });
                    break;
                case 502:
                    message({
                        duration: 1000,
                        message: '接口请求500，数据获取失败，请联系管理员！',
                        type: 'error'
                    });
                    break;
            }
        }
        return Promise.reject(error);
    });

export default instance;
// 将 axios 挂载到 vue 构造器中
// Vue.prototype.$http = axios;
// instance.defaults.headers.common['Authorization'] = sessionStorage.getItem("usid");
// axios.interceptors.request.use(function (config) {
//     // 在发送请求之前做些什么
//     console.log(con)fif
//     return config;
//   }, function (error) {
//     // 对请求错误做些什么
//     return Promise.reject(error);
//   });
// axios.interceptors.request.use(config => {
//  config.cancelToken = new axios.CancelToken(function (cancel) {
//     store.commit('pushToken', {cancelToken: cancel})
//   })
// //       // 符合判断条件，做出响应处理，例如携带token
//       config.headers["subtoken"] = localStorage.getItem("subtoken")
// //       // 最后返回 config 代表继续发送请求

// },
// // 处理错误
// // err => Promise.reject()
// );

// axios.interceptors.request.use(config => {
// //   config.cancelToken = new axios.CancelToken(function (cancel) {
// //     store.commit('pushToken', {
// //       cancelToken: cancel
// //     })
// //   })
//   let config_url = config.url
//   // let isResetUrl=config_url.indexOf('\/api')
//   // if(isResetUrl>=0){
//   config.url = global_variable.request_uri_union_prefix + `${config_url}`
//   // }
//   if (store.state.xaiot_token) {
//     config.headers['usid'] = `${store.state.xaiot_token}`

//     if (config.method === 'post') {
//       if (config.headers['Content-Type'] === undefined)
//         config.headers['Content-Type'] = 'application/json'
//       else
//         config.headers['Content-Type'] = config.headers['Content-Type']
//       if (config.headers['Accept'] === undefined)
//         config.headers['Accept'] = 'application/json'
//       else
//         config.headers['Accept'] = config.headers['Accept']
//     }
//   }
//   return config;
// }, error => {
//   // 对请求错误做些什么
//   return Promise.reject(error);
// });
// http response 拦截器
// axios.interceptors.response.use(
//   response => {
//     let response_Result = response
//     if (response_Result.data.code) { //导出这类接口没有code success
//       switch (response_Result.data.code) {
//         case '0000': //请求成功不管  要想谈成功提示框可以在界面进行提示   接口访问和读取数据正常
//           return response_Result
//         case 500: //用户信息访问移动apitoken超时
//           return response_Result
//         case '1111': //请求成功不管  要想谈成功提示框可以在界面进行提示   接口访问和读取数据正常
//           return response_Result
//         case '3333': //请求成功不管  要想谈成功提示框可以在界面进行提示   接口访问和读取数据正常
//           return response_Result
//         case '10011': //10011   请检查登录是否携带token
//           message({
//             duration: 1000,
//             message: response_Result.data.msg,
//             type: 'error',
//             onClose: res => {
//               //清空token并跳转
//               localStorage.clear()
//               sessionStorage.clear()
//               //   if(store.state.token){//如果是单点登录退回到login.vue就直接给转接到门户的登录页去
//               //window.location.href=global_variable.relogin_baseUrl
//               console.log(111111111);
//               this.$router.push('/login');
//               //   }else{
//               //     router.replace({
//               //       path: '/login',
//               //       query: {redirect: router.currentRoute.fullPath}//登录成功后跳入浏览的当前页面
//               //     })
//               //   }
//             }
//           })
//           response_Result.data = {}
//           return response_Result
//         case '10012': // 10012   token不能为空
//           message({
//             duration: 1000,
//             message: response_Result.data.msg,
//             type: 'error',
//             onClose: res => {
//               //清空token并跳转
//               localStorage.clear()
//               sessionStorage.clear()
//               //   if(store.state.token){//如果是单点登录退回到login.vue就直接给转接到门户的登录页去
//               //window.location.href=global_variable.relogin_baseUrl
//               console.log(22222222222);
//               this.$router.push('/login');
//               //   }else{
//               //     router.replace({
//               //       path: '/login',
//               //       query: {redirect: router.currentRoute.fullPath}//登录成功后跳入浏览的当前页面
//               //     })
//               //   }
//             }
//           })
//           response_Result.data = {}
//           return response_Result
//         case '10013': // 10013   会话超时，需重新登录
//           message({
//             duration: 1000,
//             message: response_Result.data.msg,
//             type: 'error',
//             onClose: res => {
//               //清空token并跳转
//               localStorage.clear()
//               sessionStorage.clear()
//               //   if(store.state.token){//如果是单点登录退回到login.vue就直接给转接到门户的登录页去
//               //window.location.href=global_variable.relogin_baseUrl
//               console.log(333333333);
//               this.$router.push('/login');
//               //   }else{
//               //     router.replace({
//               //       path: '/login',
//               //       query: {redirect: router.currentRoute.fullPath}//登录成功后跳入浏览的当前页面
//               //     })
//               //   }
//             }
//           })
//           response_Result.data = {}
//           return response_Result
//         case '10014': // 10014   当前用户无权限内容
//           message({
//             duration: 1000,
//             message: response_Result.data.msg,
//             type: 'error'
//           })
//           response_Result.data = {}
//           return response_Result
//         case '10015': // 10015   当前用户权限获取失败
//           message({
//             duration: 1000,
//             message: response_Result.data.msg,
//             type: 'error'
//           })
//           response_Result.data = {}
//           return response_Result
//         default:
//           //默认接口请求报错  除code值为0000/10011/10012/10013/10014/10015之外  默认走这块
//           //先将默认接口给返回的信息msg获取到，用于做提示框信息，然后给准备返回的response数据其他请选择滞空，将status修改为40001
//           //这样接口就直接在第一个if判断code或者msg或者success字段时直接抛异常走catch方法
//           //就可以将页面当中其他员工写的请求接口错误的弹框信息和拦截器的判断重叠多次弹相同信息的问题完美解决
//           let message_content = response_Result.data.msg
//           response_Result = {
//             status: 40001
//           }
//           message({
//             duration: 1000,
//             message: message_content,
//             type: 'error'
//           })
//           return response_Result
//       }
//     }
//     return response_Result
//   },
//   error => {
//     if (error.response) {
//       switch (error.response.status) {
//         case 401:
//           message({
//             duration: 1000,
//             message: '请重新登录,正在前往登录页...',
//             type: 'warning',
//             onClose: res => {
//               //清空token并跳转
//               localStorage.clear()
//               sessionStorage.clear()
//               isShowMessage = true
//               // if(store.state.token){//如果是单点登录退回到login.vue就直接给转接到门户的登录页去
//               //window.location.href=global_variable.relogin_baseUrl
//               console.log(9999999999);
//               this.$router.push('/login');
//               // }else{
//               //   router.replace({
//               //     path: '/login',
//               //     query: {redirect: router.currentRoute.fullPath}//登录成功后跳入浏览的当前页面
//               //   })
//               // }
//             }
//           })
//           break;
//         case 500:
//           message({
//             duration: 1000,
//             message: '接口请求500，数据获取失败，请联系管理员！',
//             type: 'error'
//           })
//           break;
//       }
//     }
//     return Promise.reject(error)
//   })




//封装 判断请求方式 （POST、GET）
// export 抛出request 函数

// type:请求方式（POST、GET）
// url参数：请求地址
// params参数:请求的参数
export function request(type, url, params, headers = {}) {
    switch (type) {
        //当请求方式为post请求时，调用封装的post函数
        case API.METHODS.POST:
            return post(url, params, headers);
        //当请求方式为get请求时，调用封装的get函数
        case API.METHODS.GET:
            return get(url, params);
            //当请求方式为get请求时，调用封装的get函数
        case API.METHODS.DELETE:
            return Delete(url, params);
    }
}


//封装的get请求方式
// url参数：请求地址
// params参数:请求的参数
function get(url, params) {
    return instance.get(url, params);
}

//封装的post请求方式
// url参数：请求地址
// params参数:请求的参数
function post(url, params, headers = {}) {
    return instance.post(url, params, headers);
}

//封装的delete请求方式
// url参数：请求地址
// params参数:请求的参数
function Delete (url, params, headers = {}) {
    return instance.delete(url, params, headers);
}