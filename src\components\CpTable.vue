<template>
  <div class="cp-table">
    <ant-table
      ref="antTable"
      v-bind="{ ...$attrs }"
      :loading="loading"
      :data="data"
      :columns="columns"
      :check-width-empty="checkWidth"
      :empty_title="empty_title"
      :has-index="hasIndex"
      :check-list="checkList"
      :pagination="pagination"
      :total="total"
      v-on="{ ...$listeners }"
      @change="query(false)"
    >
      <span slot="first">
        <slot name="d" />
      </span>
      <slot />
      <template
        v-for="tableSlot in tableSlots"
        :slot="tableSlot.slotRender"
        slot-scope="slotData"
      >
        <slot v-bind="{ ...slotData }" :name="tableSlot.slotRender" />
      </template>
    </ant-table>
  </div>
</template>

<script>
import AntTable from "./AntTable";
import tableSearch from "@/mixins/tableSearch";
export default {
  name: "CpTable",
  components: {
    AntTable,
  },
  mixins: [tableSearch],
  props: {
    hasIndex: {
      type: [Boolean],
      default: false,
    },
    checkList: {
      type: [Boolean],
      default: false,
    },
    tableData: {
      type: Array,
      default: () => [],
    },
    columns: {
      type: Array,
      default: () => [],
    },
    tableSearch: {
      type: Function,
      default: () => ({}),
    },
    asyc: {
      type: Boolean,
      default: true,
    },
    allData: {
      type: Boolean,
      default: false,
    },
    tableSource: {
      type: String,
      default: "iotp",
    },
  },
  computed: {
    tableSlots() {
      const _columns = this.columns ? this.columns.slice() : [];
      return _columns.filter((column) => {
        return column.slotRender;
      });
    },
  },
  watch: {
    data() {
      if (this.allData) return;
      this.$emit("update:tableData", this.data);
    },
    tableData: {
      handler() {
        if (this.allData) {
          if (this.allData) {
            this.__tableData = this.tableData || [];
          }
          this.query(true);
          return;
        }
        this.data = this.tableData;
      },
      immediate: true,
    },
  },
  mounted() {
    if (this.asyc) this.query(true);
  },
  methods: {
    clearSelection() {
      this.$refs.antTable.$refs.table.clearSelection(...arguments);
    },
    toggleRowSelection() {
      this.$refs.antTable.$refs.table.toggleRowSelection(...arguments);
    },
    toggleAllSelection() {
      this.$refs.antTable.$refs.table.toggleAllSelection(...arguments);
    },
    toggleRowExpansion() {
      this.$refs.antTable.$refs.table.toggleRowExpansion(...arguments);
    },
    setCurrentRow() {
      this.$refs.antTable.$refs.table.setCurrentRow(...arguments);
    },
    clearSort() {
      this.$refs.antTable.$refs.table.clearSort(...arguments);
    },
    clearFilter() {
      this.$refs.antTable.$refs.table.clearFilter(...arguments);
    },
    doLayout() {
      this.$refs.antTable.$refs.table.doLayout(...arguments);
    },
    sort() {
      this.$refs.antTable.$refs.table.sort(...arguments);
    },
  },
};
</script>

<style lang="scss"></style>
