.cspt {
  .el-month-table td .cell {
    color: #fff !important;
  }
  .el-month-table td.in-range div,
  .el-month-table td.in-range div:hover,
  .el-month-table.is-week-mode .el-month-table__row.current div,
  .el-month-table.is-week-mode .el-month-table__row:hover div {
    background-color: #080d31;
  }

  .el-month-editor .el-range__icon {
    display: none;
  }

  .el-month-editor .el-range__close-icon {
    margin-left: -15px;
  }

  .el-picker-panel,
  .el-month-table th {
    color: #fff;
  }

  .el-month-table td.today span,
  .el-month-table td.today span:hover {
    color: #3ce7f3;
  }
  .el-form-item__label {
    color: transparentize(#fff, 0.8);
    padding-right: 10px !important;
    text-align: right;
    width: 90px;
  }

  .content-area {
    // background: url("~@/assets/page-bg.png") center center;
    .el-form {
      margin-bottom: 15px;

      .el-form-item {
        .el-textarea__inner {
          background-color: #074291;
        }

        .el-date-editor .el-range-input {
          width: 121px;
        }

        .el-input {
          width: 266px;
          box-sizing: border-box;
          background: #090a2b !important;
          border: 1px solid #074291 !important;
          border-radius: 3px;
          height: 34px;
          padding: 0px;
          line-height: 30px;

          .el-input__inner {
            display: inline-block;
            height: 30px;
            // margin: 0 4px -1px !important;
            color: #fff;
            box-sizing: border-box;
            // line-height: 40px;
            background: #090a2b !important;
            // background-color: #074291;
            border: none !important;
            border-radius: 0;
          }
        }

        // .el-select .el-input .el-select__caret {
        //   // height: 30px !important;
        //   // margin-top: 6px;
        // }

        .el-select__caret {
          line-height: 34px;
        }

        .el-select .el-input .el-select__caret::before,
        .el-select__caret::after {
          font-size: 16px;
          // content: '\e78f';
          color: #00ffff !important;
        }

        .el-form-item__label {
          margin-left: 10px;
          color: #fff;
          font-size: 15px;
        }

        .el-input__inner::placeholder {
          color: rgba(255, 255, 255, 0.5);
          font-size: 14px;
        }
      }
    }
  }

  .content-container {
    padding: 0px 40px;
    text-align: left;
    color: #fff;
    @extend .content-area;
    // background: url("~@/assets/page-bg.png") center center;
    // background-size: 100% auto;
    .el-tabs__nav.is-top {
      .el-tabs__active-bar {
        background-color: rgb(0, 255, 255);
      }

      .el-tabs__item {
        color: #fff;

        &.is-active {
          color: rgb(0, 255, 255);
        }
      }
    }
  }

  .el-scrollbar__wrap {
    //   background-color: #074291 !important;
    border: none;
  }

  .el-select-dropdown__item {
    color: #fff;
  }

  .el-select-dropdown__item.hover,
  .el-select-dropdown__item:hover {
    background-color: #080d31 !important;
  }

  .search-button {
    padding: 0px 30px;
    height: 35px;
    border: none;
    margin-left: 20px;
    font-size: 18px;
    line-height: 17px;
    color: #fff;
    background-color: #00cbfe;

    span {
      background: none;
    }
  }

  .el-date-editor--daterange.el-input,
  .el-date-editor--daterange.el-input__inner,
  .el-date-editor--timerange.el-input,
  .el-date-editor--timerange.el-input__inner {
    width: 266px;
    height: 34px;
    // background: url('../assets/切图/input.png') no-repeat center center !important;
    border: 1px solid #074291 !important;
    background: #090a2b !important;
    border-radius: 3px;
    color: #fff;
  }

  .el-range-separator {
    line-height: 24px !important;
  }

  .el-date-editor .el-range-editor .el-input__inner .el-date-editor--daterange {
    background-color: #074291 !important;
  }

  .el-range-editor.el-input__inner {
    padding-left: 4px;
  }

  .el-date-editor .el-range-input {
    color: #fff !important;
    width: 121px;
    background: #090a2b !important;
  }

  .el-range-separator {
    // background-color: #074291 !important;
    color: #fff !important;
  }

  .el-range-separator,
  .el-date-table td.in-range div,
  .el-date-table td.in-range div:hover,
  .el-date-table.is-week-mode .el-date-table__row.current div,
  .el-date-table.is-week-mode .el-date-table__row:hover div {
    background-color: #080d31;
  }

  .el-date-editor .el-range__icon {
    display: none;
  }

  .el-date-editor .el-range__close-icon {
    margin-left: -15px;
  }

  .el-picker-panel,
  .el-date-table th {
    color: #fff;
  }

  .el-date-table td.today span,
  .el-date-table td.today span:hover {
    color: #3ce7f3;
  }

  .el-button:hover {
    color: #fff;
  }

  .add-button {
    // width: 117px;
    // height: 42px;
    // border: none;
    // // margin-left: 10px;
    // font-size: 18px;
    // background: url('../assets/切图/新增.png') no-repeat center !important;
    // color: #fff;

    // span {
    //   background: none;
    // }
    @extend .search-button;
  }

  .el-table__row,
  .el-table__body-wrapper {
    background-color: #080d31 !important;
  }

  .el-table_row {
    height: 54px !important;
  }

  .el-table__fixed-right::before,
  .el-table__fixed::before {
    background-color: rgba(31, 85, 218, 0.71);
  }

  .el-pagination {
    .el-pagination__sizes {
      .el-input__inner {
        width: 100px !important;
        border: 1px solid rgba(31, 85, 218, 0.71);
      }

      .el-icon-arrow-up:before {
        content: '\e78f';
      }
    }

    button:disabled {
      background-color: transparent !important;
    }

    .el-pager {
      .number {
        background-color: transparent;
        color: #fff;
      }
    }

    .el-pagination__jump {
      .el-input__inner {
        width: 40px !important;
        border: 1px solid rgba(31, 85, 218, 0.71);
      }
    }
  }

  .el-pagination__total,
  .el-pagination__jump {
    color: #fff !important;
  }

  .el-alert--warning.is-light {
    background-color: #1c375c;
    border: 1px solid #e6a23c;
    border-radius: 0;
    height: 30px;
  }

  .el-alert__content,
  .el-alert__icon,
  .el-icon-warning {
    background: none;
  }

  .el-dialog {
    background-color: #080d31;
    box-shadow: 0 0 27px 0 #5c6e87;
  }

  .el-dialog__body {
    padding: 10px 20px;

    .el-input__inner {
      width: 260px;
      color: #fff !important;
      border-color: #5c6e87;
      height: 30px;
      border-radius: 0;
      background-color: #1c375c;
      font-size: 16px;
      color: #ffffff;
    }

    .el-input__inner::placeholder {
      color: #5c6e87;
      font-size: 14px;
    }
  }

  .el-dialog__title {
    // background-color: #080d31;
    color: #fff;
  }

  .el-dialog__wrapper {
    background-color: rgba(0, 0, 0, 0.5) !important;
  }

  .el-alert__title {
    background-color: #1c375c !important;
  }

  .el-alert__closebtn {
    display: none;
  }

  .form-button {
    margin-top: 20px;

    .el-button {
      width: 70px;
      height: 30px;
      line-height: 0;
      border-radius: 0;
      margin-left: 20px;
      box-sizing: border-box;

      span {
        // margin-top: 10px;
        background: none;
      }
    }
  }

  .el-radio {
    color: #fff;
  }

  .card_title_word {
    font-weight: 700 !important;
    font-style: normal;
    color: #00e4ed;
    padding-left: 1rem;
  }

  .el-button--default:hover {
    color: #606266;
  }

  @import '../components/element-dart/dart-theme.scss';

  .select-dart {
    position: relative;
    font-size: 14px;
    display: inline-block;
    padding: 4px;
    background-color: $--dart-input-wrap-backgrond;
    border: 1px solid $--dart-input-wrap-border-color;
    box-sizing: border-box;
    line-height: 27px;

    ::-webkit-input-placeholder {
      color: $--icon-color !important;
      font-size: 15px;
      position: relative;
      bottom: 2px;
    }

    :-moz-placeholder {
      color: $--icon-color;
      font-size: 15px;
      position: relative;
      bottom: 2px;
    }

    ::-moz-placeholder {
      color: $--icon-color;
      font-size: 15px;
      position: relative;
      bottom: 2px;
    }

    :-ms-input-placeholder {
      color: $--icon-color;
      font-size: 15px;
      position: relative;
      bottom: 2px;
    }

    img {
      position: absolute;
      width: 7px;

      &.left-bottom {
        left: -1px;
        bottom: -1px;
      }

      &.left-top {
        left: -1px;
        top: -1px;
      }

      &.right-bottom {
        right: -1px;
        bottom: -1px;
      }

      &.right-top {
        right: -1px;
        top: -1px;
      }
    }

    .el-select__caret {
      color: $--icon-color !important;
    }

    .el-input__inner {
      border-radius: 0px !important;
      box-sizing: border-box;
      border: 1px solid $--dart-backgrond !important;
      background-color: $--dart-input-backgrond !important;
      color: $--dart-input-text-color !important;
      font-size: 18px;
      font-family: 微软雅黑;
    }
  }

  .el-popper {
    background-color: $--dart-input-backgrond;
    border: 1px solid $--dart-input-wrap-border-color;
    $dir: top, left, right, bottom;

    .el-select-dropdown__item {
      color: $--dart-input-text-color;

      &.hover {
        background-color: transparentize($--dart-backgrond, 0.5);
      }
    }

    .el-select-dropdown__empty {
      color: $--icon-color;
    }

    .el-picker-panel__icon-btn {
      color: $--color-white !important;
    }

    .el-picker-panel {
      .el-picker-panel__body {
        .el-picker-panel__content {
          color: $--color-white !important;

          .el-date-range-picker__header {
            color: $--color-white !important;
          }
        }
      }
    }

    @each $currentDir in $dir {
      & {
        color: $currentDir;
      }

      &[x-placement^='#{$currentDir}'] .popper__arrow {
        border-#{$currentDir}-color: $--dart-backgrond;

        &::after {
          border-#{$currentDir}-color: $--dart-input-wrap-border-color;
        }
      }
    }
  }

  .pagination-wrapper {
    margin-top: 10px;
    text-align: right;

    .el-pagination.is-background .el-pager li.btn-quickprev {
      color: #fff !important;
      background-color: transparent !important;
    }

    .el-pager {
      .number,
      .btn-quickprev {
        color: #fff !important;
        background-color: transparent !important;

        &.active {
          background-color: #00cbfe !important;
        }
      }
    }

    .btn-prev,
    .btn-next {
      background-color: transparent;
      color: #fff;
    }

    .el-input__inner {
      color: #fff;
    }

    button {
      color: #fff !important;
      background-color: transparent !important;
    }

    button:disabled {
      background-color: transparent !important;
    }

    .el-input__inner {
      background-color: transparent;
    }
  }

  .cp-table {
    .el-table__header {
      width: 100%;
    }

    .el-table td.el-table__cell {
      font-family: 微软雅黑;
      font-size: 14px;
      color: #fff;
      border-bottom: 1px solid rgba(31, 85, 218, 0.71);
      height: 54px;
    }

    .el-table th.el-table__cell.is-leaf {
      font-family: 微软雅黑;
      background-color: #021c7e;
      font-size: 14px;
      color: #fff;
      border-bottom: 1px solid rgba(31, 85, 218, 0.71);
      height: 45px;
    }

    .el-table td.el-table__cell {
      &.table-index {
        // width: 65px !important;

        .cell {
          // width: 65px !important;
          text-align: center;
        }
      }

      &.table-check {
        // width: 50px !important;

        .cell {
          // width: 50px !important;
          text-align: center;
        }
      }
    }

    font-family: 微软雅黑;

    .el-table td.el-table__cell {
      color: #fff;
      border-bottom: 1px solid #2f4384 !important;
    }

    .el-table,
    .el-table__expanded-cell {
      background-color: #021c7e;
    }

    .el-table--border::after,
    .el-table--group::after,
    .el-table__fixed-right::before,
    .el-table::before {
      background-color: #2f4384 !important;
    }

    .el-table__row,
    .el-table__body-wrapper {
      background-color: #080d31 !important;
    }

    td.el-table__cell {
      background-color: #080d31 !important;
    }

    .el-table--enable-row-hover .el-table__body tr:hover > td {
      background-color: transparentize(#031d85, 0.8) !important;
    }

    .el-table__body tr.hover-row.current-row > td.el-table__cell,
    .el-table__body
      tr.hover-row.el-table__row--striped.current-row
      > td.el-table__cell,
    .el-table__body tr.hover-row.el-table__row--striped > td.el-table__cell,
    .el-table__body tr.hover-row > td.el-table__cell {
      background-color: transparent;
    }

    .el-table__row.hover-row {
      background-color: transparentize(#031d85, 0.8) !important;
    }

    .el-table tr {
      background-color: #080d31 !important;
    }

    .el-checkbox__inner {
      background-color: #011c8d;
      border: 1px solid #818dbf;
    }
  }

  .card-title {
    padding-left: 8px;
    font-family: 微软雅黑;
    position: relative;
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 10px;
    color: #fff;

    &::before {
      content: '';
      height: 80%;
      width: 3px;
      position: absolute;
      left: 0px;
      top: 50%;
      transform: translateY(-50%);
      background-color: #00d1fe;
    }
  }

  .btn-tabs {
    width: auto;
    float: right;
    display: inline-block;
    height: 30px;
    border: 1px solid rgba(0, 203, 254, 0.6);
    border-right: 1px solid transparent;
    box-sizing: border-box;
    display: flex;
    background-color: #071126;
    margin-bottom: 10px;
    line-height: 30px;
    float: left;
    text-align: center;

    &.right {
      float: right;
    }

    &.left {
      float: left;
    }

    .tab {
      width: 70px;
      height: 100%;
      color: #fff;
      border-right: 1px solid rgba(0, 203, 254, 0.6);
      cursor: pointer;

      &.active {
        background-color: #00cbfe;
      }
    }
  }
}

.cspt {
  .el-form-item__label {
    color: transparentize(#fff, 0.8);
    padding-right: 10px !important;
    text-align: right;
  }

  .content-area {
    .el-form-item {
      margin-bottom: 10px;

      .el-textarea__inner {
        background-color: #074291;
      }

      .el-date-editor .el-range-input {
        width: 121px;
      }

      .el-input {
        width: 266px;
        box-sizing: border-box;
        background: #090a2b !important;
        border: 1px solid #074291 !important;
        border-radius: 3px;
        height: 34px;
        padding: 0px;
        line-height: 30px;

        .el-input__inner {
          display: inline-block;
          height: 30px;
          // margin: 0 4px -1px !important;
          color: #fff;
          box-sizing: border-box;
          // line-height: 40px;
          background: #090a2b !important;
          // background-color: #074291;
          border: none !important;
          border-radius: 0;
        }
      }

      // .el-select .el-input .el-select__caret {
      //   // height: 30px !important;
      //   // margin-top: 6px;
      // }

      .el-select__caret {
        line-height: 34px;
      }

      .el-select .el-input .el-select__caret::before,
      .el-select__caret::after {
        font-size: 16px;
        // content: '\e78f';
        color: #00ffff !important;
      }

      .el-form-item__label {
        margin-left: 10px;
        color: #fff;
        font-size: 16px;
      }

      .el-input__inner::placeholder {
        color: rgba(255, 255, 255, 0.5);
        font-size: 14px;
      }
    }
  }

  .content-container {
    padding: 0px 40px;
    text-align: left;
    color: #fff;
    @extend .content-area;
    // background: url("~@/assets/page-bg.png") center center no-repeat;
    /* margin-top: 60px; */
    background-size: 100% 100%;

    // background: url("~@/assets/page-bg.png") center center;
    // background-size: 100% auto;
    .el-tabs__nav.is-top {
      .el-tabs__active-bar {
        background-color: rgb(0, 255, 255);
      }

      .el-tabs__item {
        color: #fff;

        &.is-active {
          color: rgb(0, 255, 255);
        }
      }
    }
  }

  .el-scrollbar__wrap {
    background-color: #074291 !important;
    border: none;
  }

  .el-select-dropdown__item {
    color: #fff;
  }

  .el-select-dropdown__item.hover,
  .el-select-dropdown__item:hover {
    background-color: #080d31 !important;
  }

  .search-button {
    padding: 0px 20px;
    height: 34px;
    border: none;
    margin-left: 20px;
    font-size: 15px;
    line-height: 17px;
    color: #fff;
    background-color: #00cbfe;

    span {
      background: none;
    }
  }

  .el-date-editor--daterange.el-input,
  .el-date-editor--daterange.el-input__inner,
  .el-date-editor--timerange.el-input,
  .el-date-editor--timerange.el-input__inner {
    width: 266px;
    height: 34px;
    // background: url('../assets/切图/input.png') no-repeat center center !important;
    border: 1px solid #074291 !important;
    background: #090a2b !important;
    border-radius: 3px;
    color: #fff;
  }

  .el-range-separator {
    line-height: 24px !important;
  }

  .el-date-editor .el-range-editor .el-input__inner .el-date-editor--daterange {
    background-color: #074291 !important;
  }

  .el-range-editor.el-input__inner {
    padding-left: 4px;
  }

  .el-date-editor .el-range-input {
    color: #fff !important;
    width: 121px;
    background: #090a2b !important;
  }

  .el-range-separator {
    // background-color: #074291 !important;
    color: #fff !important;
  }

  .el-range-separator,
  .el-date-table td.in-range div,
  .el-date-table td.in-range div:hover,
  .el-date-table.is-week-mode .el-date-table__row.current div,
  .el-date-table.is-week-mode .el-date-table__row:hover div {
    background-color: #080d31;
  }

  .el-date-editor .el-range__icon {
    display: none;
  }

  .el-date-editor .el-range__close-icon {
    margin-left: -15px;
  }

  .el-picker-panel,
  .el-date-table th {
    color: #fff;
  }

  .el-date-table td.today span,
  .el-date-table td.today span:hover {
    color: #3ce7f3;
  }

  .el-table__header {
    width: 100%;
  }

  .el-table td.el-table__cell {
    font-family: 微软雅黑;
    font-size: 14px;
    color: #fff;
    border-bottom: 1px solid rgba(31, 85, 218, 0.71);
    height: 54px;
  }

  .el-table th.el-table__cell.is-leaf {
    font-family: 微软雅黑;
    background-color: #021c7e;
    font-size: 14px;
    color: #fff;
    border-bottom: 1px solid rgba(31, 85, 218, 0.71);
    height: 45px;
  }

  .el-button:hover {
    color: #fff;
  }

  .add-button {
    // width: 117px;
    // height: 42px;
    // border: none;
    // // margin-left: 10px;
    // font-size: 18px;
    // background: url('../assets/切图/新增.png') no-repeat center !important;
    // color: #fff;

    // span {
    //   background: none;
    // }
    @extend .search-button;
  }

  .el-table__row,
  .el-table__body-wrapper {
    background-color: transparent !important;
  }

  .el-table_row {
    height: 54px !important;
  }

  .el-table__fixed-right::before,
  .el-table__fixed::before {
    background-color: rgba(31, 85, 218, 0.71);
  }

  .el-pagination {
    .el-pagination__sizes {
      .el-input__inner {
        width: 100px !important;
        border: 1px solid rgba(31, 85, 218, 0.71);
      }

      .el-icon-arrow-up:before {
        content: '\e78f';
      }
    }

    button:disabled {
      background-color: transparent !important;
    }

    .el-pager {
      .number {
        background-color: #021c7e;
        color: #fff;
      }
    }

    .el-pagination__jump {
      .el-input__inner {
        width: 40px !important;
        border: 1px solid rgba(31, 85, 218, 0.71);
      }
    }
  }

  .el-pagination__total,
  .el-pagination__jump {
    color: #fff !important;
  }

  .el-alert--warning.is-light {
    background-color: #1c375c;
    border: 1px solid #e6a23c;
    border-radius: 0;
    height: 30px;
  }

  .el-alert__content,
  .el-alert__icon,
  .el-icon-warning {
    background: none;
  }

  .el-dialog {
    background-color: #080d31;
    box-shadow: 0 0 27px 0 #5c6e87;
  }

  .el-dialog__body {
    padding: 10px 20px 20px;

    .el-form-item__label {
      color: #fff !important;
    }

    .el-input__inner {
      width: 260px;
      color: #fff !important;
      border-color: #5c6e87;
      height: 30px;
      border-radius: 0;
      background-color: #1c375c;
      font-size: 16px;
      color: #ffffff;
    }

    .el-input__inner::placeholder {
      color: #5c6e87;
      font-size: 14px;
    }
  }

  .el-dialog__title {
    // background-color: #080d31;
    color: #fff;
  }

  .el-dialog__wrapper {
    background-color: rgba(0, 0, 0, 0.5) !important;
  }

  .el-alert__title {
    background-color: #1c375c !important;
  }

  .el-alert__closebtn {
    display: none;
  }

  .form-button {
    margin-top: 20px;

    .el-button {
      width: 70px;
      height: 30px;
      line-height: 0;
      border-radius: 0;
      margin-left: 20px;
      box-sizing: border-box;

      span {
        // margin-top: 10px;
        background: none;
      }
    }
  }

  .el-radio {
    color: #fff;
  }

  .card_title_word {
    font-weight: 700 !important;
    font-style: normal;
    color: #00e4ed;
    padding-left: 1rem;
  }

  .el-button--default:hover {
    color: #606266;
  }
  .el-dropdown {
    color: transparent
  }

  @import '../components/element-dart/dart-theme.scss';

  .select-dart {
    position: relative;
    font-size: 14px;
    display: inline-block;
    padding: 4px;
    background-color: $--dart-input-wrap-backgrond;
    border: 1px solid $--dart-input-wrap-border-color;
    box-sizing: border-box;
    line-height: 27px;

    ::-webkit-input-placeholder {
      color: $--icon-color !important;
      font-size: 15px;
      position: relative;
      bottom: 2px;
    }

    :-moz-placeholder {
      color: $--icon-color;
      font-size: 15px;
      position: relative;
      bottom: 2px;
    }

    ::-moz-placeholder {
      color: $--icon-color;
      font-size: 15px;
      position: relative;
      bottom: 2px;
    }

    :-ms-input-placeholder {
      color: $--icon-color;
      font-size: 15px;
      position: relative;
      bottom: 2px;
    }

    img {
      position: absolute;
      width: 7px;

      &.left-bottom {
        left: -1px;
        bottom: -1px;
      }

      &.left-top {
        left: -1px;
        top: -1px;
      }

      &.right-bottom {
        right: -1px;
        bottom: -1px;
      }

      &.right-top {
        right: -1px;
        top: -1px;
      }
    }

    .el-select__caret {
      color: $--icon-color !important;
    }

    .el-input__inner {
      border-radius: 0px !important;
      box-sizing: border-box;
      border: 1px solid $--dart-backgrond !important;
      background-color: $--dart-input-backgrond !important;
      color: $--dart-input-text-color !important;
      font-size: 18px;
      font-family: 微软雅黑;
    }
  }

  .el-popper {
    background-color: $--dart-input-backgrond;
    border: 1px solid $--dart-input-wrap-border-color;
    $dir: top, left, right, bottom;

    .el-select-dropdown__item {
      color: $--dart-input-text-color;

      &.hover {
        background-color: transparentize($--dart-backgrond, 0.5);
      }
    }

    .el-select-dropdown__empty {
      color: $--icon-color;
    }

    .el-picker-panel__icon-btn {
      color: $--color-white !important;
    }

    .el-picker-panel {
      .el-picker-panel__body {
        .el-picker-panel__content {
          color: $--color-white !important;

          .el-date-range-picker__header {
            color: $--color-white !important;
          }
        }
      }
    }

    @each $currentDir in $dir {
      & {
        color: $currentDir;
      }

      &[x-placement^='#{$currentDir}'] .popper__arrow {
        border-#{$currentDir}-color: $--dart-backgrond;

        &::after {
          border-#{$currentDir}-color: $--dart-input-wrap-border-color;
        }
      }
    }
  }

  .pagination-wrapper {
    margin-top: 10px;
    text-align: right;

    .el-pagination.is-background .el-pager li {
      background-color: transparent;
    }

    .el-pager {
      .number {
        color: #fff !important;
        background-color: transparent !important;

        &.active {
          background-color: #00cbfe !important;
        }
      }
    }

    .btn-prev,
    .btn-next {
      background-color: transparent;
      color: #fff;
    }

    .el-input__inner {
      color: #fff;
    }

    button {
      color: #fff !important;
      background-color: transparent !important;
    }

    button:disabled {
      background-color: transparent !important;
    }

    .el-input__inner {
      background-color: transparent;
    }
  }

  .cp-table {
    font-family: 微软雅黑;

    .el-table--small .el-table__cell {
      padding: 12px 0 !important;
    }

    .el-table td.el-table__cell {
      color: #fff;
      border-bottom: 1px solid #2f4384 !important;
    }

    .el-table,
    .el-table__expanded-cell {
      background-color: transparent;
    }

    .el-table--border::after,
    .el-table--group::after,
    .el-table__fixed-right::before,
    .el-table::before {
      background-color: #2f4384 !important;
    }

    .el-table__row,
    .el-table__body-wrapper {
      background-color: transparent !important;
    }

    .el-table__row td.el-table__cell {
      background-color: transparent !important;
    }

    .el-table--enable-row-hover .el-table__body tr:hover > td {
      background-color: transparentize(#031d85, 0.8) !important;
    }

    .el-table__body tr.hover-row.current-row > td.el-table__cell,
    .el-table__body
      tr.hover-row.el-table__row--striped.current-row
      > td.el-table__cell,
    .el-table__body tr.hover-row.el-table__row--striped > td.el-table__cell,
    .el-table__body tr.hover-row > td.el-table__cell {
      background-color: transparent;
    }

    .el-table__row.hover-row {
      background-color: transparentize(#031d85, 0.8) !important;
    }

    .el-table tr {
      background-color: transparent !important;
    }

    .el-checkbox__inner {
      background-color: #011c8d;
      border: 1px solid #818dbf;
    }
  }

  .card-title {
    padding-left: 8px;
    font-family: 微软雅黑;
    position: relative;
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 10px;
    color: #fff;

    &::before {
      content: '';
      height: 80%;
      width: 3px;
      position: absolute;
      left: 0px;
      top: 50%;
      transform: translateY(-50%);
      background-color: #00d1fe;
    }
  }

  .btn-tabs {
    width: auto;
    float: right;
    display: inline-block;
    height: 30px;
    border: 1px solid rgba(0, 203, 254, 0.6);
    border-right: 1px solid transparent;
    box-sizing: border-box;
    display: flex;
    background-color: #071126;
    margin-bottom: 10px;
    line-height: 30px;
    float: left;
    text-align: center;

    &.right {
      float: right;
    }

    &.left {
      float: left;
    }

    .tab {
      width: 70px;
      height: 100%;
      color: #fff;
      border-right: 1px solid rgba(0, 203, 254, 0.6);
      cursor: pointer;

      &.active {
        background-color: #00cbfe;
      }
    }
  }
}

.ombs {
  .el-table th.el-table__cell.is-leaf {
    font-family: 微软雅黑;
    color: #333;
  }
}

#home_obms {
  .cell {
    white-space: pre-line !important;
  }
}

.el-message-box__btns {
  .el-button {
    margin-left: 20px !important;
  }
}
.net-mon-report-container {
  .el-date-editor--monthrange.el-input,
  .el-date-editor--monthrange.el-input__inner {
    width: 266px;
    height: 34px;
    // background: url('../assets/切图/input.png') no-repeat center center !important;
    border: 1px solid #074291 !important;
    background: #090a2b !important;
    border-radius: 3px;
    color: #fff;
  }
  .el-date-editor
    .el-range-editor
    .el-input__inner
    .el-date-editor--monthrange {
    background-color: #074291 !important;
  }

  .el-range-separator,
  .el-month-table td.in-range div,
  .el-month-table td.in-range div:hover,
  .el-month-table.is-week-mode .el-month-table__row.current div,
  .el-month-table.is-week-mode .el-month-table__row:hover div {
    background-color: #080d31;
  }

  .el-month-editor .el-range__icon {
    display: none;
  }

  .el-month-editor .el-range__close-icon {
    margin-left: -15px;
  }

  .el-picker-panel,
  .el-month-table th {
    color: #fff;
  }

  .el-month-table td.today span,
  .el-month-table td.today span:hover {
    color: #3ce7f3;
  }
}
