<template>
  <!-- 重置密码 -->
  <div class="reset-password">
    <el-form
      ref="resetPassword"
      v-loading.fullscreen.lock="loading"
      element-loading-text="更改密码中"
      element-loading-background="rgba(0, 0, 0, 0.4)"
      label-width="120px"
      :rules="rules"
      :model="formData"
    >
      <el-form-item
        v-if="ruserId === baseinfo.userid"
        label="原密码："
        prop="password"
      >
        <el-input
          v-model="formData.password"
          type="password"
          placeholder="原密码"
        />
      </el-form-item>
      <el-form-item
        label="新密码："
        prop="nmmpd"
      >
        <el-input
          v-model="formData.nmmpd"
          type="password"
          placeholder="新密码"
        />
      </el-form-item>
      <el-form-item
        label="确认密码："
        prop="rmmpd"
      >
        <el-input
          v-model="formData.rmmpd"
          type="password"
          placeholder="确认密码"
        />
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { encrypt, decrypt } from "@/utils/util";
// import { updateUserPassword } from "@/apis/api-user";
// import { updateDuserPassword, loginout } from "@/apis/api-login";
import { isComplexPassword } from "@/utils/validate";
const baseinfo = JSON.parse(localStorage.getItem("baseinfo") || "{}");
export default {
  name: "ResetPassword",
  components: {},
  props: {
    ruserId: {
      type: [String, Number],
      default: "",
    },
  },
  data() {
    return {
      loading: false,
      formData: {
        nmmpd: "",
        rmmpd: "",
        password: "",
      },
      baseinfo,
      rules: {
        password: [
          { required: true, message: "请输入原密码", trigger: "blur" },
        ],
        nmmpd: [
          { required: true, message: "请输入密码", trigger: "blur" },
          {
            validator: (rule, value, callback) => {
              if (!isComplexPassword(value)) {
                callback(new Error("密码必须大于八位，且包含大写字母、小写字母、数字、特殊字符中的任意三类"));
              } else {
                callback();
              }
            },
            trigger: "blur",
          },
        ],
        rmmpd: [
          { required: true, message: "请再次输入密码", trigger: "blur" },
          {
            validator: (rule, value, callback) => {
              if (this.formData.nmmpd !== this.formData.rmmpd) {
                callback(new Error("两次密码不一致"));
              } else {
                callback();
              }
            },
            trigger: "blur",
          },
        ],
      },
    };
  },
  methods: {
    async updateAccountUserPassword() {
      const result = await updateUserPassword({
        nmmpd: encrypt(this.formData.nmmpd),
        rmmpd: encrypt(this.formData.rmmpd),
        ruserId: this.ruserId,
      });
      this.loading = false;
      if (!result.data.success) return null;
      this.$message({
        type: "success",
        message: "修改成功!",
      });
      return true;
    },
    async updateSelfPassword() {
      const result = await updateDuserPassword({
        password: encrypt(this.formData.password),
        newpassword: encrypt(this.formData.nmmpd),
        userid: this.ruserId,
      });
      this.loading = false;
      if (!result.data.success) return null;
      await loginout();
      localStorage.clear();
      window.open("/login.html", "_self");
    },
    async updateUserPassword() {
      const resultValidater = await this.validate();
      if (!resultValidater) return null;
      this.loading = true;
      if (this.ruserId === baseinfo.userid) {
        // return this.updateSelfPassword();
      } else {
        // return this.updateAccountUserPassword();
      }
    },
    validate() {
      return new Promise((resolve, reject) => {
        this.$refs.resetPassword.validate((valid) => {
          resolve(valid);
        });
      });
    },
  },
};
</script>

<style  lang='scss'>
.reset-password {
 .el-input__inner {
    width: 100% !important;
  }
}
</style>
