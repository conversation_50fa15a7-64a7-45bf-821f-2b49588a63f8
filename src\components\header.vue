<template>
  <div id="header">
    <div class="logo_img_box logo_img">
      <!-- <div class="block_1 flex-row justify-between"> -->
      <!-- <div class="block_2 flex-col"></div> -->
      <!-- <span class="text_1">统一门户</span> -->
      <!-- </div> -->
    </div>
  </div>
</template>
<script>
export default {
  name: "",
  components: {},
  props: {},
  data() {
    return {};
  },
  computed: {},
  watch: {
    $route: "onRouteChanged",
  },
  created() {},
  mounted() {},
  activited() {},
  update() {},
  beforeRouteUpdate() {},
  methods: {},

  filter: {},
};
</script>

<style >
.logo_img_box {
  width: 100%;
  height: 80px;
  text-align: center;
  background: url("../assets/featuresPage/top1.png") center center no-repeat;
  background-size: 100%;
}
.text_1 {
  width: 100%;
  height: 78px;
  font-size: 32px;
  font-family: PangMenZhengDao;
  color: #ffffff;
  line-height: 78px;
  text-align: center;
}
</style>
