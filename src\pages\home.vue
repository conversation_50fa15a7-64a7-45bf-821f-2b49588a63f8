<template>
  <div class="home" id="home" style="overflow-y: hidden;">
    <div class="header-long">
      <span class="header-right-middle">
        {{ title }}
      </span>
      <!-- <span class="imgLTSpan">
        <div class="imgLT"></div>
      </span> -->
      <!-- <span class="dateSpan">
        <div class="date">{{ dateYear }} {{ dateDay }}</div>
      </span> -->
    </div>
    <div class="btnDiv">
      <el-button plain class="btnBack" @click="back">返回上级</el-button>
      <el-button plain class="btnBack" v-for="(item, index) in btnArr" :key="index"
        @click="tableSearch(item.userId, '1', 10, 1)">
        {{ item.userGroupName }}</el-button>
      <!-- <el-button plain class="btnBack" @click="tableSearch(id, '2', 10, 1)">市公司</el-button>
      <el-button plain class="btnBack" @click="tableSearch(id, '3', 10, 1)">分组</el-button> -->
      <!-- <el-button plain class="btnBack" @click="tableSearch(id, '4', 10, 1)">专业4</el-button>
      <el-button plain class="btnBack" @click="tableSearch(id, '5', 10, 1)">专业5</el-button>
      <el-button plain class="btnBack" @click="tableSearch(id, '6', 10, 1)">专业6</el-button>
      <el-button plain class="btnBack" @click="tableSearch(id, '7', 10, 1)">专业7</el-button>
      <el-button plain class="btnBack" @click="tableSearch(id, '8', 10, 1)">专业8</el-button>
      <el-button plain class="btnBack" @click="tableSearch(id, '9', 10, 1)">专业9</el-button> -->

    </div>
    <div class="tabLine">
      <div class="tabA">
        <div class="headerTab">
          <div class="marker"></div>
          <div class="title">{{ titleInfo }}</div>
        </div>
        <div class="line"></div>
        <div class="cardTab">
          <div class="cardTabImg">
            <img :src="orcal.userImg" alt="">
            <div class="list">
              <div class="liA">
                <span class="label">姓名：</span>
                <span class="text">{{ orcal.userName }}</span>
              </div>
              <div class="liB">
                <span class="label">职位：</span>
                <span class="text">{{ orcal.userRole }}</span>
              </div>
              <div class="liC"><span class="label">电话：</span>
                <span class="text">{{ orcal.userPhone }}</span>
              </div>
              <div class="liD"><span class="label">到岗情况：</span>
<!--                <span class="text textSingle">{{ orcal.userAttendState }}</span>-->
                <span class="text textSingle">{{ orcal.userAttendName }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="tabB">
        <div class="headerTab">
          <div class="marker"></div>
          <div class="title">应急物资</div>
        </div>
        <div class="line"></div>
        <div class="cardTab">
          <div class="cardTabImg">
            <el-image
                style="width: 128px; height: 128px"
                :src="carUrl"
                :preview-src-list="srcList">
            </el-image>
<!--            <img src="../assets/img_slices/car.png" alt=""  @mouseenter="handleMouseEnter" @mouseleave="handleMouseLeave">-->
            <div class="list" style="margin-top: -5Px;">
              <div class="liA">
                <span class="label">备品备件：</span>
                <span class="textA" style="font-size: 30Px">{{ orcal.cataNum }}<span class="textB">类</span>/{{ orcal.num }}<span class="textB">件</span></span>
              </div>
              <div class="liA">
                <span class="label">应急车辆发电机组：</span>
                <span class="textA" style="font-size: 30Px">{{ orcal.emergencyElecCarNum }}</span>
                <span class="textB">台</span>
              </div>

            </div>
          </div>
        </div>
      </div>
      <div class="tabC">
        <div class="headerTab">
          <div class="marker"></div>
          <div class="title">现场保障团队状态</div>
        </div>
        <div class="line"></div>
        <div class="cardTab">
          <div class="cardTabImg">
            <img src="../assets/img_slices/user.png" alt="">
            <div class="list">
              <div class="liA">
                <span class="label">到岗率</span>
                <div class="titleList">
                  <span class="textA">{{ orcal.dgRate }}</span>
                  <!-- <span class="textB">台</span> -->

                </div>

              </div>
              <div class="liB">
                <span class="label">应到人数 / 实到人数</span>
                <div class="titleList">
                  <span class="textA">{{ orcal.ydNum }} /{{ orcal.dgNum }}</span>
                  <!-- <span class="textB">台</span> -->

                </div>
              </div>

            </div>
          </div>
        </div>
      </div>
      <div class="tabD">
        <div class="headerTab">
          <div class="marker"></div>
          <div class="title">保障承诺</div>
        </div>
        <div class="line"></div>
        <div class="cardTab">
          <div class="cardTabImg">
            <div class="cardtitle">
              {{ orcal.zbPromise }}
            </div>

          </div>
        </div>
      </div>
    </div>
    <div class="tableDiv">
      <div class="headerTable">

        <div class="marker"></div>
        <div class="title">人员信息列表</div>

      </div>
      <el-table :data="tableData" stripe style="width: 100%" row-key="userId" default-expand-all max-height="600"
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
        :header-cell-style="{ background: '#082c3aFF', color: '#606266' }">
        <el-table-column prop="userName" label="姓名">
        </el-table-column>
        <el-table-column prop="userPhone" label="联系电话">
        </el-table-column>
<!--        <el-table-column prop="userType" label="人员类型">
        </el-table-column>-->
        <el-table-column prop="userProfession" label="专业">
        </el-table-column>
        <el-table-column prop="userRole" label="角色">
        </el-table-column>
        <el-table-column prop="posName" label="驻守位置">
        </el-table-column>
        <el-table-column prop="userBzDuration" label="保障时长">
        </el-table-column>
        <el-table-column prop="userAttendName" label="到岗情况">
        </el-table-column>
        <el-table-column prop="onlineState" label="在线状态" width="120">
          <template slot="header">
            <el-tooltip class="item" effect="dark" popper-class="onlineClass" content="在线状态主要是记录员工最近一小时在其位置值班状态" placement="top-start" >
              <span><i class="el-icon-question"></i> 在线状态 </span>
            </el-tooltip>
          </template>
          <template slot-scope="scope">
            <ul class="feature-list" v-if="scope.row.onlineState === '离线'">
              <li>{{scope.row.onlineState}}</li>
            </ul>
            <ul class="feature-list2" v-if="scope.row.onlineState === '在线'">
              <li>{{scope.row.onlineState}}</li>
            </ul>
          </template>
        </el-table-column>
<!--        <el-table-column prop="signLogTime" label="最近签到时间" width="180">
        </el-table-column>-->
      </el-table>
      <!-- <el-pagination @size-change="sizeChange" @current-change="currentChange" :current-page="currentPage"
        :page-sizes="[10, 20, 50, 100]" :page-size="pageSizeDialog" :background="isBackground" layout="prev, pager, next"
        :total="total">
      </el-pagination> -->
    </div>
  </div>
</template>

<script>
import CpTable from "@/components/CpTable";
import { formatTime } from "../assets/js/index.js";

export default {
  // import引入的组件需要注入到对象中才能使用
  components: {},
  data() {

    // 这里存放数据
    return {
      tableData: [{
        userId: 1,
        date: '2016-05-02',
        name: '王小虎',
        address: '上海市普陀区金沙江路 1518 弄'
      }, {
        userId: 2,
        date: '2016-05-04',
        name: '王小虎',
        address: '上海市普陀区金沙江路 1517 弄'
      }, {
        userId: 3,
        date: '2016-05-01',
        name: '王小虎',
        address: '上海市普陀区金沙江路 1519 弄',
        children: [{
          userId: 31,
          date: '2016-05-01',
          name: '王小虎',
          address: '上海市普陀区金沙江路 1519 弄'
        }, {
          userId: 32,
          date: '2016-05-01',
          name: '王小虎',
          address: '上海市普陀区金沙江路 1519 弄'
        }]
      }, {
        userId: 4,
        date: '2016-05-03',
        name: '王小虎',
        address: '上海市普陀区金沙江路 1516 弄'
      }],
      tableData1: [{
        userId: 1,
        date: '2016-05-02',
        name: '王小虎',
        address: '上海市普陀区金沙江路 1518 弄'
      }, {
        userId: 2,
        date: '2016-05-04',
        name: '王小虎',
        address: '上海市普陀区金沙江路 1517 弄'
      }, {
        userId: 3,
        date: '2016-05-01',
        name: '王小虎',
        address: '上海市普陀区金沙江路 1519 弄',
        hasChildren: true
      }, {
        userId: 4,
        date: '2016-05-03',
        name: '王小虎',
        address: '上海市普陀区金沙江路 1516 弄'
      }],
      isBackground: true,
      size: 10,
      current: 1,
      orcal: {},
      currentPage: 1,
      pageSizeDialog: 10,
      id: "",
      title: "",
      dateDay: null,
      dateYear: null,
      dateDay2: null,
      dateYear2: null,
      dateWeek: null,
      weekday: ["周日", "周一", "周二", "周三", "周四", "周五", "周六"],
      total: 1,
      titleInfo: "信息",
      btnArr: [],
      carUrl: require('../assets/img_slices/car.png'),
      srcList: [
        'http://***********/images/zb/zjjg.png'
      ]
    };
  },
  // 监听属性 类似于data概念
  computed: {},
  // 监控data中的数据变化
  watch: {},
  // 方法集合
  methods: {
    handleMouseEnter() {

    },
    handleMouseLeave() {
    },
    timeFn() {
      this.timing1 = setInterval(() => {
        this.dateDay = formatTime(new Date(), "HH: mm: ss");
        this.dateYear = formatTime(new Date(), "yyyy年MM月dd日");
        this.dateWeek = this.weekday[new Date().getDay()];
      }, 1000);
      this.timing2 = setInterval(() => {
        this.dateDay2 = formatTime(new Date(), "HH: mm: ss");
        this.dateYear2 = formatTime(new Date(), "yyyy/MM/dd日");
      }, 1000);
    },
    back() {
      this.$router.push("./");
    },
    getData() {
      let _this = this;
      let queryData = { "parentId": 0 };
      // debugger;
      this.$axios.post("/protect-api/person/getGroupList", queryData).then((data) => {
        // console.log(data);
        if (data.data.code === "0000") {
          this.btnArr = data.data.data;
          // this.orcal = {}
          // this.tableData = data.data.data;
          // if (data.data.data[0] != null) {
          //   this.orcal = data.data.data[0];
          //   this.total = data.data.data[1].total;
          // }

        }
      });


    },
    tableSearch(id, data, size, current) {
      let _this = this;
      let queryData = { "parentId": id, "dataType": "2" };
      // debugger;
      this.$axios.post("/protect-api/person/getAllPersonList", queryData).then((data) => {
        // console.log(data);
        if (data.data.code === "0000") {
          this.orcal = {};
          this.tableData = data.data.data[0].children;
          if (data.data.data[0] != null) {
            this.orcal = data.data.data[0];
            // this.total = data.data.data[1].total;
          }

        }
      });


    },
    sizeChange(pageSize) {
      this.pageSizeDialog = Number(pageSize);
      this.tableSearch(this.id, "", this.currentPage, 10);
    },
    currentChange(page) {
      // this.pagination.page = page - 1;
      // bus.$emit("page", page);
      // this.searchTable();
      this.currentPage = Number(page);
      this.tableSearch(this.id, "", 10, page);
      // this.page = Number(page);
    },

  },
  // 生命周期 - 创建完成（可以访问当前this实例）
  created() {
    // this.getPerms();
  },
  // 生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {
    this.timeFn();
    this.getData();
    if (JSON.stringify(this.$route.query) === '{}') {
      // console.log(1);
      this.id = "";
      this.tableSearch(2, "", this.size, this.current);
    } else {
      // console.log(this.$route.query.p.parentId);
      this.id = this.$route.query.p.parentId;
      this.tableSearch(this.id, "", this.size, this.current);
    }

  },
  // 这里存放数据
  beforeCreate() { },
  // 生命周期 - 挂载之前
  beforeMount() { },
  // 生命周期 - 更新之前
  beforeUpdate() { },
  // 生命周期 - 更新之后
  updated() { },
  // 生命周期 - 销毁之前
  beforeDestroy() { },
  // 生命周期 - 销毁完成
  destroyed() { },
  // 如果页面有keep-alive缓存功能，这个函数会触发
  activated() { },
};
</script>

<style lang='scss' scoped>
// @import url(); 引入公共css类

.home {
  width: 100%;
  height: 100%;
  background-color: #091220FF;
  padding: 0 30px;

  .btnDiv {
    padding-top: 80px;
    display: flex;
    padding-left: 15px;
    padding-bottom: 16px;
    .btnBack {
      // width: 58px;
      height: 32Px;
      font-size: 14Px;
      font-family: Source Han Sans CN;
      font-weight: 400;
      color: #2AB8B7;
      line-height: 29Px;
      padding: 0px;
      padding-left: 10px;
      padding-right: 10px;
/*      padding-top: 10px;*/
      padding-bottom: 10px;
      box-shadow: 0 0 8px #2AB8B7;
      border: 1px solid #2AB8B7;

      span {
        width: 100%;
        height: 100%;
      }
    }
  }

  .tabLine {
    width: 100%;
    height: 210px;
    display: flex;
    flex-direction: row;
    align-content: center;
    justify-content: space-between;
    align-items: center;

    .tabA {
      width: 25%;
      height: 100%;
      margin-right: 15px;

      .headerTab {
        width: 100%;
        height: 44px;
        line-height: 44px;
        display: flex;
        /* padding: 1rem; */
        // background: url('../assets/img_slices/headerTitle.png') no-repeat 100% 100%;
        background: url('../assets/img_slices/headerTitle.png') no-repeat center;
        background-size: 100% 100%;
        flex-direction: row;
        align-content: center;
        justify-content: flex-start;
        align-items: center;
        padding-left: 10px;

        .marker {
          // width: 17px;
          // height: 17px;
          // line-height: 17px;
          // border-radius: 50%;
          // background: linear-gradient(0deg, #25bfabFF, #1b7981FF);
        }

        .title {
          // width: 190px;
          // height: 23px;
          padding-left: 28px;
          // width: 94px;
          height: 23px;
          font-size: 20px;
          font-family: Alimama ShuHeiTi;
          font-weight: bold;
          color: #FFFFFF;
          line-height: 20px;
          text-shadow: 0px 2px 8px rgba(5, 28, 55, 0.42);

          background: linear-gradient(0deg, rgba(14, 197, 236, 0.36) 0%, rgba(49, 238, 255, 0.36) 0%, rgba(239, 252, 254, 0.36) 100%);
          -webkit-background-clip: text;
          // -webkit-text-fill-color: transparent;
        }
      }

      .line {
        width: 100%;
        height: 4px;
        background: linear-gradient(90deg, #0BBEEB 0%);
        opacity: 0.5;
      }

      .cardTab {
        width: 100%;
        height: 165px;
        // border: 1px solid #4BB9D4;
        background: linear-gradient(-55deg, rgba(14, 45, 40, 0.5) 0%, #074653 100%);
/*        background: #0C3038;*/
        // opacity: 0.5;
        display: flex;
        flex-direction: row;
        align-content: center;
        justify-content: center;
        align-items: center;

        .cardTabImg {

          width: 96%;
          height: 146px;
/*          opacity: 0.5;*/
          background: linear-gradient(-55deg, #0A3038 0%, #114D59 100%);
/*          background: #0C3038;*/
          display: flex;
          flex-direction: row;
          align-content: center;
          justify-content: flex-start;
          align-items: center;
          padding-left: 17px;

          img {
            height: 112px;
          }

          .list {
            display: flex;
            flex-direction: column;
            flex-wrap: nowrap;
            align-content: flex-start;
            justify-content: center;
            align-items: flex-start;
            padding-left: 20px;
            font-size: 16px;
            font-family: Source Han Sans CN;
            font-weight: 400;
            color: #FFFEFE;

            .label,
            .text {
              font-size: 16px;
              font-family: Source Han Sans CN;
              font-weight: 400;
              color: #FFFEFE;
            }
          }
        }
      }
    }

    .tabB {
      width: 25%;
      height: 100%;
      margin-right: 15px;

      .headerTab {
        width: 100%;
        height: 44px;
        line-height: 44px;
        display: flex;
        /* padding: 1rem; */
        // background: url('../assets/img_slices/headerTitle.png') no-repeat 100% 100%;
        background: url('../assets/img_slices/headerTitle.png') no-repeat center;
        background-size: 100% 100%;
        flex-direction: row;
        align-content: center;
        justify-content: flex-start;
        align-items: center;
        padding-left: 10px;

        .marker {
          // width: 17px;
          // height: 17px;
          // line-height: 17px;
          // border-radius: 50%;
          // background: linear-gradient(0deg, #25bfabFF, #1b7981FF);
        }

        .title {
          // width: 190px;
          // height: 23px;
          padding-left: 28px;
          // width: 94px;
          height: 23px;
          font-size: 20px;
          font-family: Alimama ShuHeiTi;
          font-weight: bold;
          color: #FFFFFF;
          line-height: 20px;
          text-shadow: 0px 2px 8px rgba(5, 28, 55, 0.42);

          background: linear-gradient(0deg, rgba(14, 197, 236, 0.36) 0%, rgba(49, 238, 255, 0.36) 0%, rgba(239, 252, 254, 0.36) 100%);
          -webkit-background-clip: text;
          // -webkit-text-fill-color: transparent;
        }
      }

      .line {
        width: 100%;
        height: 4px;
        background: linear-gradient(90deg, #0BBEEB 0%);
        opacity: 0.5;
      }

      .cardTab {
        width: 100%;
        height: 165px;
        // border: 1px solid #4BB9D4;
        background: linear-gradient(-55deg, rgba(14, 45, 40, 0.5) 0%, #074653 100%);
   /*     background: #0C3038;*/
        // opacity: 0.5;
        display: flex;
        flex-direction: row;
        align-content: center;
        justify-content: center;
        align-items: center;

        .cardTabImg {

          width: 96%;
          height: 146px;
   /*       opacity: 0.5;*/
          background: linear-gradient(-55deg, #0A3038 0%, #114D59 100%);
/*          background: #0C3038;*/
          display: flex;
          flex-direction: row;
          align-content: center;
          justify-content: flex-start;
          align-items: center;
          padding-left: 14Px;

          img {
            height: 112px;
          }

          .list {
/*            display: flex;*/
            flex-direction: row;
            flex-wrap: nowrap;
            align-content: flex-start;
            justify-content: center;
            align-items: flex-start;
            padding-left: 20px;
            font-size: 16px;
            font-family: Source Han Sans CN;
            font-weight: 400;
            color: #FFFEFE;

            .liB {
              // margin-right: 5px;
            }

            .liA {
              margin-right: 5px;
            }

            .label,
            .text {
              font-size: 16px;
              font-family: Source Han Sans CN;
              font-weight: 400;
              color: #FFFFFF;
            }



            .textA {
              font-size: 48px;
              font-family: D-DIN-PRO;
              font-weight: 500;
              color: #71D2FF;
            }

            .textB {
              font-size: 16px;
              font-family: Source Han Sans CN;
              font-weight: 400;
              color: #71D2FF;
            }
          }
        }
      }
    }

    .tabC {
      width: 25%;
      height: 100%;
      margin-right: 15px;

      .headerTab {
        width: 100%;
        height: 44px;
        line-height: 44px;
        display: flex;
        /* padding: 1rem; */
        // background: url('../assets/img_slices/headerTitle.png') no-repeat 100% 100%;
        background: url('../assets/img_slices/headerTitle.png') no-repeat center;
        background-size: 100% 100%;
        flex-direction: row;
        align-content: center;
        justify-content: flex-start;
        align-items: center;
        padding-left: 10px;

        .marker {
          // width: 17px;
          // height: 17px;
          // line-height: 17px;
          // border-radius: 50%;
          // background: linear-gradient(0deg, #25bfabFF, #1b7981FF);
        }

        .title {
          // width: 190px;
          // height: 23px;
          padding-left: 28px;
          // width: 94px;
          height: 23px;
          font-size: 20px;
          font-family: Alimama ShuHeiTi;
          font-weight: bold;
          color: #FFFFFF;
          line-height: 20px;
          text-shadow: 0px 2px 8px rgba(5, 28, 55, 0.42);

          background: linear-gradient(0deg, rgba(14, 197, 236, 0.36) 0%, rgba(49, 238, 255, 0.36) 0%, rgba(239, 252, 254, 0.36) 100%);
          -webkit-background-clip: text;
          // -webkit-text-fill-color: transparent;
        }
      }

      .line {
        width: 100%;
        height: 4px;
        background: linear-gradient(90deg, #0BBEEB 0%);
        opacity: 0.5;
      }

      .cardTab {
        width: 100%;
        height: 165px;
        // border: 1px solid #4BB9D4;
        background: linear-gradient(-55deg, rgba(14, 45, 40, 0.5) 0%, #074653 100%);
/*        background: #0C3038;*/
        // opacity: 0.5;
        display: flex;
        flex-direction: row;
        align-content: center;
        justify-content: center;
        align-items: center;

        .cardTabImg {

          width: 96%;
          height: 146px;
/*          opacity: 0.5;*/
          background: linear-gradient(-55deg, #0A3038 0%, #114D59 100%);
/*          background: #0C3038;*/
          display: flex;
          flex-direction: row;
          align-content: center;
          justify-content: flex-start;
          align-items: center;
          padding-left: 17px;



          img {
            height: 112px;
          }

          .list {
            display: flex;
            flex-direction: row;
            flex-wrap: nowrap;
            align-content: flex-start;
            justify-content: center;
            align-items: flex-start;
            padding-left: 6px;
            font-size: 16px;
            font-family: Source Han Sans CN;
            font-weight: 400;
            color: #FFFEFE;

            .liB {
              // margin-right: 5px;
            }

            .liA {
              margin-right: 15px;
            }

            .label,
            .text {
              font-size: 16px;
              font-family: Source Han Sans CN;
              font-weight: 400;
              color: #FFFFFF;
            }

            .label {
              font-size: 14px;
            }

            .textA {
              font-size: 36px;
              font-family: D-DIN-PRO;
              font-weight: 500;
              color: #5ADB95;
            }

            // .textB {
            //   font-size: 16px;
            //   font-family: Source Han Sans CN;
            //   font-weight: 400;
            //   color: #71D2FF;
            // }
          }
        }
      }
    }

    .tabD {
      width: 25%;
      height: 100%;
      // margin-right: 15px;

      .headerTab {
        width: 100%;
        height: 44px;
        line-height: 44px;
        display: flex;
        /* padding: 1rem; */
        // background: url('../assets/img_slices/headerTitle.png') no-repeat 100% 100%;
        background: url('../assets/img_slices/headerTitle.png') no-repeat center;
        background-size: 100% 100%;
        flex-direction: row;
        align-content: center;
        justify-content: flex-start;
        align-items: center;
        padding-left: 10px;

        .marker {
          // width: 17px;
          // height: 17px;
          // line-height: 17px;
          // border-radius: 50%;
          // background: linear-gradient(0deg, #25bfabFF, #1b7981FF);
        }

        .title {
          // width: 190px;
          // height: 23px;
          padding-left: 28px;
          // width: 94px;
          height: 23px;
          font-size: 20px;
          font-family: Alimama ShuHeiTi;
          font-weight: bold;
          color: #FFFFFF;
          line-height: 20px;
          text-shadow: 0px 2px 8px rgba(5, 28, 55, 0.42);

          background: linear-gradient(0deg, rgba(14, 197, 236, 0.36) 0%, rgba(49, 238, 255, 0.36) 0%, rgba(239, 252, 254, 0.36) 100%);
          -webkit-background-clip: text;
          // -webkit-text-fill-color: transparent;
        }
      }

      .line {
        width: 100%;
        height: 4px;
        background: linear-gradient(90deg, #0BBEEB 0%);
        opacity: 0.5;
      }

      .cardTab {
        width: 100%;
        height: 165px;
        // border: 1px solid #4BB9D4;
        background: linear-gradient(-55deg, rgba(14, 45, 40, 0.5) 0%, #074653 100%);
/*        background: #0C3038;*/
        // opacity: 0.5;
        display: flex;
        flex-direction: row;
        align-content: center;
        justify-content: center;
        align-items: center;

        .cardTabImg {
          width: 96%;
          height: 146px;
/*          opacity: 0.5;*/
/*          background: #0C3038;*/
          background: linear-gradient(-55deg, #0A3038 0%, #114D59 100%);
          display: flex;
          flex-direction: row;
          align-content: center;
          justify-content: flex-start;
          align-items: center;
          // padding-left: 32px;

          .cardtitle {
            font-size: 28px;
            margin-left: 7px;
            font-family: Source Han Sans CN;
            font-weight: bold;
            color: #FFC36E;
            line-height: 38px;
            text-shadow: 0px 6px 14px rgba(12, 29, 30, 0.1);

            background: linear-gradient(0deg, #FFA647 0%, #FBD195 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
          }




        }
      }
    }
  }

  .tableDiv {
    width: 100%;

    .headerTable {
      width: 100%;
      height: 44px;
      display: flex;
      /* padding: 1rem; */
      // background: url('../assets/img_slices/headerTitle.png') no-repeat 100% 100%;
      background: url('../assets/img_slices/tableHeader.png') no-repeat center;
      background-size: 100% 100%;
      flex-direction: row;
      align-content: center;
      justify-content: flex-start;
      align-items: center;
      padding-left: 10px;

      .marker {
        // width: 17px;
        // height: 17px;
        // line-height: 17px;
        // border-radius: 50%;
        // background: linear-gradient(0deg, #25bfabFF, #1b7981FF);
      }

      .title {
        // width: 190px;
        padding-left: 28px;
        // width: 94px;
        height: 23px;
        font-size: 20px;
        font-family: Alimama ShuHeiTi;
        font-weight: bold;
        color: #FFFFFF;
        line-height: 20px;
        text-shadow: 0px 2px 8px rgba(5, 28, 55, 0.42);

        background: linear-gradient(0deg, rgba(14, 197, 236, 0.36) 0%, rgba(49, 238, 255, 0.36) 0%, rgba(239, 252, 254, 0.36) 100%);
        -webkit-background-clip: text;
        // -webkit-text-fill-color: transparent;
      }
    }
  }

  /deep/.el-table th.el-table__cell>.cell {
    font-size: 16px;
    font-family: Source Han Sans CN;
    font-weight: 400;
    color: #2AB8B7;
  }

  /deep/.el-table tr {
    background-color: #073653FF;
  }

  /deep/.el-table__row--striped {
    background-color: #082a37FF !important;


  }

  /deep/.el-table--enable-row-hover .el-table__body tr:hover>td.el-table__cell {
    background-color: #082a37FF !important;

  }

  /deep/.el-table--striped .el-table__body tr.el-table__row--striped td.el-table__cell {
    background-color: #082a37FF !important;

  }

  /deep/.el-table--enable-row-transition .el-table__body td.el-table__cell {
    border: none;
  }

  /deep/.el-table th.el-table__cell.is-leaf {
    border: none;
    border-bottom: none;
  }

  .el-pagination {
    float: right;
  }

  /deep/.el-pagination.is-background .btn-next,
  .el-pagination.is-background .btn-prev,
  .el-pagination.is-background .el-pager li {
    background-color: transparent;
  }

  /deep/ .el-button {
    background-color: transparent;

  }

  .el-pagination.is-background .el-pager li:not(.disabled) {
    background-color: transparent; // 进行修改未选中背景和字体
    // color: #fff;
  }

  .el-pagination.is-background .el-pager li:not(.disabled).active {
    background-color: transparent; // 进行修改选中项背景和字体
    color: #fff;
  }

  .is-background .el-pager li {
    /*对页数的样式进行修改*/
    background-color: #10263c;
    color: #FFF;
  }

  .is-background .btn-next {
    /*对下一页的按钮样式进行修改*/
    background-color: #10263c;
    color: #FFF;
  }

  .is-background .btn-prev {
    /*对上一页的按钮样式进行修改*/
    background-color: #10263c;
    color: #FFF;
  }

  .is-background .el-pager li:not(.disabled).active {
    /*当前选中页数的样式进行修改*/
    background-color: #446cff;
    color: #FFF;
  }

  /deep/ .el-pagination.is-background .el-pager li {
    background-color: transparent;
  }

  /deep/ .el-table td.el-table__cell div {
    color: #E1E1E1;
  }

  /deep/.el-table,
  .el-table__expanded-cell {
    background-color: transparent;
  }

  .header-long {
    position: fixed;
    width: 100%;
    height: 70Px;
    line-height: 70Px;
    z-index: 2;
    background-image: url('../assets/img_slices/bg.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
  }

  .header-right-middle {
    position: fixed;
    width: 391Px;
    height: 60Px;
    margin-left: 766Px;
    background-image: url('../assets/img_slices/userrun.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
  }

  .header-right-middle-span {
    width: 356Px;
    height: 34Px;
    font-size: 36Px;
    font-family: Alimama ShuHeiTi;
    font-weight: bold;
    color: #C6F6FF;
    line-height: 50Px;
    opacity: 0.89;
    text-shadow: 0Px 4Px 6Px rgba(7, 46, 26, 0.7);

    background: linear-gradient(0deg, rgba(119, 255, 253, 0.45) 0%, rgba(233, 248, 255, 0.45) 73.3154296875%, rgba(255, 255, 255, 0.45) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;

  }

  .imgLT {
    background-image: url('../assets/zxx/lt.png');
    background-repeat: no-repeat;
    background-size: 81% 81%;
    width: 84Px;
    height: 46Px;
    margin: 4Px 10Px;
  }

  .dateSpan {
    margin-left: 1712Px;
    margin-top: -37Px;
    position: absolute;
  }

  .date {
    text-align: left;
    width: 200Px;
    line-height: 14Px;
    color: #ffffff;
    font-size: 14Px;
  }

}

/deep/.el-table .el-table__cell.gutter {
  width: 0px !important;
  height: 0px;
  display: none;
}
/deep/ .el-table__body-wrapper::-webkit-scrollbar:hover {
  width: 8px !important;
}

/*定义滚动条轨道 内阴影+圆角*/
/deep/ .el-table__body-wrapper::-webkit-scrollbar-track {
  box-shadow: inset 0 0 0px rgba(240, 240, 240, .5);
  border-radius: 10px;
  background-color: rgba(240, 240, 240, .5);
}

/deep/ .el-table__body-wrapper:hover::-webkit-scrollbar-corner {
  width: 6px;
}

/*定义滑块 内阴影+圆角*/
/deep/ .el-table__body-wrapper::-webkit-scrollbar-thumb {
  border-radius: 10px;
  box-shadow: inset 0 0 0px #206977;
  background: linear-gradient(-55deg, #0A3038 0%, #114D59 100%);
}

/deep/ .el-table__body-wrapper::-webkit-scrollbar {
  width: 5px; // 横向滚动条
  height: 5px; // 纵向滚动条 必写
}
.el-button.is-plain:focus, .el-button.is-plain:hover {
    // width: 58px;
    height: 32Px;
    font-size: 14Px;
    font-family: Source Han Sans CN;
    font-weight: 400;
    color: #2AB8B7;
    line-height: 29Px;
    padding: 0px;
    padding-left: 10px;
    padding-right: 10px;
    /*      padding-top: 10px;*/
    padding-bottom: 10px;
    border: 1px solid #1a9695;
    background-color: #038793;
    box-shadow: 0 0 5px #1a9695;
    color: #fff;

}
.feature-list {
  color: #818181;
  margin-left: 0rem;
  list-style-type: disc;
  ul{
    margin-left: 0rem;
    list-style-type: circle;
    margin-bottom: 0rem;
  }
}
.feature-list2 {
  color: #00c54f;
  margin-left: 0rem;
  list-style-type: disc;
  ul{
    margin-left: 0rem;
    list-style-type: circle;
    margin-bottom: 0rem;
  }
}
ul{
  margin-bottom: 0rem;
  padding-left: 1.5rem;
}
/deep/ .el-icon-question{
  font-size: 12px;
}
</style>
<style>
.el-table--fit {
  border-right: 0;
  border-bottom: 0;
}

.el-table::before {
  width: 0;
  height: 0;
}
.onlineClass  {
  background: #095a82!important;
  color: #d3d1d1 !important;
}
</style>
