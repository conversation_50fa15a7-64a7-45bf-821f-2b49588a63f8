<template>
  <div>
    <j-hover-btn
      :width="70"
      :text="textTitleS"
      :btn-style="btnStyle"
      @hoverBtnClick="hoverBtnClick()"
    />
  </div>
</template>
<script>
import bus from "@/eventBus/bus.js";
export default {
  props: {
    textTitle: { type: String, default: "大屏模式" },
  },

  data() {
    return {
      colspan: true,
      btnStyle: {
        fontSize: "small",
        top: "80vh",
        left: "90vw",
        background:
          "linear-gradient(180deg, rgba(0, 60, 114, 0.2) 0%, #0d5d8e 100%)",
        border: "1px solid #48b5ff",
        color: "#5dc3ff",
      },
      textTitleS: "",
    };
  },
  // 生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {
    this.textTitleS = this.textTitle;
  },
  methods: {
    hoverBtnClick() {
      this.colspan = !this.colspan;
      this.$emit("hover-btn", this.colspan);
    },
  },
};
</script>