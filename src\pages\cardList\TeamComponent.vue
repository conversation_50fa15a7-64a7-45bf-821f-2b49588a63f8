<template>
  <div class="team-container">
    <div class="team-stats">
      <div
        class="stat-item"
        @click="viewTeamDetail('4')"
      >
        <div
          class="stat-value"
          style="color: #55A4FE;"
        >
          {{ data.num }}
        </div>
        <div class="stat-label">
          保障总人数
        </div>
      </div>
        
      <div class="stat-item">
        <div
          class="stat-value"
          style="color: #5ADB95;"
        >
          {{ data.ydNum }}/{{ data.dgNum }}
        </div>
        <div class="stat-label">
          应到/实到人数
        </div>
      </div>
        
      <div class="stat-item">
        <div
          class="stat-value"
          style="color: #5ADB95;"
        >
          {{ data.dgRate }}
        </div>
        <div class="stat-label">
          到岗率
        </div>
      </div>
    </div>
  </div>
</template>
  
  <script>
  export default {
    props: {
      data: {
        type: Object,
        default: () => ({})
      }
    },
    methods: {
      viewTeamDetail(type) {
        // console.log(`查看${type}详情`);
        // 实际项目中这里可能会打开详情弹窗或跳转页面
        const data = {
          // type:'数通设备',
          message: type,
        };
        
        // 触发自定义事件，并传递数据
        this.$emit('customEvent', data);
      }
    }
  };
  </script>
  
  <style scoped>
  .team-container {
    height: 100%;
  }
  
  .team-stats {
    display: flex;
    justify-content: space-between;
    padding: 10px 0;
  }
  
  .stat-item {
    flex: 1;
    text-align: center;
    padding: 8px;
    border-radius: 4px;
    transition: background-color 0.2s;
  }
  
  .stat-item:hover {
    background-color: rgba(255, 255, 255, 0.05);
  }
  
  .stat-value {
    font-size: 22px;
    font-weight: 500;
    margin-bottom: 4px;
  }
  
  .stat-label {
    font-size: 14px;
    color: rgba(255, 255, 255, 0.7);
  }
  </style>
  