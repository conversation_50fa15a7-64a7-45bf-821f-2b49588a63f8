<template>
  <div
    v-loading="loading"
    :class="['ant-table']"
    element-loading-text="数据加载中"
    element-loading-spinner="el-icon-loading"
    element-loading-background="rgba(0, 0, 0, 0.2)"
  >
    <el-table
      ref="table"
      :data="data"
      :border="border"
      size="small"
      v-bind="{ ...cpTableDes, ...$attrs }"
      v-on="{ ...$listeners }"
    >
      <template slot="empty">
        <div class="table-empty">
          <img
            :width="checkWidth_empty * 1.6 + 'px'"
            :src="require('@/assets/empty.png')"
            alt=""
          />
          <div class="noCart">
            {{ empty_title }}
          </div>
        </div>
      </template>
      <el-table-column type="selection" width="55" align="center">
      </el-table-column>
      
      <el-table-column
        v-if="checkList"
        type="selection"
        align="center"
        :width="checkWidth"
        :reserve-selection="true"
      />
      <el-table-column
        v-if="hasIndex"
        label="序号"
        type="index"
        align="center"
        :width="indexWidth"
      />
      <slot name="first" />
      <template v-for="(column, idx) in columns">
        <ant-column
          v-if="!column.hidden"
          :key="idx"
          :column="column"
          :pagination="pagination"
          :show-overflow-tooltip="
            showOverflowTooltip || column.showOverflowTooltip
          "
          :align="align"
        >
          <template
            v-for="tableSlot in tableSlots"
            :slot="tableSlot.slotRender"
            slot-scope="slotData"
          >
            <slot v-bind="{ ...slotData }" :name="tableSlot.slotRender" />
          </template>
        </ant-column>
      </template>
      <slot />
    </el-table>
    <div v-if="hasPagination" class="pagination-wrapper">
      <el-pagination
        :page-size="pagination.pageSize"
        :current-page="pagination.size"
        :layout="pagination.layout || 'total, sizes, prev, pager, next, jumper'"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        background
        @current-change="currentChange"
        @size-change="sizeChange"
      />
    </div>
  </div>
</template>

<script>
import Vue from "vue";
import AntColumn from "@/components/AntColumn";
import $ from "jquery";
import "@/utils/util.js";
import bus from "@/eventBus/bus.js";
let basicPageSizes = [];
export default {
  name: "CpTable",
  components: { AntColumn },
  filters: {
    emptyContent(value) {
      return value || value === 0 ? value : "-";
    },
  },
  props: {
    /** **表格对齐方式 */
    align: {
      type: [String],
      default: "center",
    },
    /** **表格溢出变点 */
    showOverflowTooltip: {
      type: [Boolean],
      default: false,
    },
    hasIndex: {
      type: [Boolean],
      default: false,
    },
    loading: {
      type: [Boolean, Object],
      default: false,
    },
    border: {
      type: [Boolean, Object],
      default: false,
    },
    /** **外部数据 */
    data: {
      type: Array,
      default: () => [],
    },
    /** **列名 */
    columns: {
      type: Array,
      default: () => [],
    },
    /** **loading */
    total: {
      type: [String, Number],
      default: 50,
    },
    /** *** 翻页器配置 */
    pagination: {
      type: Object,
      default: () => ({
        page: 1,
        pageSize: 10,
      }),
    },
    /** *** 翻页器显隐 */
    hasPagination: {
      type: Boolean,
      default: true,
    },
    /** *** 多选框 */
    checkList: {
      type: [Boolean, Array],
      default: false,
    },
    checkWidth_empty: {
      type: [String, Number],
      default: 0,
    },
    empty_title: {
      type: [String],
      default: "",
    },
  },
  data() {
    return {
      indexWidth: 60,
      checkWidth: 55,
      isIe: false,
      pageSizes: basicPageSizes,
      // 提前规定的静态属性
      cpTableDes: {
        center: true,
      },
    };
  },
  computed: {
    tableSlots() {
      const _columns = this.columns?.slice() || [];
      return _columns.filter((column) => {
        return column.slotRender;
      });
    },
  },
  watch: {
    data: {
      handler() {},
      deep: true,
      immediate: true,
    },
  },
  mounted() {
    const copy = $(
      '<i class="el-icon-document-copy table-icon copy" collapse-disable="true" title="复制" ></i>'
    ).css({
      position: "absolute",
      top: "50%",
      transform: "translateY(-50%)",
      right: 0,
    })[0];
    function handleShowPopper() {
      if (!this.expectedState || this.manual) return;
      clearTimeout(this.timeout);
      this.timeout = setTimeout(() => {
        this.showPopper = true;
        $(this.referenceElm).children(":first").css({
          position: "relative",
          paddingRight: 20,
        });
        $(copy).attr({
          "copy-text": this.$refs.popper.innerText,
        });
        $(this.referenceElm).children(":first")[0].appendChild(copy);
      }, this.openDelay);
      if (this.hideAfter > 0) {
        this.timeoutPending = setTimeout(() => {
          this.showPopper = false;
        }, this.hideAfter);
      }
    }
    function handleClosePopper() {
      if ((this.enterable && this.expectedState) || this.manual) return;
      clearTimeout(this.timeout);
      if (this.timeoutPending) {
        clearTimeout(this.timeoutPending);
      }
      this.showPopper = false;
      $(copy).remove();
      $(this.referenceElm).children(":first").css({
        position: "auto",
        paddingRight: 10,
      });
      if (this.disabled) {
        this.doDestroy();
      }
    }
    this.$refs.table.$children.forEach((child) => {
      if (child.$options.name === "ElTableBody") {
        const tooltip = child.$refs.tooltip;
        tooltip.handleShowPopper = handleShowPopper.bind(tooltip);
        tooltip.handleClosePopper = handleClosePopper.bind(tooltip);
      }
    });
    window.onresize = this.resize;
    this.$once("hook:destroyed", () => {
      window.onresize = null;
    });
    this.$nextTick(() => {
      this.init();
    });
  },
  methods: {
    init() {
      $(".el-checkbox__input").attr({
        title: "当前页全选",
      });
    },
    resize() {
      this.indexWidth = (document.documentElement.clientWidth * 60) / 1920;
      this.checkWidth = (document.documentElement.clientWidth * 55) / 1920;
    },
    searchTable() {
      this.$emit("change", this.pagination);
    },
    sizeChange(pageSize) {
      this.pagination.page = 0;
      this.pagination.pageSize = pageSize;
      bus.$emit("size", pageSize);
      this.searchTable();
    },
    currentChange(page) {
      this.pagination.page = page - 1;
      bus.$emit("page", page);
      this.searchTable();
    },
    handleSelectionChange(checkList) {
      this.$emit("update:checkList", checkList);
    },
    changeColumns(column) {
      if (column.hidden === undefined || column.hidden === null) {
        Vue.set(column, "hidden", true);
      } else {
        column.hidden = !column.hidden;
      }
    },
  },
};
</script>

<style  lang="scss" >
.pagination-wrapper {
  margin-top: 10px;
  text-align: right;
}
</style>
<style lang="scss" scoped>
.table-empty {
  height: 180px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
</style>
