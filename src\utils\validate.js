/**
 * 手机号码
 */
export function isMobile(s) {
  return /^1[0-9]{10}$/.test(s);
}

// +86 手机号码
export function isMobiles(s) {
  return /^\d{11}$|^86\d{11}$|^86141\d{10}$|^86106\d{10}$|^141\d{10}$|^106\d{10}$/.test(s);
}

/**
 * 电话号码
 */
export function isPhone(s) {
  return /^([0-9]{3,4}-)?[0-9]{7,8}$/.test(s);
}

/**
 * URL地址
 */
export function isURL(s) {
  return /^http[s]?:\/\/.*/.test(s);
}
/**
 * 数字
 */
export function isNum(s) {
  return /^[0-9]+$/.test(s);
}
/*
  0-100数字
*/
export function isNumVal(s) {
  return /^(\d{1,2}(\.\d{1,2})?|100|100.0|100.00)$/.test(s);
}
/**
 * 邮编
 */
export function isZip(s) {
  return /^[1-9]\d{5}(?!\d)$/.test(s);
}

/**
 *IP
 */
export function isIP(s) {
  return /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$/.test(s);
}

/**
 *IP端口
 */
export function isPort(s) {
  return /^([0-9]|[1-9]\d{1,3}|[1-5]\d{4}|6[0-4]\d{4}|65[0-4]\d{2}|655[0-2]\d|6553[0-5])$/.test(s);
}
/**
 *子网掩码
 */
export function isSubnetmask(s) {
  return /^(254|252|248|240|224|192|128|0)\.0\.0\.0|255\.(254|252|248|240|224|192|128|0)\.0\.0|255\.255\.(254|252|248|240|224|192|128|0)\.0|255\.255\.255\.(254|252|248|240|224|192|128|0)$/.test(s);
}

/**
 * 频率
 */
export function isFrequency(s) {
  return /^[0-9]+[s|m|h]$/.test(s);
}
/**
 * 3gimsi
 */
export function is3gImsi(s) {
  // return /^46003\d{10}$/.test(s);
  return /^460\d{12}$/.test(s);
}
/**
 * 4gimsi
 */
export function is4gImsi(s) {
  // return /^46011\d{10}$/.test(s);
  return /^460\d{12}$/.test(s);
}
/**
 * Mdn
 */
export function isMdn(s) {
  return /^(\d{11}|\d{13}|\d{15})$/.test(s);
}
/**
 * domain
 */
export function isDomain(s) {
  return /^(?=^.{3,255}$)[a-zA-Z0-9][-a-zA-Z0-9]{0,62}(\.[a-zA-Z0-9][-a-zA-Z0-9]{0,62})+$/.test(s);
}
/**
 * domain:port
 */
export function isDomainPort(s) {
  const reg = new RegExp(/^((ht|f)tps?:\/\/)?[\w-]+(\.[\w-]+)+(:\d{1,5}\/?)?$/);
  return reg.test(s);
}
/**
 * chinese
 */
export function checkChinese(val) {
  var reg = new RegExp('[\\u4E00-\\u9FFF]+', 'g');
  return reg.test(val);
}

/**
 * 英文/数字
 */
export function isNumEn(s) {
  return /^[0-9a-zA-Z\_]{1,}$/.test(s);
}

/**
 * 复杂密码  大小字母 数字特殊字符 中的三个
 */
export function isComplexPassword(val) {
    var reg = new RegExp('^(?![a-zA-Z]+$)(?![A-Z0-9]+$)(?![A-Z\W_!@#$%^&*`~()-+=:"|\'<>]+$)(?![a-z0-9]+$)(?![a-z\W_!@#$%^&*`~()-+=:"|\'<>]+$)(?![0-9\W_!@#$%^&*`~()-+=:"|\'<>]+$)[a-zA-Z0-9\W_!@#$%^&*`~()-+=:"|\'<>]{8,30}$');
  return reg.test(val);
}

/**
 * imsi
 */
export function isImsi(s) {
  return /^\d{15}$/.test(s);
}

/**
 *  包含中文分号
 */
export function isInput(s) {
  return /[；]$/.test(s);
}
/**
 *  邮箱正则
 */
export function isEmail(s) {
  return /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/.test(s);
}


export function validateMin(rule, value, callback, max) {
  // console.log(max, value);
  if (value && !isNum(value)) {
    callback(new Error('请输入数字'));
  } else if (max && value && Number(value) > Number(max)) {
    callback(new Error('输入值大于上限值，请重新输入'));
  } else {
    callback();
  }
};

export function validateMax(rule, value, callback, min) {
  // console.log(min, value);
  if (value && !isNum(value)) {
    callback(new Error('请输入数字'));
  } else if (min && value && Number(value) < Number(min)) {
    callback(new Error('输入值小于下限值，请重新输入'));
  } else {
    callback();
  }
};
