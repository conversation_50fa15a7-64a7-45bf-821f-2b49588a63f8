<template>
    <!-- 网络拓扑图 -->
    <!-- <div class="topology"> -->
    <cp-chart :options="options_graph_topology" />
    <!-- </div> -->
</template>
<script>
import Cp<PERSON><PERSON> from "@/components/CpChart";
import $ from "jquery";
import {
    graphData,
    graphLinks,
    getEdgeNodes,
    getEdgeLines,
    graphEndNodes,
    graphEndLinks,
    UPFimg
} from "../utils/data.js";
export default {
    name: "Topology",
    components: { CpChart },
    props: {
        UPFnum: [Number, String],
    },
    data() {
        return {
            options_graph_topology: {},
            levelOne: "image://data:image/png;base64,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",
            levelTwo: "image://data:image/png;base64,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"
            , topoData: {},
            point: [
                {
                    "symbol": "image://data:image/png;base64,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",
                    "symbolSize": 55,
                    "x": "6",
                    "name": "东五路",
                    "y": "15",
                    "lable": {
                        "textStyle": {
                            "color": "#00FF00",
                            "fontSize": "16"
                        }
                    },
                    "id": "1"
                },
                {
                    "symbol": this.levelTwo,
                    "symbolSize": 55,
                    "x": "10",
                    "name": "兰溪",
                    "y": "15",
                    "lable": {
                        "textStyle": {
                            "color": "#00FF00",
                            "fontSize": "16"
                        }
                    },
                    "id": "2"
                },
                {
                    "symbol": "",
                    "symbolSize": "",
                    "x": "9",
                    "name": "",
                    "y": "1",
                    "lable": {
                        "textStyle": {
                            "color": "#00FF00",
                            "fontSize": "16"
                        }
                    },
                    "id": "39"
                },
                {
                    "symbol": "",
                    "symbolSize": "",
                    "x": "10",
                    "name": "",
                    "y": "5",
                    "lable": {
                        "textStyle": {
                            "color": "#00FF00",
                            "fontSize": "16"
                        }
                    },
                    "id": "41"
                },
                {
                    "symbol": "",
                    "symbolSize": "",
                    "x": "15",
                    "name": "",
                    "y": "5",
                    "lable": {
                        "textStyle": {
                            "color": "#00FF00",
                            "fontSize": "16"
                        }
                    },
                    "id": "42"
                },
                {
                    "symbol": "",
                    "symbolSize": "",
                    "x": "15",
                    "name": "",
                    "y": "10",
                    "lable": {
                        "textStyle": {
                            "color": "#00FF00",
                            "fontSize": "16"
                        }
                    },
                    "id": "43"
                },
                {
                    "symbol": "",
                    "symbolSize": "",
                    "x": "10.5",
                    "name": "",
                    "y": "10",
                    "lable": {
                        "textStyle": {
                            "color": "#00FF00",
                            "fontSize": "16"
                        }
                    },
                    "id": "44"
                },
                {
                    "symbol": "",
                    "symbolSize": "",
                    "x": "15",
                    "name": "",
                    "y": "1",
                    "lable": {
                        "textStyle": {
                            "color": "#00FF00",
                            "fontSize": "16"
                        }
                    },
                    "id": "40"
                },
                {
                    "symbol": "demoSvg",
                    "symbolSize": "45",
                    "x": "15",
                    "name": "秦雅",
                    "y": "15",
                    "lable": {
                        "textStyle": {
                            "color": "#00FF00",
                            "fontSize": "16"
                        }
                    },
                    "id": "3"
                },
                {
                    "symbol": "demoSvg",
                    "symbolSize": "45",
                    "x": "20",
                    "name": "绿地",
                    "y": "15",
                    "lable": {
                        "textStyle": {
                            "color": "#00FF00",
                            "fontSize": "16"
                        }
                    },
                    "id": "4"
                },
                {
                    "symbol": "demoSvg",
                    "symbolSize": "45",
                    "x": "25",
                    "name": "望景",
                    "y": "15",
                    "lable": {
                        "textStyle": {
                            "color": "#00FF00",
                            "fontSize": "16"
                        }
                    },
                    "id": "5"
                },
                {
                    "symbol": "",
                    "symbolSize": "",
                    "x": "25",
                    "name": "",
                    "y": "10",
                    "lable": {
                        "textStyle": {
                            "color": "#00FF00",
                            "fontSize": "16"
                        }
                    },
                    "id": "47"
                },
                {
                    "symbol": "demoSvg",
                    "symbolSize": "45",
                    "x": "30",
                    "name": "雅荷",
                    "y": "15",
                    "lable": {
                        "textStyle": {
                            "color": "#00FF00",
                            "fontSize": "16"
                        }
                    },
                    "id": "6"
                },
                {
                    "symbol": "",
                    "symbolSize": "",
                    "x": "30",
                    "name": "",
                    "y": "10",
                    "lable": {
                        "textStyle": {
                            "color": "#00FF00",
                            "fontSize": "16"
                        }
                    },
                    "id": "48"
                },
                {
                    "symbol": "demoSvg",
                    "symbolSize": "45",
                    "x": "35",
                    "name": "华科广电",
                    "y": "15",
                    "lable": {
                        "textStyle": {
                            "color": "#00FF00",
                            "fontSize": "16"
                        }
                    },
                    "id": "7"
                },
                {
                    "symbol": "",
                    "symbolSize": "",
                    "x": "35",
                    "name": "",
                    "y": "5",
                    "lable": {
                        "textStyle": {
                            "color": "#00FF00",
                            "fontSize": "16"
                        }
                    },
                    "id": "46"
                },
                {
                    "symbol": "demoSvg",
                    "symbolSize": "45",
                    "x": "40",
                    "name": "华南城",
                    "y": "15",
                    "lable": {
                        "textStyle": {
                            "color": "#00FF00",
                            "fontSize": "16"
                        }
                    },
                    "id": "8"
                },
                {
                    "symbol": "demoSvg",
                    "symbolSize": "45",
                    "x": "40",
                    "name": "纺织城",
                    "y": "25",
                    "lable": {
                        "textStyle": {
                            "color": "#00FF00",
                            "fontSize": "16"
                        }
                    },
                    "id": "9"
                },
                {
                    "symbol": "demoSvg",
                    "symbolSize": "45",
                    "x": "35",
                    "name": "科技六路",
                    "y": "25",
                    "lable": {
                        "textStyle": {
                            "color": "#00FF00",
                            "fontSize": "16"
                        }
                    },
                    "id": "10"
                },
                {
                    "symbol": "",
                    "symbolSize": "",
                    "x": "36",
                    "name": "",
                    "y": "27",
                    "lable": {
                        "textStyle": {
                            "color": "#00FF00",
                            "fontSize": "16"
                        }
                    },
                    "id": "35"
                },
                {
                    "symbol": "",
                    "symbolSize": "",
                    "x": "38",
                    "name": "",
                    "y": "27",
                    "lable": {
                        "textStyle": {
                            "color": "#00FF00",
                            "fontSize": "16"
                        }
                    },
                    "id": "36"
                },
                {
                    "symbol": "",
                    "symbolSize": "",
                    "x": "38",
                    "name": "",
                    "y": "35",
                    "lable": {
                        "textStyle": {
                            "color": "#00FF00",
                            "fontSize": "16"
                        }
                    },
                    "id": "37"
                },
                {
                    "symbol": "demoSvg",
                    "symbolSize": "45",
                    "x": "30",
                    "name": "浐灞",
                    "y": "25",
                    "lable": {
                        "textStyle": {
                            "color": "#00FF00",
                            "fontSize": "16"
                        }
                    },
                    "id": "11"
                },
                {
                    "symbol": "demoSvg",
                    "symbolSize": "45",
                    "x": "25",
                    "name": "西咸",
                    "y": "25",
                    "lable": {
                        "textStyle": {
                            "color": "#00FF00",
                            "fontSize": "16"
                        }
                    },
                    "id": "12"
                },
                {
                    "symbol": "demoSvg",
                    "symbolSize": "45",
                    "x": "15",
                    "name": "水晶岛",
                    "y": "25",
                    "lable": {
                        "textStyle": {
                            "color": "#00FF00",
                            "fontSize": "16"
                        }
                    },
                    "id": "13"
                },
                {
                    "symbol": "",
                    "symbolSize": "0",
                    "x": "14.7",
                    "name": "",
                    "y": "30",
                    "lable": {
                        "textStyle": {
                            "color": "#00FF00",
                            "fontSize": "16"
                        }
                    },
                    "id": "33"
                },
                {
                    "symbol": "",
                    "symbolSize": "0",
                    "x": "16.7",
                    "name": "",
                    "y": "30",
                    "lable": {
                        "textStyle": {
                            "color": "#00FF00",
                            "fontSize": "16"
                        }
                    },
                    "id": "34"
                },
                {
                    "symbol": "demoSvg",
                    "symbolSize": "45",
                    "x": "10",
                    "name": "裕昌",
                    "y": "25",
                    "lable": {
                        "textStyle": {
                            "color": "#00FF00",
                            "fontSize": "16"
                        }
                    },
                    "id": "14"
                },
                {
                    "symbol": "demoSvg",
                    "symbolSize": "45",
                    "x": "6",
                    "name": "华汇",
                    "y": "25",
                    "lable": {
                        "textStyle": {
                            "color": "#00FF00",
                            "fontSize": "16"
                        }
                    },
                    "id": "15"
                },
                {
                    "symbol": "image://data:image/png;base64,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",
                    "symbolSize": "40",
                    "x": "1",
                    "name": "西一路",
                    "y": "20",
                    "lable": {
                        "textStyle": {
                            "color": "#82FFF9",
                            "fontSize": "14"
                        }
                    },
                    "id": "16"
                },
                {
                    "symbol": "http://***********/images/zb/18.gif",
                    "symbolSize": "40",
                    "x": "12",
                    "name": "曲江海洋馆",
                    "y": "1",
                    "lable": {
                        "textStyle": {
                            "color": "#82FFF9",
                            "fontSize": "14"
                        }
                    },
                    "id": "17"
                },
                {
                    "symbol": "http://***********/images/zb/18.gif",
                    "symbolSize": "40",
                    "x": "12",
                    "name": "曲江书城",
                    "y": "5",
                    "lable": {
                        "textStyle": {
                            "color": "#82FFF9",
                            "fontSize": "14"
                        }
                    },
                    "id": "18"
                },
                {
                    "symbol": "http://***********/images/zb/18.gif",
                    "symbolSize": "40",
                    "x": "12",
                    "name": "曲江池",
                    "y": "10",
                    "lable": {
                        "textStyle": {
                            "color": "#82FFF9",
                            "fontSize": "14"
                        }
                    },
                    "id": "19"
                },
                {
                    "symbol": "http://***********/images/zb/18.gif",
                    "symbolSize": "40",
                    "x": "27",
                    "name": "益田假日",
                    "y": "5",
                    "lable": {
                        "textStyle": {
                            "color": "#82FFF9",
                            "fontSize": "14"
                        }
                    },
                    "id": "20"
                },
                {
                    "symbol": "",
                    "symbolSize": "",
                    "x": "20",
                    "name": "",
                    "y": "5",
                    "lable": {
                        "textStyle": {
                            "color": "#82FFF9",
                            "fontSize": "14"
                        }
                    },
                    "id": "45"
                },
                {
                    "symbol": "http://***********/images/zb/18.gif",
                    "symbolSize": "40",
                    "x": "27.5",
                    "name": "文景公园",
                    "y": "10",
                    "lable": {
                        "textStyle": {
                            "color": "#82FFF9",
                            "fontSize": "14"
                        }
                    },
                    "id": "21"
                },
                {
                    "symbol": "http://***********/images/zb/18.gif",
                    "symbolSize": "40",
                    "x": "43.5",
                    "name": "浐灞世园会",
                    "y": "5",
                    "lable": {
                        "textStyle": {
                            "color": "#82FFF9",
                            "fontSize": "14"
                        }
                    },
                    "id": "22"
                },
                {
                    "symbol": "",
                    "symbolSize": "",
                    "x": "40",
                    "name": "",
                    "y": "5",
                    "lable": {
                        "textStyle": {
                            "color": "#82FFF9",
                            "fontSize": "14"
                        }
                    },
                    "id": "49"
                },
                {
                    "symbol": "",
                    "symbolSize": "",
                    "x": "47",
                    "name": "",
                    "y": "5",
                    "lable": {
                        "textStyle": {
                            "color": "#82FFF9",
                            "fontSize": "14"
                        }
                    },
                    "id": "50"
                },
                {
                    "symbol": "",
                    "symbolSize": "",
                    "x": "47",
                    "name": "",
                    "y": "15",
                    "lable": {
                        "textStyle": {
                            "color": "#82FFF9",
                            "fontSize": "14"
                        }
                    },
                    "id": "51"
                },
                {
                    "symbol": "http://***********/images/zb/18.gif",
                    "symbolSize": "40",
                    "x": "43",
                    "name": "浐灞万枫酒店",
                    "y": "10",
                    "lable": {
                        "textStyle": {
                            "color": "#82FFF9",
                            "fontSize": "14"
                        }
                    },
                    "id": "23"
                },
                {
                    "symbol": "",
                    "symbolSize": "",
                    "x": "43",
                    "name": "",
                    "y": "6.9",
                    "lable": {
                        "textStyle": {
                            "color": "#82FFF9",
                            "fontSize": "14"
                        }
                    },
                    "id": "52"
                },
                {
                    "symbol": "",
                    "symbolSize": "",
                    "x": "40.1",
                    "name": "",
                    "y": "6.9",
                    "lable": {
                        "textStyle": {
                            "color": "#82FFF9",
                            "fontSize": "14"
                        }
                    },
                    "id": "53"
                },
                {
                    "symbol": "",
                    "symbolSize": "",
                    "x": "43",
                    "name": "",
                    "y": "15",
                    "lable": {
                        "textStyle": {
                            "color": "#82FFF9",
                            "fontSize": "14"
                        }
                    },
                    "id": "54"
                },
                {
                    "symbol": "http://***********/images/zb/18.gif",
                    "symbolSize": "40",
                    "x": "43",
                    "name": "砂之船",
                    "y": "20",
                    "lable": {
                        "textStyle": {
                            "color": "#82FFF9",
                            "fontSize": "14"
                        }
                    },
                    "id": "24"
                },
                {
                    "symbol": "",
                    "symbolSize": "",
                    "x": "43",
                    "name": "",
                    "y": "25",
                    "lable": {
                        "textStyle": {
                            "color": "#82FFF9",
                            "fontSize": "14"
                        }
                    },
                    "id": "55"
                },
                {
                    "symbol": "http://***********/images/zb/18.gif",
                    "symbolSize": "40",
                    "x": "35",
                    "name": "高新四季风景",
                    "y": "30",
                    "lable": {
                        "textStyle": {
                            "color": "#82FFF9",
                            "fontSize": "14"
                        }
                    },
                    "id": "25"
                },
                {
                    "symbol": "http://***********/images/zb/18.gif",
                    "symbolSize": "40",
                    "x": "35",
                    "name": "北京银行",
                    "y": "35",
                    "lable": {
                        "textStyle": {
                            "color": "#82FFF9",
                            "fontSize": "14"
                        }
                    },
                    "id": "26"
                },
                {
                    "symbol": "",
                    "symbolSize": "",
                    "x": "15",
                    "name": "",
                    "y": "35",
                    "lable": {
                        "textStyle": {
                            "color": "#82FFF9",
                            "fontSize": "14"
                        }
                    },
                    "id": "38"
                },
                {
                    "symbol": "http://***********/images/zb/18.gif",
                    "symbolSize": "40",
                    "x": "10",
                    "name": "GX-Z",
                    "y": "30",
                    "lable": {
                        "textStyle": {
                            "color": "#82FFF9",
                            "fontSize": "14"
                        }
                    },
                    "id": "27"
                },
                {
                    "symbol": "http://***********/images/zb/18.gif",
                    "symbolSize": "40",
                    "x": "6",
                    "name": "鼓楼",
                    "y": "30",
                    "lable": {
                        "textStyle": {
                            "color": "#82FFF9",
                            "fontSize": "14"
                        }
                    },
                    "id": "28"
                },
                {
                    "symbol": "image://data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADMAAAAoCAYAAABTsMJyAAAMwklEQVRoga2ZC3Ac9X3HP7t7e0/dnd4nnV4+YcuP2NjYxtjYoGDSOAnDy2kghDJNKaGBpqGFkNY0JaS0zJA2dOLMNAWmkARMCx0mEFI7GAzYYGNbtmLLtvyQZEvWWyedHqfTPXR32/nv/2QJ1zorln6am7097f73//09v7/fKsZIM6QToHtgPAzpcbA4IRUHPQcSQ6DZAQUwQNEgHgJ7obxWnBspea/ulWtZ3KAYEO2F2MCdjI/9AEV9h5zKp8hdCNGg/BgG2HIzzxmB+PDkbxYbREOQSoCqg12snYToEKTGwZ4L1hyIj0AsBKoNC3MqhnywpsPQyWUko9/BVfEg+Ush3LqS4ea19B/5Cc7S97C4YHx0Tp+uzs0ywmIq5gYTwyrBAz9AtTXgv/lB8pdJq7oDUP5Hm1CtOwkefplwayGaFTSbtMYcyCzBCBCKBCHcsv/QAySjLRSsfhrf9XKjF0vxtVD+hW+iu5rprXuGSDfY86VFjfSsdjM7MJoDDAWGm9cQH3wdd/WLlNwwD1dZ9vtseeCv9eKv3cJY3346d9/OeARs3swFV2apKwAj4sIik0K0107/oa1otgOUbbqLvKWXviURvvTveTVQfdt16O636K3bTuhkpRlvIiFcgev9AWAymUxkKhG4wbpHTZcqrf0ritfJmLmUnP8A3toM7z8ss9GlxL8eFtz1ZXTPGbr2/YyhZgvOwkw8zdz1ZpCaB6U7yeCGaN8mUtHHyQncTO6i6VeO9kPjq3DqdWnFZBS8AVjxF1Dx+envGzwDvfVnIf1DXKWv4ipFuqAb0qmsqTm7ZYSpxUaEhkbb8hhs2Ibu+h3lX84ORGjz7HZoeQecReDygbtCKAJO/Ar6T0x/r3C9RV+vxlX2Cn1H9tN9cDGKRSaJy1hpGss4pBacPhhqhNH2H+Ese4S8z3mx5mVd8IIShGVFrLz7LbmJsX5YfDes/K58jtVz+XWENdt3Q//xlyhc+rf4rukn3A2xIXDkz8QyhowLATB09EaiwQbylj+Jb8PMgJgqEunaLtcS7EAoRhwFQPG/mQARIpQa+BLMv+1+QmdaaNl+D5EecBVPMpIpMgnGdCmbjI2R034Gj/8G1bmbytuXZXWpbJKMQXxQai7WD/GhK1tHuN6q73rIX/ga3XVHaNl+vaksZ/FnXE8xhpuANDj9EGpwMtq2BVf5o+QucWIrmP4BYpHWnVJz00kqBsHjUoMiaEXs5F41/fU9dTJTVtw0/TUiGTT/FkbOv4w38Czl159mpA2SSRQjFpSEcKzrKySjP8dTU5nVEiIOuvfDyf+CYAOs+6E0+4SGRPoVNMW3StajS0lsUN4raorpLoq89vgvoO19WPpNqL4FipZNv4+BM9D8GwOL4yFcvhcpXpFWjMFjVxNu3YqzrJaCa6a/WYhItXU/kZvJrZYJYqBRxsKECOYrLHD3RzJ9XkpEUP/6NukmygRgA9yVmPUldEoy5uKrofZfIH/h9Hvq+BRadzXhnfc3inHkmWGuutdDTmV2IEKENvuOiDoAnftA1cC/Tga7CHIhyTFJS657AnTnpdcJnYZDz4HNMxm2wjLDrRA8illbqr4A+TVQvgEcRdn3lYzDnsdQjPonx7DmOiiphYKVlwc0IS2/lTGz4UeSa82FNG6Dzk9g1SOQP8OkE2qE8zsEP0woxvHn+oj2FpnB6lkAFbdAzry52Vx0AA7+WNYuUQ/8a+HqB7LcYGRiaCZrB6HpdejZJ63qKA5bzPwvCqY1F8Jn4fQLkLccyr44e40L8wt3FIoSwESRyyozAGKkSJ1+A6X7Y1SRvHL8EoyRnug0M8XHUSwf3P0BhI5JQKU3XjkYt18mA1FvRKCLhDELSXYfJCmADBzH4i3HcFehiLKSYdhTcqdh5mp0G8y7BoLtcPqX0PURBDZjtr4zFgPa9wgakmkXMj38WA+ceAV8K6AwS9q9SFLDbUR//wJG3xE0mxMtr4a0AkoqiaJN1n31gmlFnfC6aTx2il/89D8ZHElC5RJI9EHDVjjziiSKMxJF0pdzv4OhlgyzcELbB9C1V36fgRjJGJGjrzL83vdIdOw3s5xhL8RIJTHSadLpNMaUvsdyAYjYQCJOSXkJve99QtPpFsoVA7+rEOxJOLcduj6B6q9C5Rcvv5PKjfKzZws0vSXT93V/B0vunRGQseZdhOtfIj3UjDW3Ei232ty4kU5hKKrpWYahmIBUTZOl12h4NkgyWmgaKRaHkgLI90JXD2+/sYP6xhYe/fbdeGvmQWcrhHrAXSU6RCi9fmaGOrlN1g4B7jIS6znB4P5/J9FzFIvVjtXrN7mpqqqomoqmauZRnGuZowCjqmpYMQ79vWFaRhXEzc7p+uMEB4fZUHstpFLs/vQIfreDw83tbFg6n/JAKYx0wtgY+G+AwK2y6ZqlJCMDhA6/ysjxN1HGI9gKAmi6FVUAMTetTW5+EkDmCFo6jvbUlkdKGe1YbQ7hnG6Gh8O89N//S7HTQUl1GYHrllFg09n/ST3n+vpZUFmCrjlRPLnQUQft78s6kr9k+tb5MhI68ibn336USMuH6DlF6J5Sc4hoRrOiYP7JE/OjKBMf1SSmSiqGmrtgl2KMdYvAXknfwX+kp+4WkQRScSvh8QTb391LqC/Eww9uRq0qhc4eGutP8eKvd/Ont65nxU0rIRSErnOQUw4LNsO8LCz6Ihk5u5fuj7YS7WpAd3qxeX3SEhe0Ly1ysUtpFosJQk1FsRYtPqX7Vj2muMu2a089/ueivnTjnvcaVncP8Xipmuz12102ilxuWgeHcaVSHDt6hkBBLkVlhdgNsDtttLX10Hd+gNJAAOL9cHYnjLRLbuYqmT64gy107Po3zu94mvGRHmz5lWi2zERmom4qSItkej3TQqqKkUqQjoXQc0p7bf5VW61l6zcrnsom0TMpxtBJOXURhU0wAUEOOz98is69T6BGdPwBwm29PPf8m1xV6eNPvrYRbLr5sAO7DrFtVz0P37Ge+TXlWJQ09LTAeAwW3gXL/kwORaZIx54XaHv3xxiJUZxF1ej2HBTFyAR3xhKaOhnk5rmwjkI6PoRudeD0LX7DtfD2b2HNGTHC7Sgi9acTE2Asku2mknJoLcBBJV0fP07rju9gF2NUD+2dQSoKvXIonkqD287Y0CjhcIRt7/+eqkIPd25cjhqLwNB5yCmDxffA/FvpO/kx57b/M5GOo9hzS7G5C1AFiKnBbLrT1GylmeBS8RHUdBRPYN0u9+I7/kEj9akhsq9o+JKjKFa3yVw+C0bQeN0l29uJBq33wK107nuSSMtqdCskrbIBMxN9GuzSSqca26g708ndtZ/DahU+nZZFVkvSMVrI4QP7cOkpXEVVqJrFzEATVtCmxkfGKpqmY4iBxvgYrsJ5XZ6qNVtd8296Fkchqa6DKBYXimAW4+HLgDEzm0+6izCh7hAt7X2cfed5RjsdpjtOTHAEoLRou63SucORTG9jSMpn12g+eoLGzgj5BQVmgCsXAvr/B7csgAbJcC92TwGFNTc+41206WnF5okZYmamO0mPtKHoOSiCKk0Bk+WVRiYSxTDP8ILv2ldwFH1I176H6a3fwmgn2PIzrbEBkZicJRgZ4mfSDPFdRXPkoWjjpIUbi00LeihSr5I2L0unRXArpFMp4uEguqZQvOyWl/MW3fyC1WLZnxJKTcRMJShZ0v/lC4PQtnCrsaCYlHdQsfEJVj+2HP/6nWaLHOmb3Hx6KhBxLl3RMNIZKmLIozg3uVXmHIV4ZIj46ACe0oXHAp//9h/71t9/v7Vw/v74cIfJ0TKFJqvM/GWTWMzsS/rAt6aBJfdtwrd6I83vvET/iSrT7azeDM/LxNMUei44VNowUAQAxcgQRIVENEIqHiYnv8SoWvPVh/IX3/w8yQTJwU4Uq8h02owbtj/8zZkwszkHC0HR1R+Qt6CGczsfMpPEYHM+9jwZTxMWMj4LSDHrh0YymWBsqJucvBLKVmz61+KF6//D4VvUkoyOkoqG0ETLMANrzA4ME1XMgDGRrawJytb9lIraX9L09s9p3fV1cxQrGj0BPC0tZZIT08UURgc60XWd0sU37g1s+MZf2y3KoUQsQmwkiKbbssZFNpndyybxUPECdfg8WJ1DLL3vHtZ+fznlG/aYY9TRrokLzWwVGx1kuLuJPH9N74o7vn/nkk1/ucFRUHVodKCdVCJq8q3ZyNy8oBUjJzEcHB8Dd1kDa75Xi3/tN2jZsYXBw0vHowZDfW1UBWoiVTfd+09Va7/2M1XTIqPdTege3xVb4mKZw7fNmcmkGFyIoWDFDa9Rtu41Tv5qq7Pvf+5cuOYrJ5Z/6YEHHDl5HfHxGKmR4cl3onMhwP8Bbu5nxOUW+P8AAAAASUVORK5CYII=",
                    "symbolSize": "0",
                    "x": "1",
                    "name": "",
                    "y": "15",
                    "lable": {
                        "textStyle": {
                            "color": "#00FF00",
                            "fontSize": "16"
                        }
                    },
                    "id": "29"
                },
                {
                    "symbol": "image://data:image/png;base64,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",
                    "symbolSize": "0",
                    "x": "1",
                    "name": "",
                    "y": "25",
                    "lable": {
                        "textStyle": {
                            "color": "#00FF00",
                            "fontSize": "16"
                        }
                    },
                    "id": "30"
                },
                {
                    "symbol": "image://data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADMAAAAoCAYAAABTsMJyAAAMwklEQVRoga2ZC3Ac9X3HP7t7e0/dnd4nnV4+YcuP2NjYxtjYoGDSOAnDy2kghDJNKaGBpqGFkNY0JaS0zJA2dOLMNAWmkARMCx0mEFI7GAzYYGNbtmLLtvyQZEvWWyedHqfTPXR32/nv/2QJ1zorln6am7097f73//09v7/fKsZIM6QToHtgPAzpcbA4IRUHPQcSQ6DZAQUwQNEgHgJ7obxWnBspea/ulWtZ3KAYEO2F2MCdjI/9AEV9h5zKp8hdCNGg/BgG2HIzzxmB+PDkbxYbREOQSoCqg12snYToEKTGwZ4L1hyIj0AsBKoNC3MqhnywpsPQyWUko9/BVfEg+Ush3LqS4ea19B/5Cc7S97C4YHx0Tp+uzs0ywmIq5gYTwyrBAz9AtTXgv/lB8pdJq7oDUP5Hm1CtOwkefplwayGaFTSbtMYcyCzBCBCKBCHcsv/QAySjLRSsfhrf9XKjF0vxtVD+hW+iu5rprXuGSDfY86VFjfSsdjM7MJoDDAWGm9cQH3wdd/WLlNwwD1dZ9vtseeCv9eKv3cJY3346d9/OeARs3swFV2apKwAj4sIik0K0107/oa1otgOUbbqLvKWXviURvvTveTVQfdt16O636K3bTuhkpRlvIiFcgev9AWAymUxkKhG4wbpHTZcqrf0ritfJmLmUnP8A3toM7z8ss9GlxL8eFtz1ZXTPGbr2/YyhZgvOwkw8zdz1ZpCaB6U7yeCGaN8mUtHHyQncTO6i6VeO9kPjq3DqdWnFZBS8AVjxF1Dx+envGzwDvfVnIf1DXKWv4ipFuqAb0qmsqTm7ZYSpxUaEhkbb8hhs2Ibu+h3lX84ORGjz7HZoeQecReDygbtCKAJO/Ar6T0x/r3C9RV+vxlX2Cn1H9tN9cDGKRSaJy1hpGss4pBacPhhqhNH2H+Ese4S8z3mx5mVd8IIShGVFrLz7LbmJsX5YfDes/K58jtVz+XWENdt3Q//xlyhc+rf4rukn3A2xIXDkz8QyhowLATB09EaiwQbylj+Jb8PMgJgqEunaLtcS7EAoRhwFQPG/mQARIpQa+BLMv+1+QmdaaNl+D5EecBVPMpIpMgnGdCmbjI2R034Gj/8G1bmbytuXZXWpbJKMQXxQai7WD/GhK1tHuN6q73rIX/ga3XVHaNl+vaksZ/FnXE8xhpuANDj9EGpwMtq2BVf5o+QucWIrmP4BYpHWnVJz00kqBsHjUoMiaEXs5F41/fU9dTJTVtw0/TUiGTT/FkbOv4w38Czl159mpA2SSRQjFpSEcKzrKySjP8dTU5nVEiIOuvfDyf+CYAOs+6E0+4SGRPoVNMW3StajS0lsUN4raorpLoq89vgvoO19WPpNqL4FipZNv4+BM9D8GwOL4yFcvhcpXpFWjMFjVxNu3YqzrJaCa6a/WYhItXU/kZvJrZYJYqBRxsKECOYrLHD3RzJ9XkpEUP/6NukmygRgA9yVmPUldEoy5uKrofZfIH/h9Hvq+BRadzXhnfc3inHkmWGuutdDTmV2IEKENvuOiDoAnftA1cC/Tga7CHIhyTFJS657AnTnpdcJnYZDz4HNMxm2wjLDrRA8illbqr4A+TVQvgEcRdn3lYzDnsdQjPonx7DmOiiphYKVlwc0IS2/lTGz4UeSa82FNG6Dzk9g1SOQP8OkE2qE8zsEP0woxvHn+oj2FpnB6lkAFbdAzry52Vx0AA7+WNYuUQ/8a+HqB7LcYGRiaCZrB6HpdejZJ63qKA5bzPwvCqY1F8Jn4fQLkLccyr44e40L8wt3FIoSwESRyyozAGKkSJ1+A6X7Y1SRvHL8EoyRnug0M8XHUSwf3P0BhI5JQKU3XjkYt18mA1FvRKCLhDELSXYfJCmADBzH4i3HcFehiLKSYdhTcqdh5mp0G8y7BoLtcPqX0PURBDZjtr4zFgPa9wgakmkXMj38WA+ceAV8K6AwS9q9SFLDbUR//wJG3xE0mxMtr4a0AkoqiaJN1n31gmlFnfC6aTx2il/89D8ZHElC5RJI9EHDVjjziiSKMxJF0pdzv4OhlgyzcELbB9C1V36fgRjJGJGjrzL83vdIdOw3s5xhL8RIJTHSadLpNMaUvsdyAYjYQCJOSXkJve99QtPpFsoVA7+rEOxJOLcduj6B6q9C5Rcvv5PKjfKzZws0vSXT93V/B0vunRGQseZdhOtfIj3UjDW3Ei232ty4kU5hKKrpWYahmIBUTZOl12h4NkgyWmgaKRaHkgLI90JXD2+/sYP6xhYe/fbdeGvmQWcrhHrAXSU6RCi9fmaGOrlN1g4B7jIS6znB4P5/J9FzFIvVjtXrN7mpqqqomoqmauZRnGuZowCjqmpYMQ79vWFaRhXEzc7p+uMEB4fZUHstpFLs/vQIfreDw83tbFg6n/JAKYx0wtgY+G+AwK2y6ZqlJCMDhA6/ysjxN1HGI9gKAmi6FVUAMTetTW5+EkDmCFo6jvbUlkdKGe1YbQ7hnG6Gh8O89N//S7HTQUl1GYHrllFg09n/ST3n+vpZUFmCrjlRPLnQUQft78s6kr9k+tb5MhI68ibn336USMuH6DlF6J5Sc4hoRrOiYP7JE/OjKBMf1SSmSiqGmrtgl2KMdYvAXknfwX+kp+4WkQRScSvh8QTb391LqC/Eww9uRq0qhc4eGutP8eKvd/Ont65nxU0rIRSErnOQUw4LNsO8LCz6Ihk5u5fuj7YS7WpAd3qxeX3SEhe0Ly1ysUtpFosJQk1FsRYtPqX7Vj2muMu2a089/ueivnTjnvcaVncP8Xipmuz12102ilxuWgeHcaVSHDt6hkBBLkVlhdgNsDtttLX10Hd+gNJAAOL9cHYnjLRLbuYqmT64gy107Po3zu94mvGRHmz5lWi2zERmom4qSItkej3TQqqKkUqQjoXQc0p7bf5VW61l6zcrnsom0TMpxtBJOXURhU0wAUEOOz98is69T6BGdPwBwm29PPf8m1xV6eNPvrYRbLr5sAO7DrFtVz0P37Ge+TXlWJQ09LTAeAwW3gXL/kwORaZIx54XaHv3xxiJUZxF1ej2HBTFyAR3xhKaOhnk5rmwjkI6PoRudeD0LX7DtfD2b2HNGTHC7Sgi9acTE2Asku2mknJoLcBBJV0fP07rju9gF2NUD+2dQSoKvXIonkqD287Y0CjhcIRt7/+eqkIPd25cjhqLwNB5yCmDxffA/FvpO/kx57b/M5GOo9hzS7G5C1AFiKnBbLrT1GylmeBS8RHUdBRPYN0u9+I7/kEj9akhsq9o+JKjKFa3yVw+C0bQeN0l29uJBq33wK107nuSSMtqdCskrbIBMxN9GuzSSqca26g708ndtZ/DahU+nZZFVkvSMVrI4QP7cOkpXEVVqJrFzEATVtCmxkfGKpqmY4iBxvgYrsJ5XZ6qNVtd8296Fkchqa6DKBYXimAW4+HLgDEzm0+6izCh7hAt7X2cfed5RjsdpjtOTHAEoLRou63SucORTG9jSMpn12g+eoLGzgj5BQVmgCsXAvr/B7csgAbJcC92TwGFNTc+41206WnF5okZYmamO0mPtKHoOSiCKk0Bk+WVRiYSxTDP8ILv2ldwFH1I176H6a3fwmgn2PIzrbEBkZicJRgZ4mfSDPFdRXPkoWjjpIUbi00LeihSr5I2L0unRXArpFMp4uEguqZQvOyWl/MW3fyC1WLZnxJKTcRMJShZ0v/lC4PQtnCrsaCYlHdQsfEJVj+2HP/6nWaLHOmb3Hx6KhBxLl3RMNIZKmLIozg3uVXmHIV4ZIj46ACe0oXHAp//9h/71t9/v7Vw/v74cIfJ0TKFJqvM/GWTWMzsS/rAt6aBJfdtwrd6I83vvET/iSrT7azeDM/LxNMUei44VNowUAQAxcgQRIVENEIqHiYnv8SoWvPVh/IX3/w8yQTJwU4Uq8h02owbtj/8zZkwszkHC0HR1R+Qt6CGczsfMpPEYHM+9jwZTxMWMj4LSDHrh0YymWBsqJucvBLKVmz61+KF6//D4VvUkoyOkoqG0ETLMANrzA4ME1XMgDGRrawJytb9lIraX9L09s9p3fV1cxQrGj0BPC0tZZIT08UURgc60XWd0sU37g1s+MZf2y3KoUQsQmwkiKbbssZFNpndyybxUPECdfg8WJ1DLL3vHtZ+fznlG/aYY9TRrokLzWwVGx1kuLuJPH9N74o7vn/nkk1/ucFRUHVodKCdVCJq8q3ZyNy8oBUjJzEcHB8Dd1kDa75Xi3/tN2jZsYXBw0vHowZDfW1UBWoiVTfd+09Va7/2M1XTIqPdTege3xVb4mKZw7fNmcmkGFyIoWDFDa9Rtu41Tv5qq7Pvf+5cuOYrJ5Z/6YEHHDl5HfHxGKmR4cl3onMhwP8Bbu5nxOUW+P8AAAAASUVORK5CYII=",
                    "symbolSize": "0",
                    "x": "-4",
                    "name": "",
                    "y": "30",
                    "lable": {
                        "textStyle": {
                            "color": "#00FF00",
                            "fontSize": "16"
                        }
                    },
                    "id": "31"
                },
                {
                    "symbol": "image://data:image/png;base64,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",
                    "symbolSize": "0",
                    "x": "-4",
                    "name": "",
                    "y": "14.8",
                    "lable": {
                        "textStyle": {
                            "color": "#00FF00",
                            "fontSize": "16"
                        }
                    },
                    "id": "32"
                },
            ],
            source: [{
                source: '1',
                target: '2',
                "lineStyle": {
                    "color": "#7D5A2C",
                    "type": "boid"
                }
            }, {
                "source": "2",
                "target": "3",
                "lineStyle": {
                    "color": "#7D5A2C",
                    "type": "boid"
                }
            }, {
                "source": "3",
                "target": "4",
                "lineStyle": {
                    "color": "#7D5A2C",
                    "type": "boid"
                }
            }, {
                "source": "4",
                "target": "5",
                "lineStyle": {
                    "color": "#7D5A2C",
                    "type": "boid"
                }
            }, {
                "source": "5",
                "target": "6",
                "lineStyle": {
                    "color": "#7D5A2C",
                    "type": "boid"
                }
            },
            {
                "source": "6",
                "target": "7",
                "lineStyle": {
                    "color": "#7D5A2C",
                    "type": "boid"
                }
            }, {
                "source": "7",
                "target": "8",
                "lineStyle": {
                    "color": "#7D5A2C",
                    "type": "boid"
                }
            }, {
                "source": "8",
                "target": "9",
                "lineStyle": {
                    "color": "#7D5A2C",
                    "type": "boid"
                }
            },
            {
                "source": "9",
                "target": "10",
                "lineStyle": {
                    "color": "#7D5A2C",
                    "type": "boid"
                }
            },
            {
                "source": "10",
                "target": "11",
                "lineStyle": {
                    "color": "#7D5A2C",
                    "type": "boid"
                }
            },
            {
                "source": "11",
                "target": "12",
                "lineStyle": {
                    "color": "#7D5A2C",
                    "type": "boid"
                }
            },
            {
                "source": "12",
                "target": "13",
                "lineStyle": {
                    "color": "#7D5A2C",
                    "type": "boid"
                }
            },
            {
                "source": "13",
                "target": "14",
                "lineStyle": {
                    "color": "#7D5A2C",
                    "type": "boid"
                }
            },
            {
                "source": "14",
                "target": "15",
                "lineStyle": {
                    "color": "#7D5A2C",
                    "type": "boid"
                }
            }, {
                "source": "15",
                "target": "1",
                "lineStyle": {
                    "color": "#7D5A2C",
                    "type": "boid"
                }
            },
            {
                "source": "16",
                "target": "29",
                "lineStyle": {
                    "color": "#298252",
                    "type": "boid"
                }
            },
            {
                "source": "29",
                "target": "1",
                "lineStyle": {
                    "color": "#298252",
                    "type": "boid"
                }
            },
            {
                "source": "16",
                "target": "30",
                "lineStyle": {
                    "color": "#2D63A2",
                    "type": "boid"
                }
            },
            {
                "source": "30",
                "target": "15",
                "lineStyle": {
                    "color": "#2D63A2",
                    "type": "boid"
                }
            },
            // {
            //     "source": "16",
            //     "target": "1",
            //     "lineStyle": {
            //         "color": "#298252",
            //         "type": "boid"
            //     }
            // },
            // {
            //     "source": "16",
            //     "target": "15",
            //     "lineStyle": {
            //         "color": "#2D63A2",
            //         "type": "boid"
            //     }
            // },
            {
                "source": "28",
                "target": "31",
                "lineStyle": {
                    "color": "#298252",
                    "type": "boid"
                }
            },
            {
                "source": "31",
                "target": "32",
                "lineStyle": {
                    "color": "#298252",
                    "type": "boid"
                }
            }, {
                "source": "32",
                "target": "1",
                "lineStyle": {
                    "color": "#298252",
                    "type": "boid"
                }
            }, {
                "source": "28",
                "target": "15",
                "lineStyle": {
                    "color": "#2D63A2",
                    "type": "boid"
                }
            }, {
                "source": "27",
                "target": "14",
                "lineStyle": {
                    "color": "#298252",
                    "type": "boid"
                }
            },
            {
                "source": "27",
                "target": "33",
                "lineStyle": {
                    "color": "#2D63A2",
                    "type": "boid"
                }
            },
            {
                "source": "33",
                "target": "13",
                "lineStyle": {
                    "color": "#2D63A2",
                    "type": "boid"
                }
            }, {
                "source": "25",
                "target": "34",
                "lineStyle": {
                    "color": "#298252",
                    "type": "boid"
                }
            }, {
                "source": "34",
                "target": "13",
                "lineStyle": {
                    "color": "#298252",
                    "type": "boid"
                }
            },
            {
                "source": "25",
                "target": "10",
                "lineStyle": {
                    "color": "#2D63A2",
                    "type": "boid"
                }
            }, {
                "source": "26",
                "target": "38",
                "lineStyle": {
                    "color": "#298252",
                    "type": "boid"
                }
            },
            {
                "source": "26",
                "target": "37",
                "lineStyle": {
                    "color": "#2D63A2",
                    "type": "boid"
                }
            }, {
                "source": "37",
                "target": "36",
                "lineStyle": {
                    "color": "#2D63A2",
                    "type": "boid"
                }
            }, {
                "source": "36",
                "target": "35",
                "lineStyle": {
                    "color": "#2D63A2",
                    "type": "boid"
                }
            }, {
                "source": "35",
                "target": "10",
                "lineStyle": {
                    "color": "#2D63A2",
                    "type": "boid"
                }
            }, {
                "source": "38",
                "target": "13",
                "lineStyle": {
                    "color": "#298252",
                    "type": "boid"
                }
            }, {
                "source": "17",
                "target": "39",
                "lineStyle": {
                    "color": "#298252",
                    "type": "boid"
                }
            }, {
                "source": "39",
                "target": "2",
                "lineStyle": {
                    "color": "#298252",
                    "type": "boid"
                }
            },
            {
                "source": "17",
                "target": "40",
                "lineStyle": {
                    "color": "#2D63A2",
                    "type": "boid"
                }
            }, {
                "source": "40",
                "target": "3",
                "lineStyle": {
                    "color": "#2D63A2",
                    "type": "boid"
                }
            }, {
                "source": "18",
                "target": "41",
                "lineStyle": {
                    "color": "#298252",
                    "type": "boid"
                }
            }, {
                "source": "41",
                "target": "2",
                "lineStyle": {
                    "color": "#298252",
                    "type": "boid"
                }
            },
            {
                "source": "18",
                "target": "42",
                "lineStyle": {
                    "color": "#2D63A2",
                    "type": "boid"
                }
            },
            {
                "source": "42",
                "target": "3",
                "lineStyle": {
                    "color": "#2D63A2",
                    "type": "boid"
                }
            }, {
                "source": "19",
                "target": "44",
                "lineStyle": {
                    "color": "#298252",
                    "type": "boid"
                }
            }, {
                "source": "44",
                "target": "2",
                "lineStyle": {
                    "color": "#298252",
                    "type": "boid"
                }
            },
            {
                "source": "19",
                "target": "43",
                "lineStyle": {
                    "color": "#2D63A2",
                    "type": "boid"
                }
            },
            {
                "source": "43",
                "target": "3",
                "lineStyle": {
                    "color": "#2D63A2",
                    "type": "boid"
                }
            },
            {
                "source": "20",
                "target": "45",
                "lineStyle": {
                    "color": "#298252",
                    "type": "boid"
                }
            }, {
                "source": "45",
                "target": "4",
                "lineStyle": {
                    "color": "#298252",
                    "type": "boid"
                }
            },
            {
                "source": "20",
                "target": "46",
                "lineStyle": {
                    "color": "#2D63A2",
                    "type": "boid"
                }
            }, {
                "source": "46",
                "target": "7",
                "lineStyle": {
                    "color": "#2D63A2",
                    "type": "boid"
                }
            }, {
                "source": "21",
                "target": "47",
                "lineStyle": {
                    "color": "#298252",
                    "type": "boid"
                }
            }, {
                "source": "47",
                "target": "5",
                "lineStyle": {
                    "color": "#298252",
                    "type": "boid"
                }
            },
            {
                "source": "21",
                "target": "48",
                "lineStyle": {
                    "color": "#2D63A2",
                    "type": "boid"
                }
            }, {
                "source": "48",
                "target": "6",
                "lineStyle": {
                    "color": "#2D63A2",
                    "type": "boid"
                }
            },
            {
                "source": "22",
                "target": "49",
                "lineStyle": {
                    "color": "#298252",
                    "type": "boid"
                }
            }, {
                "source": "49",
                "target": "8",
                "lineStyle": {
                    "color": "#298252",
                    "type": "boid"
                }
            },
            {
                "source": "22",
                "target": "50",
                "lineStyle": {
                    "color": "#2D63A2",
                    "type": "boid"
                }
            }, {
                "source": "50",
                "target": "51",
                "lineStyle": {
                    "color": "#2D63A2",
                    "type": "boid"
                }
            }, {
                "source": "51",
                "target": "7",
                "lineStyle": {
                    "color": "#2D63A2",
                    "type": "boid"
                }
            }, {
                "source": "23",
                "target": "52",
                "lineStyle": {
                    "color": "#298252",
                    "type": "boid"
                }
            },
            {
                "source": "52",
                "target": "53",
                "lineStyle": {
                    "color": "#298252",
                    "type": "boid"
                }
            },
            {
                "source": "23",
                "target": "54",
                "lineStyle": {
                    "color": "#2D63A2",
                    "type": "boid"
                }
            }, {
                "source": "54",
                "target": "7",
                "lineStyle": {
                    "color": "#2D63A2",
                    "type": "boid"
                }
            }, {
                "source": "24",
                "target": "54",
                "lineStyle": {
                    "color": "#298252",
                    "type": "boid"
                }
            }, {
                "source": "54",
                "target": "7",
                "lineStyle": {
                    "color": "#298252",
                    "type": "boid"
                }
            },
            {
                "source": "24",
                "target": "55",
                "lineStyle": {
                    "color": "#2D63A2",
                    "type": "boid"
                }
            },
            {
                "source": "55",
                "target": "9",
                "lineStyle": {
                    "color": "#2D63A2",
                    "type": "boid"
                }
            }],
            icon: ""
        };
    },
    mounted() {
        this.$nextTick(() => {
          

            this.drawGraph(this.topoData);
            this.getSTData();
        });
    },
    methods: {
        getSTData(queryData) {
            let _this = this;
            _this.$axios.post("/protect-api/pointLine/getZbTopology", queryData).then((data) => {
                if (data.data.code === '0000') {
                    // console.log(data.data.data);
                    this.topoData = data.data.data;

                    for (let index = 0; index < this.topoData.pointList.length; index++) {
                        if (this.topoData.pointList[index].pos_type == '局向') {
                            // console.log(22);
                            this.topoData.pointList[index].symbol = this.levelOne;
                            // this.topoData.pointList[index].lable.textStyle.color = '#FEC270';

                        } else if (this.topoData.pointList[index].pos_type == '用户点'){
                            this.topoData.pointList[index].symbol = this.levelTwo;
                            // this.topoData.pointList[index].lable.textStyle.color = '#5DA6FA';

                        } else if (this.topoData.pointList[index].pos_type == '拐点') {
                            this.topoData.pointList[index].symbol = "";
                            this.topoData.pointList[index].symbolSize = "";
                        }

                    }
                    this.drawGraph(this.topoData, this.icon);
                }
            });
        },
        drawGraph(data, icon) {
            // console.log(icon);
            var self = this;
            let levelOne = "image://data:image/png;base64,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";
            let levelTwo = "image://data:image/png;base64,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";
            var res = [
                {
                    city: "上海",
                    local: "长虹",
                },
            ];

            if (res.length > 1) {
                $("#graphEcharts").css("height", "640px");
            } else {
                $("#graphEcharts").css("height", "420px");
            }
            // console.log(data.pointList);
            self.options_graph_topology = {
                animationDurationUpdate: 1500,
                animationEasingUpdate: "quinticInOut",
                series: [
                    {
                        type: "graph",
                        layout: "none",
                        // symbol: levelTwo,
                        // symbol: icon,

                        // symbolSize: 50,
                        itemStyle: {
                            // color: "#aaaaff",
                        },
                        tooltip: {
                            show: false,
                        },
                        // color: ["#1492F5"],
                        label: {
                            show: true, //是否显示标签。
                            position: "bottom", //标签的位置。['50%', '50%'] [x,y]   'inside'
                            textStyle: {
                                
                                //标签的字体样式
                                // color: "#FEC270", //字体颜色
                                fontStyle: "normal", //文字字体的风格 'normal'标准 'italic'斜体 'oblique' 倾斜
                                fontWeight: "bolder", //'normal'标准'bold'粗的'bolder'更粗的'lighter'更细的或100 | 200 | 300 | 400...
                                fontFamily: "sans-serif", //文字的字体系列
                                // fontSize: 12, //字体大小
                            },
                        },
                        data: data.pointList,
                        links: data.lineList,
                        lineStyle: {
                            opacity: 0.9,
                            // color: "#00F5FF",
                            width: 1.5,
                            // curveness: 0.01,
                        },
                    },
                ],
            };

            //  动态 边缘节点框与点

            // var edgeNodes = getEdgeNodes(2);

            // self.options_graph_topology.series[0].data =
            //     self.options_graph_topology.series[0].data.concat(edgeNodes);

            // 返回的 边缘节点数量  目前最大支持数量为2
            if (res.length > 1) {
                var nodes = [];

                nodes = JSON.parse(JSON.stringify(edgeNodes));
                for (var j = 0; j < edgeNodes.length; j++) {
                    nodes[j].y -= -40;

                    if (nodes[j].id) {
                        nodes[j].id += "node";
                    } else {
                        nodes[j].name += "z";
                    }
                }

                self.options_graph_topology.series[0].data =
                    self.options_graph_topology.series[0].data.concat(nodes);
            }

            // 动态边缘节点line

            // var edgeLines = getEdgeLines();

            // self.options_graph_topology.series[0].links =
            //     self.options_graph_topology.series[0].links.concat(edgeLines);

            // 返回的 边缘节点数量  目前最大支持数量为2
            if (res.length > 1) {
                var nodeLines = [];

                nodeLines = JSON.parse(JSON.stringify(edgeLines));
                for (var j = 0; j < edgeLines.length; j++) {
                    nodeLines[j].source = edgeLines[j].source + "z";
                    nodeLines[j].target = edgeLines[j].target + "z";
                }

                nodeLines.push({
                    source: "STNtorz",
                    target: "STNtor",
                });

                self.options_graph_topology.series[0].links =
                    self.options_graph_topology.series[0].links.concat(
                        nodeLines
                    );
            }

            // 5G专线与物理专线节点node 与 line

            // var endNodes = graphEndNodes("5g");
            // var endLinks = graphEndLinks("5g1");

            // self.options_graph_topology.series[0].data =
            //     self.options_graph_topology.series[0].data.concat(endNodes);
            // 返回的 5G专线与物理专线节点node 与 line  目前最大支持数量为2
            if (res.length > 1) {
                var endNodeList = [];

                endNodeList = JSON.parse(JSON.stringify(endNodes));
                for (var j = 0; j < endNodes.length; j++) {
                    endNodeList[j].y -= -40;

                    if (endNodeList[j].id) {
                        endNodeList[j].id += "node";
                    } else {
                        endNodeList[j].name += "z";
                    }
                }

                // self.options_graph_topology.series[0].data =
                //     self.options_graph_topology.series[0].data.concat(
                //         endNodeList
                //     );
            }

            // self.options_graph_topology.series[0].links =
            //     self.options_graph_topology.series[0].links.concat(endLinks);
            if (res.length > 1) {
                var endNodeLines = [];

                endNodeLines = JSON.parse(JSON.stringify(endLinks));
                for (var j = 0; j < endLinks.length; j++) {
                    endNodeLines[j].source = endLinks[j].source + "z";
                    endNodeLines[j].target = endLinks[j].target + "z";
                }

                self.options_graph_topology.series[0].links =
                    self.options_graph_topology.series[0].links.concat(
                        endNodeLines
                    );
            }

            // echarts setOption

            // this.myChart = echarts.init(
            //     document.getElementById("graphEcharts")
            // );

            // this.myChart.setOption(option);

            // this.myChart.on("click", function (params) {
            //     console.log(params);

            //     self.$emit("tabChange", params);
            // });
        },
    },
};
</script>
<style scoped lang='scss'>
.topology {
    width: 100%;
    height: 100%;
}
</style>