<template>
  <div style="height: 100%;height: 100%;  overflow: hidden;">
    <div class="header-long">
      <span class="header-right-middle">
        {{ title }}
      </span>
    </div>
    <div>
      <div v-show="showTail">
        <div class="buttonZ1">
          <span class="buttonSpanCur" id="buttonSpanId1" @click="changeScene(1)">制证网</span>
        </div>
        <div class="buttonZ2">
          <span class="buttonSpan" id="buttonSpanId4" @click="changeScene(4)">指挥网</span>
        </div>
        <div class="buttonZ3">
          <span class="buttonSpan" id="buttonSpanId2" @click="changeScene(3)">重保场景-线路</span>
        </div>
        <div class="buttonZ4">
          <span class="buttonSpan" id="buttonSpanId3" @click="changeScene(2)">重保场景-拓扑</span>
        </div>
        <div style="margin-left: 1260Px;position: fixed;z-index: 2;margin-top: 95Px;    cursor:pointer;"
             @click="changeJob">
          <img :src="urlButton" alt="" style="position: absolute;left: 45%;">
        </div>
      </div>
      <div>
        <div class="mapLeftTopz">
          <div v-show="showTail" class="right" style="z-index:5;cursor:pointer;" @click="closeShowTail"></div>
          <div v-show="!showTail" class="left" style="z-index:5;cursor:pointer;" @click="closeShowTail"></div>
        </div>
        <div class="mapLeftTop1" v-show="showTail">
          <span class="mapLeftTopTitle" >公告信息</span>
          <div class="mapLeftTop1div">
            <div class="item-list2">
              <span class="mapLeftTopName" v-for="(item, index) in eventData" :key="index">
                <span class="mapLeftTop1Radis"></span>
                <span style="margin-left: 5px;">{{ item.mesTitle }}</span>
                <span style="float: right;margin-right: 10Px;">{{ item.mesTime }}</span>
                <div style="padding: 2Px 22Px;cursor:pointer;" @click="noticePopoverClick(item)">
                  <span class="mapLeftTopText">
                    {{ item.mesName }}
                  </span>
                </div>
              </span>
            </div>
          </div>
        </div>
        <div class="mapLeftTop2" v-show="showTail">
          <span class="mapLeftTopTitle" style="line-height: 44Px;">数通设备</span>
          <span class="mapRightTopTitle" style="line-height: 44Px;    cursor:pointer;display: inline-block"
                @click="reset('1')"></span>
          <div class="mapLeftTop2Div" style="margin-top:16Px;margin-left: 16Px;">
            <div class="mapLeftTop2Img1"></div>
            <div class="mapLeftTop2Text">防火墙</div>
            <div class="mapLeftTop2Text2" style="cursor:pointer;" @click="sTDataClick('1')"><span
                style="color: #55A4FE;">{{ sTDataInfo.fhqNum ?
                sTDataInfo.fhqNum : 0 }}</span></div>
          </div>
          <div class="mapLeftTop2Div" style="margin-top:10Px;margin-left: 16Px;">
            <div class="mapLeftTop2Img2"></div>
            <div class="mapLeftTop2Text">交换机</div>
            <div class="mapLeftTop2Text2" style="cursor:pointer;" @click="sTDataClick('2')"><span span
                                                                                                  style="color: #5ADB95;">{{
                sTDataInfo.jhjNum ? sTDataInfo.jhjNum : 0 }}</span></div>
          </div>
          <div class="mapLeftTop2Div" style="margin-top:10Px;margin-left: 16Px;">
            <div class="mapLeftTop2Img3"></div>
            <div class="mapLeftTop2Text">安全设备</div>
            <div class="mapLeftTop2Text2" style="cursor:pointer;" @click="sTDataClick('3')"><span span
                                                                                                  style="color: #FABB61;">{{
                sTDataInfo.jhjNum ? sTDataInfo.posAqNum : 0 }}</span></div>
          </div>
        </div>
        <div class="mapLeftTop3" v-show="showTail">
          <span class="mapLeftTopTitle" @click="reinsuranceTeam()"><span>重保团队</span>
            <div class="mapLeftTopTitleDetalis" style="cursor:pointer;"></div>
          </span>
          <span class="mapRightTopTitle" style="line-height: 44Px;    cursor:pointer;display: inline-block" @click="reset('1')"></span>
          <div class="mapLeftTop2Div" style="margin-top:16Px;margin-left: 16Px;background: transparent">
            <div style="">
              <span @click="sTDataClick('4')" style="margin-left: 20Px;margin-top: 16Px;color: #55A4FE;width:90px;    cursor:pointer;"
                    class="mapLeftTop2Text2Top">{{
                  zbGroup.num }}</span>
              <span style="margin-left: 12Px;margin-top: 55Px;width:90px;" class="mapLeftTop2Text2Bottom">保障总人数</span>
            </div>
            <div style="">
              <span style="margin-left: 141Px;margin-top: 16Px;color: #5ADB95;width:90px;" class="mapLeftTop2Text2Top">{{
                  zbGroup.ydNum }}/{{ zbGroup.dgNum }}</span>
              <span style="margin-left: 147Px;margin-top: 55Px;width:90px;" class="mapLeftTop2Text2Bottom">应到/实到人数</span>
            </div>
            <div style="">
              <span style="margin-left: 291Px;margin-top: 16Px;color: #5ADB95;width:90px;" class="mapLeftTop2Text2Top">{{
                  zbGroup.dgRate
                }}</span>
              <span style="margin-left: 280Px;margin-top: 55Px;width:90px;" class="mapLeftTop2Text2Bottom">到岗率</span>
            </div>
          </div>
        </div>
      </div>
      <div v-show="showTail">
        <div class="mapRightTop3">
          <span class="mapLeftTopTitle"><span>{{ rightTitle }}</span></span>
          <div class="mapLeftTop1Div" style="margin-top:16Px;margin-left: 16Px;">
            <!-- <div class="mapLeftTop2Img1"></div> -->
            <!-- <dv-scroll-board :config="config" style="width:500px;height:220px" /> -->
            <div class="fix_content">
              <div class="item-list">
                <span class="mapLeftTopName item" v-for="(item, index) in XlInfoData" :key="index">
                  <div v-if="busTypeZ=='拓扑'">
                    <div class="alog">
                      <span class="mapLeftTop1Radis"></span>
                      <span class="mapLeftTop1Text" style="margin-left: 5px;">{{ item.lineName }}</span>
          <!-- <span style="float: right;margin-right: 10Px;">{{ item.mesTime }}</span> -->
                    </div>

                    <div
                        style="padding: 2Px 22Px; display: flex;flex-direction: row;justify-content: space-between;align-items: center;">
                      <span class="mapLeftTopText">
                        专线号 <span style=" color: red "
                                  v-if="item.lineState == 'alarm' || item.lineState == 'event' || item.lineType == 'event' || item.lineType == 'alarm'">
                          {{ item.zxNum
                        }}</span>
                        <span style=" color: #fff " v-else> {{ item.zxNum
                          }}</span>
                      </span>
                      <span class="mapLeftTopText"
                            v-show="item.busType == '5' || item.busType == '10' || item.busType == '拓扑'">
                        {{ titleZm }} {{ item.bandWidth }}
                      </span>
                      <span class="mapLeftTopText" v-show="item.busType == '线路'">
                        {{ titleZmLt }} {{ item.alarmCount }}
                      </span>
                    </div>
                  </div>
                  <div v-else>
                    <div class="alog">
                      <span class="mapLeftTop1Radis"></span>
                      <span class="mapLeftTop1Text" style="margin-left: 5px;">{{ item.circuitStart }}/{{ item.circuitEnd }}</span>
                             <!-- <span style="float: right;margin-right: 10Px;">{{ item.mesTime }}</span> -->
                    </div>
                    <div
                        style="padding: 2Px 22Px;width: 410px; display: flex;flex-direction: row;justify-content: space-between;align-items: center;">
                      <span class="mapLeftTopText" style="width: 270%;display: flex;" v-if="item.busType != '线路'">
                        专线号 <span style=" color: red;"
                                  v-if="item.status == 'alarm' || item.status == 'event'">
                          {{ item.circuitNo}}</span>
                        <span style="color: #fff" v-else> &nbsp;&nbsp;{{ item.circuitNo
                          }}</span>
                      </span>
                      <span class="mapLeftTopText" style=""
                            v-if="item.busType == '5' || item.busType == '10' || item.busType == '拓扑'">
                        <span  v-if="undefined != item.bandWidth && '' != item.bandWidth">带宽 {{ item.bandWidth }}</span>
                      </span>
                      <span class="mapLeftTopText" v-show="item.busType == '线路'">
                            <span style=" color: red;"
                              v-if="item.status == 'alarm' || item.status == 'event'">
                             {{ titleZmLt }} {{ item.alarmNum }}</span>
                        <span v-else>{{ titleZmLt }} {{ item.alarmNum }}</span>
                      </span>
                    </div>
                  </div>
                </span>
              </div>
            </div>
          </div>
        </div>
        <div class="mapRightTop1">
          <span class="mapLeftTopTitle"><span>当前告警数量</span></span>
          <div class="mapLeftTop2Div" style="margin-top:16Px;margin-left: 16Px;">
            <div class="mapRightTop1Img1"></div>
            <div class="mapLeftTop2Text" style="width: 100Px;">当前告警数量</div>
            <div class="mapLeftTop2Text2" style="margin-left: 180Px;" @click="alarmPopoverClick(alarmData.alarmNum)"><span style="font-weight: 500;color: #FF5656;">{{
                alarmData.alarmNum }}</span></div>
          </div>
        </div>
        <div class="mapRightTop2">
          <span class="mapLeftTopTitle"><span>应急物资</span></span>
          <span class="mapRightTopTitle" style="line-height: 44Px;    cursor:pointer;"  @click="reset('1')"></span>
          <div class="mapLeftTop2Div" style="margin-top:16Px;margin-left: 16Px;background: transparent">
            <div style="display: inline-block;">
              <span class="mapRightTop1Img2"></span>
              <span @click="sTDataClick('5')" style="margin-left: -8Px;margin-top: 20Px;font-size: 22Px;cursor:pointer;color: #55A4FE"
                    class="mapLeftTop2Text2Top">{{
                  yjData.cataNum
                }}<span style="font-size: 16px;">类</span>/{{yjData.num}}<span style="font-size: 16px;">件</span></span>
              <span style="margin-left: -9Px;margin-top: 57Px;font-size: 14px"
                    class="mapLeftTop2Text2Bottom">备品备件</span>
            </div>
            <div style="display: inline-block;">
              <span class="mapRightTop1Img3"></span>
              <span @click="sTDataClick('6')" style="margin-left: -32Px;margin-top: 16Px; cursor:pointer;color: #FABB61"
                    class="mapLeftTop2Text2Top">{{
                  yjData.emergElecNum }}<span style="font-size: 15px;">台</span></span>
              <span style="margin-left: -25Px;margin-top: 57Px;font-size: 14px"
                    class="mapLeftTop2Text2Bottom">应急发电机组</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div v-show="!showTopology" id="allmap"></div>
    <div v-show="showTopology">
      <div class="topologyBackground">
        <div class="topology">
          <topo />
        </div>
      </div>
    </div>
    <div class="noticePopover" v-if="noticePopoverShow">
      <div class="noticePopoverTitle" style="margin-top: 16Px;">{{ noticePopoverObj.mesTitle }}</div>
      <span class="noticePopoverX" style="margin-top: -2Px;" @click="noticePopoverQuit"></span>
      <div class="noticePopoverText4">{{ noticePopoverObj.mesName }}</div>
      <div class="noticePopoverText">&nbsp;&nbsp;&nbsp;&nbsp;{{noticePopoverObj.mesContext}}</div>
      <div class="noticePopoverText2" v-if="undefined != noticePopoverObj.mesDepaName && '' != noticePopoverObj.mesDepaName"><span style="width: 200Px;text-align: center;display: inline-block">{{ noticePopoverObj.mesDepaName }}</span></div>
      <div class="noticePopoverText3"><span style="width: 200Px;text-align: center;display: inline-block">{{ noticePopoverObj.mesDate }}</span></div>
    </div>
    <div class="alarmPopover" v-if="alarmPopoverShow">
      <div class="alarmPopoverTitle">告警详情</div>
      <span class="alarmPopoverX" @click="alarmPopoverQuit"></span>
      <div class="alarmPopoverText4">
        <el-table :data="tableData" stripe style="width: 100%;" default-expand-all max-height="380"
                  :cell-style="{color:'#ffffff',top:'5px'}" :row-class-name="tableRowClassName"
                  :header-cell-style="{ background: 'transparent',top:'5px' }">
          <el-table-column prop="circuitStart"  :width="80"  label="起点">
          </el-table-column>
          <el-table-column prop="circuitEnd" :width="80"  label="终点">
          </el-table-column>
          <el-table-column prop="circuitNo" label="电路代号">
          </el-table-column>
          <el-table-column prop="locateNeName"  label="告警位置">
          </el-table-column>
          <el-table-column prop="alarmTitle" label="告警名称">
          </el-table-column>
          <el-table-column prop="alarmCreateTime" label="告警发生时间">
          </el-table-column>
          <el-table-column prop="workOrderNumberJt" label="工单号">
          </el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>
<script>
import { formatTime } from "../assets/js/index.js";
import darkStyle from '../assets/js/darkStyle.json';
import urlCar from '../assets/zxx/car.gltf';
import bb from '../assets/zxx/yw1-dt.gif';
import $ from "jquery";
import url from "../assets/img_slices/照片.png";
import bottom from "../assets/zxx/bottom.png";
import Topo from "@/pages/topo";
import begin from '../assets/zxx/begin.png';
import ing from '../assets/zxx/ing.png';
import locationTab from "../assets/zxx/locationTab.png";
import bottom2 from "../assets/zxx/bottom2.png";
import urlP from "../assets/zxx/quit.png";
// import Topo from './topo.vue';

export default {
  name: "reinsuranceBigScreen",
  components: { Topo },
  data() {
    return {
      alarmNoZ:'',
      alarmSyncType:'0',
      curAlarmSyn: false,
      alarmSyncCount: '0',
      alarmSync:'0',
      alarmSync2:'0',
      busTypeZ:'其它',
      curAlarm : '10',
      tableData :[],
      noticePopoverShow:false,
      alarmPopoverShow:false,
      urlButton: ing,
      beginUrl: begin,
      ingUrl: ing,
      beginUrlShow: true,
      showTopology: false,
      urlCar: urlCar,
      sTDataInfo: { "fhqNum": 0, "jhjNum": 0 },
      showTail: true,
      darkStyle: darkStyle,
      dateDay: null,
      dateYear: null,
      dateDay2: null,
      dateYear2: null,
      dateWeek: null,
      weekday: ["周日", "周一", "周二", "周三", "周四", "周五", "周六"],
      eventData: [],
      zbGroup: [],
      alarmData: {},
      yjData: [],
      title: "",
      carlineLayer: null,
      rightTitle: "业务信息",
      lastArr: [],
      XlInfoData: [],
      titleZm: "带宽",
      titleZmLt: "告警",
      polylineArrFly: [],
      timeA: null,
      timeB: null,
      numberStyle: "",
      numberColor: "#fff",
      markShowSt: false,
      noticePopoverObj :null,
      alarmPopoverObj:null,
      cerrorColor: "",
      canvasObj :document.getElementsByTagName("canvas"),
      canvasCount :0
    };
  },
  created() {
    /*    this.initMap()*/
    /*    $('.right').css({ marginTop: -875 });
        $('.left').css({ marginTop: -875 });*/
  },
  watch:{
    canvasCount:{
      handler (newVal, oldVal) {
        let count = document.getElementsByTagName("canvas").length;
        if(count >10){
          this.map.clearOverlays();
          this.map = new BMapGL.Map('allmap'); // 创建Map实例
          this.map.centerAndZoom(new BMapGL.Point(108.953418, 34.274803), 13); // 初始化地图,设置中心点坐标和地图级别
          this.map.enableScrollWheelZoom(true); // 开启鼠标滚轮缩放
          /*     this.map.setHeading(84.5)*/
          this.map.setTilt(56.8);
          this.changeDarkStyle();
        }
      },
      immediate: true,
      deep: true
    }
  },
  mounted() {
    this.timeFn();
    this.initMap();
    this.getBusInfo(10);
    this.getEventData();
    this.getzbGroupData();
    this.getAlarmData(10);
    this.getYjData(10);
    if (this.alarmTime) {
      clearInterval(this.alarmTime);
      this.alarmTime = null;
    }
/*    this.refreshAlarm();*/
    this.queryData = {};
    this.queryData.busType = "10";
    this.getSTData(this.queryData);
    this.getYjData(this.queryData);
    this.jobIndex = 1;
    this.beginUrlShow = false;
    this.urlButton = this.beginUrl;
    if (this.timing3) {
      clearInterval(this.timing3);
      this.timing3 = null;
    }
    setTimeout(() =>{
      this.canvasCount= document.getElementsByTagName("canvas").length;
    },1000);
  },
  // 监听属性 类似于data概念
  computed: {

  },
  destroyed: function () {
    //清除定时器
    // this.timeA;
    clearInterval(this.timing1);
    this.timing1 = null;
    clearInterval(this.timeA);
    this.timeA = null;
    clearInterval(this.timeB);
    this.timeB = null;
    clearInterval(this.timing2);
    this.timing2 = null;
    clearInterval(this.timing3);
    this.timing3 = null;
    clearInterval(this.timeC);
    this.timeC = null;
    clearInterval(this.timeD);
    this.timeD = null;
    clearInterval(this.timeZxx1);
    this.timeZxx1 = null;
    clearInterval(this.timeZxx2);
    this.timeZxx2 = null;
    clearInterval(this.alarmTime);
    this.alarmTime = null;
  },
  methods: {
/*    refreshAlarm(){
      let _this = this;
      _this.alarmTime = setInterval(() => {
         _this.$axios.post("/protect-api/pointLine/getOccurAlarm", {}).then((data) => {
          if (data.data.code === "0000") {
              if (undefined !=data.data.data.one && data.data.data.one  == 'refresh') { //-10
                this.changeScene(1);
                return false;
              } else if (undefined !=data.data.data.two && data.data.data.two  == 'refresh') { //-5
                this.changeScene(4);
                return false;
              } else if (undefined !=data.data.data.three && data.data.data.three  == 'refresh') { //-线路
                this.changeScene(3);
                return false;
              }
            }
        });
      },10000);
    },*/
    tableRowClassName({row, rowIndex}) {
      if (rowIndex%2==0) {
        return 'warning-row';
      } else {
        return 'success-row';
      }
      return '';
    },
    async tableSearch() {
      let _this = this;
/*      let queryData = { "parentId": 2, "dataType": "2" };*/
          let queryData = {
            "busType": _this.curAlarm
          };
      await _this.$axios.post("/protect-api/zb/getGjList", queryData).then((data) => {
        if (data.data.code === "0000") {
          _this.tableData = data.data.data.alarmList;
        }
      });
    },
    alarmPopoverQuit(){
      let _this = this;
      _this.alarmPopoverShow = false;
      _this.alarmPopoverObj = null;
    },
    noticePopoverQuit(){
      let _this = this;
      _this.noticePopoverShow = false;
      _this.noticePopoverObj = null;
    },
    alarmPopoverClick(param){
      let _this = this;
      _this.alarmPopoverShow = true;
      _this.tableSearch();
    },
    noticePopoverClick(param){
      let _this = this;
      _this.noticePopoverShow = true;
      _this.noticePopoverObj = param;
      _this.noticePopoverObj.mesDate = _this.formatDateTime(new Date(_this.noticePopoverObj.mesTime));
    },
    addZero(num) {
      return num < 10 ? '0' + num : num;
    },
    formatDateTime(date) {
      const time = new Date(Date.parse(date));
      time.setTime(time.setHours(time.getHours()));
      const Y = time.getFullYear() + '年';
      const M = this.addZero(time.getMonth() + 1) + '月';
      const D = this.addZero(time.getDate()) + '日';
      return Y + M + D ;
    },
    sTDataClick(param) {
      let _this = this;
      _this.map.clearOverlays();
      _this.showTopology = false;
      if (null != _this.view && undefined != _this.view) {
        _this.view.removeAllLayers();
      }
      _this.beginUrlShow = false;
      _this.urlButton = _this.beginUrl;
      if (_this.timing3) {
        clearInterval(_this.timing3);
        _this.timing3 = null;
      }
      _this.indexA = 0;
      _this.markList.map((item, index) => {
        /*        debugger*/
        let point = new BMapGL.Point(item.posLng, item.posLat);
        let myIcon = new BMapGL.Icon(item.posUrl, new BMapGL.Size(23, 33));
        let marker4 = new BMapGL.Marker(point, { icon: myIcon, "aa": item });  // 创建标注
        _this.map.addOverlay(marker4);
        _this.initTabchuangSt(item, param,marker4,index);
      });
      _this.markShowSt = true;
      _this.map.setZoom(13);
    },
    reset(param,marker4) {
      let _this = this;
      _this.curAlarm = '10';
      _this.markShowSt = false;
      _this.map.clearOverlays();
      _this.showTopology = false;
      if (null != _this.view && undefined != _this.view) {
        _this.view.removeAllLayers();
      }
      this.jobIndex = 1;
      this.beginUrlShow = false;
      this.urlButton = this.beginUrl;
      if (_this.timing3) {
        clearInterval(_this.timing3);
        _this.timing3 = null;
      }
      /*      _this.jonButton();*/
      _this.addReguaranteeLogic();  //增加重保逻辑
      _this.addReguaranteeLogicFly();  //增加重保逻辑
      _this.map.setZoom(13);
      $("#buttonSpanId1").removeClass("buttonSpan");
      $("#buttonSpanId1").addClass("buttonSpanCur");
      $("#buttonSpanId2").removeClass("buttonSpanCur");
      $("#buttonSpanId2").addClass("buttonSpan");
      $("#buttonSpanId3").removeClass("buttonSpanCur");
      $("#buttonSpanId3").addClass("buttonSpan");
      $("#buttonSpanId4").removeClass("buttonSpanCur");
      $("#buttonSpanId4").addClass("buttonSpan");
      _this.canvasCount++;
    },
    initTabchuangSt(param, type,marker4,index) {
      let bottom2 = require("../assets/zxx/bottom2.png");
      let text1 = '<div class="stDataDiv1">';
      let text2 = '';
      let text4 = '';
      var label = "";
      if (type === "1") {
        if(param.fhqNum == 0){
          this.indexA++;
          return "";
        }else {
          text1 = '<div class="stDataDiv1">';
          text2 = '    <span class="stDataSpan1">防火墙：' + param.fhqNum + '</span>';
          text4 = '<div style="margin-top:13px;text-align: center;width: 128px;height: 40px;font-size: 16px;font-family: Source Han Sans CN;' +
              'font-weight: 500; color: #ffffff;"><span>' + param.posName + '</span></div>';
          let text3 = '</div><div style="position: relative;margin-top: 22px;"> <img src="' + bottom2 + ' "  alt="" style="position: absolute;left: 45%;"></div>';
          label = new BMapGL.Label(text1 + text2 + text4 + text3, { offset: new BMapGL.Size(-71, -108) });
          marker4.setLabel(label);
          document.getElementsByClassName("BMapLabel")[index-this.indexA].style.border = "1px #1ef1a5";
          document.getElementsByClassName("BMapLabel")[index-this.indexA].style.padding = "";
          document.getElementsByClassName("BMapLabel")[index-this.indexA].style.background = "transparent";
        }
      } else if (type === "2") {
        if(param.jhjNum == 0){
          this.indexA++;
          return "";
        }else {
          text1 = '<div class="stDataDiv2">';
          text2 = '    <span class="stDataSpan1">交换机：' + param.jhjNum + '</span>';
          text4 = '<div style="margin-top:13px;text-align: center;width: 128px;height: 40px;font-size: 16px;font-family: Source Han Sans CN;' +
              'font-weight: 500; color: #ffffff;"><span>' + param.posName + '</span></div>';
          let text3 = '</div><div style="position: relative;margin-top: 22px;"> <img src="' + bottom2 + ' "  alt="" style="position: absolute;left: 45%;"></div>';
          label = new BMapGL.Label(text1 + text2 + text4 + text3, { offset: new BMapGL.Size(-71, -108) });
          marker4.setLabel(label);
          document.getElementsByClassName("BMapLabel")[index-this.indexA].style.border = "1px #1ef1a5";
          document.getElementsByClassName("BMapLabel")[index-this.indexA].style.padding = "";
          document.getElementsByClassName("BMapLabel")[index-this.indexA].style.background = "transparent";
        }
      } else if (type === "3") {
        if(param.posAqNum == 0){
          this.indexA++;
          return "";
        }else {
          text1 = '<div class="stDataDiv3">';
          text2 = '    <span class="stDataSpan1">安全设备：' + param.posAqNum + '</span>';
          text4 = '<div style="margin-top:13px;text-align: center;width: 161px;height: 40px;font-size: 16px;font-family: Source Han Sans CN;' +
              'font-weight: 500; color: #ffffff;"><span>' + param.posName + '</span></div>';
          let text3 = '</div><div style="position: relative;margin-top:-2px;"> <img src="' + bottom2 + ' "  alt="" style="position: absolute;left: 45%;"></div>';
          label = new BMapGL.Label(text1 + text2 + text4 + text3, { offset: new BMapGL.Size(-85, -108) });
          marker4.setLabel(label);
          document.getElementsByClassName("BMapLabel")[index-this.indexA].style.border = "1px #1ef1a5";
          document.getElementsByClassName("BMapLabel")[index-this.indexA].style.padding = "";
          document.getElementsByClassName("BMapLabel")[index-this.indexA].style.background = "transparent";
        }
      } else if (type === "4") {
        if(param.zbNum == 0){
          this.indexA++;
          return "";
        }else {
          text1 = '<div class="stDataDiv4">';
          text2 = '    <span class="stDataSpan1">重保人数：' + param.zbNum + '</span>';
          text4 = '<div style="margin-top:13px;text-align: center;width: 148px;height: 40px;font-size: 16px;font-family: Source Han Sans CN;' +
              'font-weight: 500; color: #ffffff;"><span>' + param.posName + '</span></div>';
          let text3 = '</div><div style="position: relative;margin-top: 20px;"> <img src="' + bottom2 + ' "  alt="" style="position: absolute;left: 45%;"></div>';
          label = new BMapGL.Label(text1 + text2 + text4 + text3, { offset: new BMapGL.Size(-79, -108) });
          marker4.setLabel(label);
          document.getElementsByClassName("BMapLabel")[index-this.indexA].style.border = "1px #1ef1a5";
          document.getElementsByClassName("BMapLabel")[index-this.indexA].style.padding = "";
          document.getElementsByClassName("BMapLabel")[index-this.indexA].style.background = "transparent";
        }
      } else if (type === "5") {
        if(param.upsNum == 0){
          this.indexA++;
          return "";
        }else {
          text1 = '<div class="stDataDiv5" style="width: 220Px">';
          text2 = '    <span class="stDataSpan1" style="font-size: 17px;width: 220Px">备品备件数量：' + param.num + '类/' + param.cataNum + '件</span>';
          text4 = '<div style="margin-top:13px;text-align: center;width: 128px;height: 40px;font-size: 16px;font-family: Source Han Sans CN;' +
              'font-weight: 500; color: #ffffff;"><span>' + param.posName + '</span></div>';
          let text3 = '</div><div style="position: relative;margin-top: 22px;"> <img src="' + bottom2 + ' "  alt="" style="position: absolute;left: 45%;"></div>';
          label = new BMapGL.Label(text1 + text2 + text4 + text3, { offset: new BMapGL.Size(-111, -108) });
          marker4.setLabel(label);
          document.getElementsByClassName("BMapLabel")[index-this.indexA].style.border = "1px #1ef1a5";
          document.getElementsByClassName("BMapLabel")[index-this.indexA].style.padding = "";
          document.getElementsByClassName("BMapLabel")[index-this.indexA].style.background = "transparent";
        }
      } else if (type === "6") {
        if(param.yjclNum == 0){
          this.indexA++;
          return "";
        }else {
          text1 = '<div class="stDataDiv6" style="width: 175px;">';
          text2 = '    <span class="stDataSpan1" style="width: 175px;">应急发电机组：' + param.yjclNum + '</span>';
          text4 = '<div style="margin-top:13px;text-align: center;width: 175px;height: 40px;font-size: 16px;font-family: Source Han Sans CN;' +
              'font-weight: 500; color: #ffffff;"><span>' + param.posName + '</span></div>';
          let text3 = '</div><div style="position: relative;margin-top:-2px;"> <img src="' + bottom2 + ' "  alt="" style="position: absolute;left: 45%;"></div>';
          label = new BMapGL.Label(text1 + text2 + text4 + text3, { offset: new BMapGL.Size(-90, -108) });
          marker4.setLabel(label);
          document.getElementsByClassName("BMapLabel")[index-this.indexA].style.border = "1px #1ef1a5";
          document.getElementsByClassName("BMapLabel")[index-this.indexA].style.padding = "";
          document.getElementsByClassName("BMapLabel")[index-this.indexA].style.background = "transparent";
        }
      }
    },
    abc() {
      this.addReguaranteeLogicFHQ();
    },
    changeJob() {
      if (this.timing3) {
        clearInterval(this.timing3);
        this.timing3 = null;
      }
      if (!this.beginUrlShow) {
        this.beginUrlShow = true;
        this.urlButton = this.ingUrl;
        this.jobIndex = 1;
        this.jonButton();
      } else {
        this.beginUrlShow = false;
        this.urlButton = this.beginUrl;
      }
    },
    jonButton() {
      this.arrJob = ['1', '4', '3', '2'];
      this.timing3 = setInterval(() => {
        let _this = this;
        let param = this.arrJob[_this.jobIndex];
        _this.map.clearOverlays();
        _this.showTopology = false;
        if (null != _this.view && undefined != _this.view) {
          _this.view.removeAllLayers();
        }
        if (param == '1') {
          $("#buttonSpanId1").removeClass("buttonSpan");
          $("#buttonSpanId1").addClass("buttonSpanCur");
          $("#buttonSpanId2").removeClass("buttonSpanCur");
          $("#buttonSpanId2").addClass("buttonSpan");
          $("#buttonSpanId3").removeClass("buttonSpanCur");
          $("#buttonSpanId3").addClass("buttonSpan");
          $("#buttonSpanId4").removeClass("buttonSpanCur");
          $("#buttonSpanId4").addClass("buttonSpan");
          this.rightTitle = "业务信息";
          clearInterval(_this.timeA);
          _this.timeA = null;
          clearInterval(_this.timeB);
          _this.timeB = null;
          clearInterval(_this.timeC);
          _this.timeC = null;
          clearInterval(_this.timeD);
          _this.timeD = null;
          this.getBusInfo(10);
          this.queryData.busType = 10;
          this.getYjData(this.queryData);
          this.getAlarmData(10);
          _this.curAlarm = '10';
          /*          $('.right').css({ marginTop: -875 });
                    $('.left').css({ marginTop: -875 });*/

          this.map.clearOverlays();
          _this.addReguaranteeLogicFly();
          _this.addReguaranteeLogic();  //增加重保逻辑
        } else if (param == '2') {
          $("#buttonSpanId1").removeClass("buttonSpanCur");
          $("#buttonSpanId1").addClass("buttonSpan");
          $("#buttonSpanId2").removeClass("buttonSpanCur");
          $("#buttonSpanId2").addClass("buttonSpan");
          $("#buttonSpanId3").removeClass("buttonSpan");
          $("#buttonSpanId3").addClass("buttonSpanCur");
          $("#buttonSpanId4").removeClass("buttonSpanCur");
          $("#buttonSpanId4").addClass("buttonSpan");
          this.rightTitle = "业务信息";
          this.getBusInfo("拓扑");

          clearInterval(_this.timeA);
          _this.timeA = null;
          clearInterval(_this.timeB);
          _this.timeB = null;
          clearInterval(_this.timeC);
          _this.timeC = null;
          clearInterval(_this.timeD);
          _this.timeD = null;
          this.queryData.busType = "拓扑";
          this.getYjData(this.queryData);
          this.getAlarmData('拓扑');
          _this.curAlarm = '拓扑';
          this.map.clearOverlays();
          /*          $('.right').css({ marginTop: 200 });
                    $('.left').css({ marginTop: 200 });*/
          _this.addReguaranteeTopology();  //增加重保拓扑
        } else if (param == '3') {
          $("#buttonSpanId1").removeClass("buttonSpanCur");
          $("#buttonSpanId1").addClass("buttonSpan");
          $("#buttonSpanId2").removeClass("buttonSpan");
          $("#buttonSpanId2").addClass("buttonSpanCur");
          $("#buttonSpanId3").removeClass("buttonSpanCur");
          $("#buttonSpanId3").addClass("buttonSpan");
          $("#buttonSpanId4").removeClass("buttonSpanCur");
          $("#buttonSpanId4").addClass("buttonSpan");
          this.rightTitle = "光缆信息";
          clearInterval(_this.timeA);
          _this.timeA = null;
          clearInterval(_this.timeB);
          _this.timeB = null;
          clearInterval(_this.timeC);
          _this.timeC = null;
          clearInterval(_this.timeD);
          _this.timeD = null;
          this.getXlInfo();
          this.queryData.busType = "线路";
          this.getYjData(this.queryData);
          this.getAlarmData('线路');
          _this.curAlarm = '线路';
          /*         $('.right').css({ marginTop: -875 });
                   $('.left').css({ marginTop: -875 });*/
          this.map.clearOverlays();

          _this.addLineMonitoring();  //增加线路监控
        } else if (param == '4') {
          $("#buttonSpanId1").removeClass("buttonSpanCur");
          $("#buttonSpanId1").addClass("buttonSpan");
          $("#buttonSpanId2").removeClass("buttonSpanCur");
          $("#buttonSpanId2").addClass("buttonSpan");
          $("#buttonSpanId3").removeClass("buttonSpanCur");
          $("#buttonSpanId3").addClass("buttonSpan");
          $("#buttonSpanId4").removeClass("buttonSpan");
          $("#buttonSpanId4").addClass("buttonSpanCur");
          this.rightTitle = "业务信息";
          this.getBusInfo(5);

          clearInterval(_this.timeA);
          _this.timeA = null;
          clearInterval(_this.timeB);
          _this.timeB = null;
          clearInterval(_this.timeC);
          _this.timeC = null;
          clearInterval(_this.timeD);
          _this.timeD = null;
          this.queryData.busType = 5;
          this.getYjData(this.queryData);
          _this.curAlarm = '5';
          this.getAlarmData(5);
          /*          $('.right').css({ marginTop: -875 });
                    $('.left').css({ marginTop: -875 });*/
          this.map.clearOverlays();

          _this.getAllPointLineBus4();  //增加-5
        };
        if (_this.jobIndex == 3) {
          _this.jobIndex = 0;
        } else {
          _this.jobIndex++;
        }
        _this.canvasCount++;
      }, 30000);
    },
    closeShowTail() {
      let _this = this;
      if (_this.showTail) {
        _this.showTail = false;
      } else {
        this.showTail = true;
      }
    },
    // 获取光缆信息
    getXlInfo() {
      let _this = this;
      let queryData = {
/*        "posId": "1",*/
        "busType": "线路"
      };
      this.XlInfoData = [];
      clearInterval(_this.timeA);
      _this.timeA = null;
      clearInterval(_this.timeB);
      _this.timeB = null;
      clearInterval(_this.timeC);
      _this.timeC = null;
      clearInterval(_this.timeD);
      _this.timeD = null;
      let url = '/protect-api/zb/getBusInfoCjAndXl';
      _this.busTypeZ="其它";
      _this.$axios.post(url, queryData).then((data) => {
        if (data.data.code === '0000') {
          // console.log(data);
          this.XlInfoData = data.data.data;
          var itemList = document.querySelector('.item-list');
          var scrollDis =  7.78;
          var length = this.XlInfoData.length;
          var index = 1;
          const _this = this;
          _this.timeD = setInterval(() => {
            if (index < length + 1) {
              itemList.style.top = -(scrollDis * index) + 'rem';
              itemList.style.transitionDuration = '1s';
              if (index === length) {
                clearInterval(this.timeC);
                this.timeC = null;
                _this.timeC = setTimeout(() => {
                  itemList.style.top = 0;
                  itemList.style.transitionDuration = '0s';
                }, 10);
                index = 0;
              }
            }
            ++index;
          }, 3000);
        }
      });
    },
    // 获取线路信息
    getBusInfo(data) {
      let _this = this;
      let queryData = {
        "busType": data
      };
      this.XlInfoData = [];
      clearInterval(_this.timeA);
      _this.timeA = null;
      clearInterval(_this.timeB);
      _this.timeB = null;
      clearInterval(_this.timeC);
      _this.timeC = null;
      clearInterval(_this.timeD);
      _this.timeD = null;
      let url = '/protect-api/zb/getBusInfoCjAndXl';
      if(data=='拓扑'){
        url = '/protect-api/zb/getBusInfo';
        _this.busTypeZ="拓扑";
      }else {
        _this.busTypeZ="其它";
      }
      _this.$axios.post(url, queryData).then((data) => {
        if (data.data.code === '0000') {
          // console.log(data);
          this.XlInfoData = data.data.data;
          var itemList = document.querySelector('.item-list');
          var scrollDis = 7.78;
          var length = this.XlInfoData.length;
          var index = 1;
          const _this = this;
          _this.timeA = setInterval(() => {
            if (index < length + 1) {
              itemList.style.top = -(scrollDis * index) + 'rem';
              itemList.style.transitionDuration = '1s';
              if (index === length) {
                clearInterval(this.timeB);
                this.timeB = null;
                _this.timeB = setTimeout(() => {
                  itemList.style.top = 0;
                  itemList.style.transitionDuration = '0s';
                }, 10);
                index = 0;
              }
            }
            ++index;
          }, 3000);
        }
      });
    },
    getEventData() {
      let _this = this;
      let queryData = {
        "posId": "1",
        "busType": "1"
      };
      _this.$axios.post("/protect-api/zb/getMessage", {}).then((data) => {
        if (data.data.code === '0000') {
          // console.log(data);
          this.eventData = data.data.data;
          let itemList = document.querySelector('.item-list2');
          let scrollDis =  7.75;
          let length = this.eventData.length;
          let index = 1;
          const _this = this;


          _this.timeZxx1 = setInterval(() => {
            if (index < length + 1) {
              itemList.style.top = -(scrollDis * index) + 'rem';
              itemList.style.transitionDuration = '1s';
              if (index === length) {
                clearInterval(this.timeZxx2);
                this.timeZxx2 = null;
                _this.timeZxx2 = setTimeout(() => {
                  itemList.style.top = 0;
                  itemList.style.transitionDuration = '0s';
                }, 10);
                index = 0;
              }
            }
            ++index;
          }, 3000);
        }
      });
    },
    getzbGroupData() {
      let _this = this;
      let queryData = {
        "posId": "1",
        "busType": "10"
      };
      _this.$axios.post("/protect-api/zb/getZbGroup", {}).then((data) => {
        if (data.data.code === '0000') {
          // console.log(data);
          this.zbGroup = data.data.data;
        }
      });
    },
    getAlarmData(params) {
      let _this = this;
      let queryData = {
        "busType": params
      };
      _this.$axios.post("/protect-api/zb/getGjNum", queryData).then((data) => {
        if (data.data.code === '0000') {
          // console.log(data);
          this.alarmData = data.data.data;
        }
      });
    },
    getYjData(params) {
      let _this = this;
/*      let queryData = {
        "busType": params
      };*/
      _this.$axios.post("/protect-api/zb/getYjwz", params).then((data) => {
        if (data.data.code === '0000') {
          // console.log(data);
          this.yjData = data.data.data;
        }
      });
    },
    getSTData(queryData) {
      let _this = this;
      _this.$axios.post("/protect-api/zb/getSTData", queryData).then((data) => {
        if (data.data.code === '0000') {
          _this.sTDataInfo = data.data.data;
        }
      });
    },
    reinsuranceTeam() {
      let p= {};
      p.parentId = 2030;
      this.$router.push({ path: "/home", query: {p} });
    },
    changeScene(param) {
      let _this = this;
      _this.map.clearOverlays();
      _this.showTopology = false;
      if (null != _this.view && undefined != _this.view) {
        _this.view.removeAllLayers();
      }
      _this.map.removeEventListener("zoomend", function () { });
      if (param == '1') {
        $("#buttonSpanId1").removeClass("buttonSpan");
        $("#buttonSpanId1").addClass("buttonSpanCur");
        $("#buttonSpanId2").removeClass("buttonSpanCur");
        $("#buttonSpanId2").addClass("buttonSpan");
        $("#buttonSpanId3").removeClass("buttonSpanCur");
        $("#buttonSpanId3").addClass("buttonSpan");
        $("#buttonSpanId4").removeClass("buttonSpanCur");
        $("#buttonSpanId4").addClass("buttonSpan");
        this.rightTitle = "业务信息";
        _this.getBusInfo(10);

        clearInterval(this.timeA);
        this.timeA = null;
        clearInterval(this.timeB);
        this.timeB = null;
        clearInterval(_this.timeC);
        _this.timeC = null;
        clearInterval(_this.timeD);
        _this.timeD = null;
        /*      $('.right').css({ marginTop: -875 });
              $('.left').css({ marginTop: -875 });*/
        // this.map.clearOverlays();
        _this.addReguaranteeLogicFly();

        _this.addReguaranteeLogic();  //增加重保逻辑
        _this.curAlarm = '10';
        _this.getAlarmData(10);
        _this.queryData.busType = "10";
        _this.getSTData(_this.queryData);
        _this.getYjData(_this.queryData);
        _this.getzbGroupData();
      } else if (param == '2') {
        $("#buttonSpanId1").removeClass("buttonSpanCur");
        $("#buttonSpanId1").addClass("buttonSpan");
        $("#buttonSpanId2").removeClass("buttonSpanCur");
        $("#buttonSpanId2").addClass("buttonSpan");
        $("#buttonSpanId3").removeClass("buttonSpan");
        $("#buttonSpanId3").addClass("buttonSpanCur");
        $("#buttonSpanId4").removeClass("buttonSpanCur");
        $("#buttonSpanId4").addClass("buttonSpan");
        _this.rightTitle = "业务信息";
        _this.getBusInfo("拓扑");

        clearInterval(this.timeA);
        _this.timeA = null;
        clearInterval(this.timeB);
        _this.timeB = null;
        clearInterval(_this.timeC);
        _this.timeC = null;
        clearInterval(_this.timeD);
        _this.timeD = null;
        _this.curAlarm = '拓扑';
        _this.getAlarmData('拓扑');
        _this.queryData.busType = "拓扑";
        _this.getSTData(_this.queryData);
        _this.getYjData(_this.queryData);
        _this.getzbGroupData();
        // this.map.clearOverlays();
        /*        $('.right').css({ marginTop: 200 });
                $('.left').css({ marginTop: 200 });*/
        _this.addReguaranteeTopology();  //增加重保拓扑
      } else if (param == '3') {
        $("#buttonSpanId1").removeClass("buttonSpanCur");
        $("#buttonSpanId1").addClass("buttonSpan");
        $("#buttonSpanId2").removeClass("buttonSpan");
        $("#buttonSpanId2").addClass("buttonSpanCur");
        $("#buttonSpanId3").removeClass("buttonSpanCur");
        $("#buttonSpanId3").addClass("buttonSpan");
        $("#buttonSpanId4").removeClass("buttonSpanCur");
        $("#buttonSpanId4").addClass("buttonSpan");
        this.rightTitle = "光缆信息";
        this.getXlInfo();

        clearInterval(this.timeA);
        this.timeA = null;
        clearInterval(this.timeB);
        this.timeB = null;
        clearInterval(_this.timeC);
        _this.timeC = null;
        clearInterval(_this.timeD);
        _this.timeD = null;
        this.getYjData('线路');
        _this.curAlarm = '线路';
        this.getAlarmData('线路');


        /*        $('.right').css({ marginTop: -875 });
                $('.left').css({ marginTop: -875 });*/
        this.map.clearOverlays();

        _this.addLineMonitoring();  //增加线路监控
        // _this.getBusInfo("线路");
        _this.getAlarmData("线路");
        _this.queryData.busType = "线路";
        _this.getSTData(_this.queryData);
        _this.getYjData(_this.queryData);
        _this.getzbGroupData();
      } else if (param == '4') {
        $("#buttonSpanId1").removeClass("buttonSpanCur");
        $("#buttonSpanId1").addClass("buttonSpan");
        $("#buttonSpanId2").removeClass("buttonSpanCur");
        $("#buttonSpanId2").addClass("buttonSpan");
        $("#buttonSpanId3").removeClass("buttonSpanCur");
        $("#buttonSpanId3").addClass("buttonSpan");
        $("#buttonSpanId4").removeClass("buttonSpan");
        $("#buttonSpanId4").addClass("buttonSpanCur");
        this.rightTitle = "业务信息";
        this.getBusInfo(5);

        clearInterval(this.timeA);
        this.timeA = null;
        clearInterval(this.timeB);
        this.timeB = null;
        clearInterval(_this.timeC);
        _this.timeC = null;
        clearInterval(_this.timeD);
        _this.timeD = null;
        _this.curAlarm = '5';
        this.getAlarmData(5);


        /*        $('.right').css({ marginTop: -875 });
                $('.left').css({ marginTop: -875 });*/
        this.map.clearOverlays();
        _this.getAllPointLineBus4();  //增加-5
        _this.getYjData(5);
        _this.getAlarmData(5);
        _this.queryData.busType = "5";
        _this.getSTData(_this.queryData);
        _this.getYjData(_this.queryData);
        _this.getzbGroupData();
      }
      _this.canvasCount++;
    },
    timeFn() {
      this.timing1 = setInterval(() => {
        this.dateDay = formatTime(new Date(), "HH: mm: ss");
        this.dateYear = formatTime(new Date(), "yyyy年MM月dd日");
        this.dateWeek = this.weekday[new Date().getDay()];
      }, 1000);
      this.timing2 = setInterval(() => {
        this.dateDay2 = formatTime(new Date(), "HH: mm: ss");
        this.dateYear2 = formatTime(new Date(), "yyyy/MM/dd日");
      }, 1000);
    },
    initMap() {
      // GL版命名空间为BMapGL

      this.map = new BMapGL.Map('allmap'); // 创建Map实例
      this.map.clearOverlays();
      this.map.centerAndZoom(new BMapGL.Point(108.953418, 34.274803), 13); // 初始化地图,设置中心点坐标和地图级别
      this.map.enableScrollWheelZoom(true); // 开启鼠标滚轮缩放
      /*     this.map.setHeading(84.5)*/
      this.map.setTilt(56.8);
      this.changeDarkStyle();
      this.addReguaranteeLogic();  //增加重保逻辑
      this.addReguaranteeLogicFly();  //增加重保逻辑

      /*      this.addReguaranteeTopology();  //增加重保拓扑
            this.addLineMonitoring();  //增加线路监控*/
    },
    addLineMonitoring() {
      let _this = this;
      let queryData = { "aa": '21' };
      this.$axios.post("/protect-api/pointLine/getAllPointLineBus3", queryData).then((data) => {
        if (data.data.code === "0000") {
          _this.reguaranteeTopologyList = data.data.data.zbLineStrList;
          _this.reguaranteeTopologyList2 = data.data.data.zbPos;
          _this.reguaranteeTopologyList2.map((itemNext2,index) => {
            let point = new BMapGL.Point(itemNext2.posLng, itemNext2.posLat);
            let myIcon = new BMapGL.Icon(itemNext2.posUrl, new BMapGL.Size(23, 33));
            let marker2 = new BMapGL.Marker(point, { icon: myIcon });  // 创建标注
            _this.map.addOverlay(marker2);
            let lableNn = _this.initTabNn(itemNext2);
            marker2.setLabel(lableNn);
            document.getElementsByClassName("BMapLabel")[index].style.border = "1px #1ef1a5";
            document.getElementsByClassName("BMapLabel")[index].style.padding = "";
            document.getElementsByClassName("BMapLabel")[index].style.background = "transparent";
          });
          _this.nn(_this.reguaranteeTopologyList);
        }
      });
    },
    initTabNn(param) {
      let text = '<div style="width: 100px;height: 18px;font-size: 12px;text-align:center;font-family: Source Han Sans CN;font-weight: 400;' +
          'text-shadow: 0Px 2Px 8Px rgb(14 197 236 / 36%);color: #ffffff;line-height: 22px;">' + param.posName + '</div>';
      var label = new BMapGL.Label(text, { offset: new BMapGL.Size(-51, -40) });
      return label;
    },
    nn(reguaranteeTopologyList) {
      let _this = this;
      let data = [];
      _this.view = new mapvgl.View({
        map: _this.map
      });
      reguaranteeTopologyList.map((item, index) => {
        let lineArrList = item.lineArr;
        data.push({ "geometry": { "type": "LineString", "coordinates": lineArrList }, "color": item.lineColor + "2e", "width": JSON.stringify(item.lineBold.slice(0, item.lineBold.length - 2) * 1.1) });
      });
      let data2 = [];
      let data3 = [];
      reguaranteeTopologyList.map((item, index) => {
        let lineArrList = item.lineArr;
        if (item.lineState != "alarm") {
          data2.push({ "geometry": { "type": "LineString", "coordinates": lineArrList }, "color": item.lineColor, "width": '3' });
        } else {
          //告警点打X
          let ii = Math.round(lineArrList.length / 2);
          let middenP = new BMapGL.Point(lineArrList[ii][0], lineArrList[ii][1],);
          let urlP = require("../assets/zxx/quit.png");
          let myIcon = new BMapGL.Icon(urlP, new BMapGL.Size(16, 16));
          let marker3 = new BMapGL.Marker(middenP, { icon: myIcon, "aa": item });  // 创建标注
          _this.map.addOverlay(marker3);
          data3.push({ "geometry": { "type": "Point", "coordinates": lineArrList[ii] }, "properties": { "height": 10 } });
        };
      });
      let rippleLayer = new mapvgl.RippleLayer({
        size: 3500,
        unit: 'm',
        renderOrder: 2,//设置图层的层级
        color: 'rgb(218,61,40)',
        enablePicked: false,
        onClick: (e) => { // 点击事件
          /*    console.log(e);*/
        },
      });
      _this.view.addLayer(rippleLayer);
      rippleLayer.setData(data3);
      let lineLayer = new mapvgl.LineLayer({
        width: 5,
        style: 'road',
        enablePicked: true,
        onClick: e => {
          /*          console.log(e);*/
        }
      });
      _this.view.addLayer(lineLayer);
      lineLayer.setData(data);

      var lineLayer2 = new mapvgl.LineLayer({
        animation: true,
        duration: 2.5, // 循环时间2s
        trailLength: 0.6, // 拖尾长度占间隔的0.4
        interval: 0.2 // 粒子长度占线整体长度的0.2
      });
      _this.view.addLayer(lineLayer2);
      lineLayer2.setData(data2);
      /*       _this.carlineLayer = new mapvgl.CarLineLayer({
              url:_this.urlCar,
              autoPlay: true,
              scale: 100,
            });

            _this.view.addLayer( _this.carlineLayer);
            _this.carlineLayer.setData(data);*/

      /*      _this.map.setDefaultCursor('default');
            var layer2 = new mapvgl.WallTripLayer({
              trailLength: 10.0,
              step: 0.02,
              /!*        color: 'rgba(255, 0, 102, 0.5)',*!/
              height: 80
            });

            _this.view.addLayer(layer2);

            layer2.setData(data2);*/

      /*      var coordinates = data[0].geometry.coordinates;
            var point = [];
            for (var i = 0; i < coordinates.length; i += 5) {
              point.push(new BMapGL.Point(coordinates[i][0], coordinates[i][1]));
            }

            var pl = new BMapGL.Polyline(point, {strokeWeight: 1, strokeColor:'#f00000', strokeOpacity: 0});
            var trackAni = new BMapGLLib.TrackAnimation(_this.map, pl, {
              overallView: false,
              tilt: 70,
              heading: 100,
              zoom: 13,
              duration: 20000,
              delay: 100
            });
            trackAni.start();*/
    },
    addReguaranteeTopology() {
      let _this = this;
      _this.showTopology = true;
    },
    addReguaranteeLogic() {
      let _this = this;
      let queryData = { "busType": '1' };
      _this.$axios.post("/protect-api/pointLine/getAllPointLineByBus", queryData).then((data) => {
        if (data.data.code === "0000") {
          _this.reguaranteeLogicList = data.data.data.zbPosList;
          _this.reguaranteeLogicList2 = data.data.data.zbLines;
          _this.markList = _this.reguaranteeLogicList;
          var data = [];
          let aaaaa = {};
          var randomCount = 2; // 模拟的飞线的数量
          var curve = new mapvgl.BezierCurve();
          _this.reguaranteeLogicList.map((item, index) => {
            let polylineArr1 = [];
            let polylineArr2 = [];
            let polylineArr3 = [];
            polylineArr1.push(new BMapGL.Point(_this.reguaranteeLogicList[0].posLng, _this.reguaranteeLogicList[0].posLat));
            let point = new BMapGL.Point(item.posLng, item.posLat);
            let myIcon = new BMapGL.Icon(item.posUrl, new BMapGL.Size(23, 33));
            let marker2 = new BMapGL.Marker(point, { icon: myIcon, "aa": item });  // 创建标注
            if ('1' != item.posType) {
              polylineArr1.push(new BMapGL.Point(item.posLng, item.posLat));
              marker2.customData = item;
              let color = ["#dfde3a", "#dfde3a", "#dfde3a"];
              let border = "2px";
              if (null != item.posId) {
                let colorArr = _this.reguaranteeLogicList2.filter(cc => cc.posIdTarget == item.posId);
                if (undefined != colorArr[0]) {
                  /*       color = colorArr[0].lineColor;*/
                  border = colorArr[0].lineBold;
                }
                if (undefined != colorArr && colorArr.length > 0) {
                  color = colorArr[0].lineColor.split(",");
                }
                //告警点打!
                if (undefined != colorArr[0] && colorArr[0].lineType === "event") {  //event
                  let ppp = colorArr[0].lineStr.split(";");
                  let ll = ppp[0].split(",");
                  let ll2 = ppp[1].split(",");
                  let pointArr = [new BMapGL.Point(Number(ll[0]), Number(ll[1])), new BMapGL.Point(Number(ll2[0]), Number(ll2[1]))];
                  let middenP = _this.getMidpoiont(pointArr);
                  let urlP = require("../assets/zxx/warning.png");
                  let myIcon = new BMapGL.Icon(urlP, new BMapGL.Size(25, 25));
                  let marker3 = new BMapGL.Marker(middenP, { icon: myIcon, "aa": item });  // 创建标注
                  _this.map.addOverlay(marker3);
                }
                //告警点打X
                if (undefined != colorArr[0] && colorArr[0].lineType === "alarm") {
                  let ppp = colorArr[0].lineStr.split(";");
                  let ll = ppp[0].split(",");
                  let ll2 = ppp[1].split(",");
                  let pointArr = [new BMapGL.Point(Number(ll[0]), Number(ll[1])), new BMapGL.Point(Number(ll2[0]), Number(ll2[1]))];
                  let middenP = _this.getMidpoiont(pointArr);
                  let urlP = require("../assets/zxx/quit.png");
                  let myIcon = new BMapGL.Icon(urlP, new BMapGL.Size(16, 16));
                  let marker3 = new BMapGL.Marker(middenP, { icon: myIcon, "aa": item });  // 创建标注
                  _this.map.addOverlay(marker3);
                }
              }
              let polyline1 = new BMapGL.Polyline(polylineArr1, { strokeColor: color[0], strokeWeight: border[0], strokeOpacity: 0.9 });   //创建折线
              if (item.posName === "联通省公司" || item.posName === "联通西安分公司") {
              }else {
                _this.map.addOverlay(polyline1);
              }
              /*              let polyline2 = new BMapGL.Polyline(polylineArr2, { strokeColor: color[1], strokeWeight: border[0], strokeOpacity: 0.9 });   //创建折线
                            _this.map.addOverlay(polyline2);
                            let polyline3 = new BMapGL.Polyline(polylineArr3, { strokeColor: color[2], strokeWeight: border[0], strokeOpacity: 0.9 });   //创建折线
                            _this.map.addOverlay(polyline3);*/
              _this.map.addOverlay(marker2);
              marker2.customData = item;
            }
            if (index === 0) {
              _this.map.addOverlay(marker2);
              marker2.setAnimation(BMAP_ANIMATION_BOUNCE);
            }
            _this.marker2 = marker2;
            marker2.customData = item;
            let lableText = _this.initTabchuang(item);
            marker2.setLabel(lableText);
            _this.clickInfomation = true;
            marker2.addEventListener("mouseover", async function (param) {
              let list2Length1 = _this.reguaranteeLogicList.length;
              if (_this.clickInfomation && undefined == document.getElementsByClassName("BMapLabel")[list2Length1]) {
                let lableText2 = await _this.tabchuang(item);
                marker2.setLabel(lableText2);
                document.getElementsByClassName("BMapLabel")[list2Length1].style.border = "1px #1ef1a5";
                document.getElementsByClassName("BMapLabel")[list2Length1].style.padding = "";
                _this.queryData = { "busType": '10', "posId": param.target.customData.posId };
                _this.getSTData(_this.queryData);
                _this.getYjData(_this.queryData);
                $("#openZhongbaoTeam").val(param.target.customData);
                document.getElementById("openZhongbaoTeam").onclick = function (e) {
                  let jj = e.target.value;
                  let p = {};
                  if(e.target.value.posName==="联通省公司"){
                    p.parentId = "2";
                  } else if (param.posName === "联通西安分公司") {
                    p.parentId = "3";
                  }else {
                    p.parentId = "4";
                  }
                  if (undefined != jj && null != jj) {
                    _this.$router.push({ path: "/home", query: { p } });
                  }
                };
                document.getElementById("informationExplains").onmouseover = function (e) {
                  $("#informationExplainsDetails").show();
                };
                document.getElementById("informationExplains").onmouseout = function (e) {
                  $("#informationExplainsDetails").hide();
                };
              }
            });
            marker2.addEventListener("mouseout", function () {
                  if (_this.clickInfomation) {
                    _this.queryData.busType = "10";
                    _this.queryData.posId ="";
                    _this.getSTData(_this.queryData);
                    _this.getYjData(_this.queryData);
                    let list2Length2 = _this.reguaranteeLogicList.length;
                    if (undefined != document.getElementsByClassName("BMapLabel")[list2Length2]) {
                      document.getElementsByClassName("BMapLabel")[list2Length2].remove();
                    }
                  }
                }
            );
            marker2.addEventListener("click", function () {
              if (_this.clickInfomation) {
                marker2.removeEventListener("mouseout", function () { });
                _this.clickInfomation = false;
              } else {
                marker2.addEventListener("mouseout", function () {
                  _this.queryData.busType = "10";
                  _this.queryData.posId ="";
                  _this.getSTData(_this.queryData);
                  _this.getYjData(_this.queryData);
                  let list2Length2 = _this.reguaranteeLogicList.length;
                  if (undefined != document.getElementsByClassName("BMapLabel")[list2Length2]) {
                    document.getElementsByClassName("BMapLabel")[list2Length2].remove();
                  }
                });
                _this.clickInfomation = true;
              }
            });
          });
          setTimeout(() => {
            for (let i = 0; i < _this.reguaranteeLogicList.length; i++) {
              if (undefined != document.getElementsByClassName("BMapLabel")[i]) {
                document.getElementsByClassName("BMapLabel")[i].style.border = "1px dashed #1ef1a5";
                document.getElementsByClassName("BMapLabel")[i].style.padding = "";
              }
            }
            $(".BMapLabel").hide();
          }, 250);
          _this.map.addEventListener("zoomend", function (e) {
            if (_this.markShowSt) { return false; };
            let zoom = _this.map.getZoom(); // 获取缩放级别
            if (zoom < 14) {
              for (let i = 0; i < _this.reguaranteeLogicList.length; i++) {
                if (undefined != document.getElementsByClassName("BMapLabel")[i]) {
                  document.getElementsByClassName("BMapLabel")[i].style.display = "none";
                }
              }
            } else {
              for (let i = 0; i < _this.reguaranteeLogicList.length; i++) {
                if (undefined != document.getElementsByClassName("BMapLabel")[i]) {
                  document.getElementsByClassName("BMapLabel")[i].style.display = "";
                }
              }
            }
          });
        }
      });
    },
    //获取两点之间中间点
    getMidpoiont(pointArr) {
      const lngca =
          (Math.max(parseFloat(pointArr[0].lng), parseFloat(pointArr[1].lng)) -
              Math.min(parseFloat(pointArr[0].lng), parseFloat(pointArr[1].lng))) /
          2;
      const latca =
          (Math.max(parseFloat(pointArr[0].lat), parseFloat(pointArr[1].lat)) -
              Math.min(parseFloat(pointArr[0].lat), parseFloat(pointArr[1].lat))) /
          2;
      const lngcenter = Math.min(parseFloat(pointArr[0].lng), parseFloat(pointArr[1].lng)) + lngca;
      const latcenter = Math.min(parseFloat(pointArr[0].lat), parseFloat(pointArr[1].lat)) + latca;
      const pointcenter = new BMapGL.Point(lngcenter, latcenter);
      return pointcenter;
    },
    addReguaranteeLogicFHQ() {
      let _this = this;
      let queryData = { "busType": '1' };
      _this.$axios.post("/protect-api/pointLine/getAllPointLineByBus", queryData).then((data) => {
        if (data.data.code === "0000") {
          _this.reguaranteeLogicList = data.data.data.zbPosList;
          _this.reguaranteeLogicList2 = data.data.data.zbLines;
          var data = [];
          let aaaaa = {};
          var randomCount = 2; // 模拟的飞线的数量
          var curve = new mapvgl.BezierCurve();
          _this.reguaranteeLogicList.map((item, index) => {
            let polylineArr1 = [];
            let polylineArr2 = [];
            let polylineArr3 = [];
            polylineArr1.push(new BMapGL.Point(_this.reguaranteeLogicList[0].posLng, _this.reguaranteeLogicList[0].posLat));
            polylineArr2.push(new BMapGL.Point(_this.reguaranteeLogicList[0].posLng, _this.reguaranteeLogicList[0].posLat - 0.0003));
            polylineArr3.push(new BMapGL.Point(_this.reguaranteeLogicList[0].posLng, _this.reguaranteeLogicList[0].posLat - 0.0006));
            let point = new BMapGL.Point(item.posLng, item.posLat);
            let myIcon = new BMapGL.Icon(item.posUrl, new BMapGL.Size(23, 33));
            let marker2 = new BMapGL.Marker(point, { icon: myIcon, "aa": item });  // 创建标注
            if ('1' != item.posType) {
              polylineArr1.push(new BMapGL.Point(item.posLng, item.posLat));
              polylineArr2.push(new BMapGL.Point(item.posLng, item.posLat - 0.0003));
              polylineArr3.push(new BMapGL.Point(item.posLng, item.posLat - 0.0006));
              marker2.customData = item;
              let color = ["#dfde3a", "#dfde3a", "#dfde3a"];
              let border = "2px";
              if (null != item.posId) {
                let colorArr = _this.reguaranteeLogicList2.filter(cc => cc.posIdTarget == item.posId);
                if (undefined != colorArr[0]) {
                  /*       color = colorArr[0].lineColor;*/
                  border = colorArr[0].lineBold;
                }
                if (undefined != colorArr && colorArr.length > 0) {
                  color = colorArr[0].lineColor.split(",");
                }
              }
              let polyline1 = new BMapGL.Polyline(polylineArr1, { strokeColor: color[0], strokeWeight: border[0], strokeOpacity: 0.9 });   //创建折线
              _this.map.addOverlay(polyline1);
              let polyline2 = new BMapGL.Polyline(polylineArr2, { strokeColor: color[1], strokeWeight: border[0], strokeOpacity: 0.9 });   //创建折线
              _this.map.addOverlay(polyline2);
              let polyline3 = new BMapGL.Polyline(polylineArr3, { strokeColor: color[2], strokeWeight: border[0], strokeOpacity: 0.9 });   //创建折线
              _this.map.addOverlay(polyline3);
              _this.map.addOverlay(marker2);
              marker2.customData = item;
            }
            if (index === 0) {
              _this.map.addOverlay(marker2);
              marker2.setAnimation(BMAP_ANIMATION_BOUNCE);
            }
            marker2.customData = item;
            let lableText = _this.initTabchuang1(item);
            marker2.setLabel(lableText);
            _this.clickInfomation = true;

            marker2.addEventListener("mouseout", function () {
                  if (_this.clickInfomation) {
                    let list2Length2 = _this.reguaranteeLogicList.length;
                    if (undefined != document.getElementsByClassName("BMapLabel")[list2Length2]) {
                      document.getElementsByClassName("BMapLabel")[list2Length2].remove();
                    }
                  }
                }
            );
            marker2.addEventListener("click", function () {
              if (_this.clickInfomation) {
                marker2.removeEventListener("mouseout", function () { });
                _this.clickInfomation = false;
              } else {
                marker2.addEventListener("mouseout", function () {
                  let list2Length2 = _this.reguaranteeLogicList.length;
                  if (undefined != document.getElementsByClassName("BMapLabel")[list2Length2]) {
                    document.getElementsByClassName("BMapLabel")[list2Length2].remove();
                  }
                });
                _this.clickInfomation = true;
              }
            });
          });

        }
      });
    },

    addReguaranteeLogicFly() {
      let _this = this;
      let queryData = { "busType": '1' };
      _this.$axios.post("/protect-api/pointLine/getAllPointLineByBus", queryData).then((data) => {
        if (data.data.code === "0000") {
          _this.reguaranteeLogicList = data.data.data.zbPosList;
          _this.reguaranteeLogicList2 = data.data.data.zbLines;
          var data = [];
          let aaaaa = {};
          let color = "#dfde3a";
          let border = "3px";
          var randomCount = 2; // 模拟的飞线的数量
          let a = [];
          let b = [];

          let c = [];
          let d = [];

          let all = [];
          var curve = new mapvgl.BezierCurve();
          // let polylineArrFly = [];
          // for (let index = 0; index < _this.reguaranteeLogicList.length; index++) {
          //   if (_this.reguaranteeLogicList[index].posLevel == "3") {
          //   }
          // }
          _this.reguaranteeLogicList.map((item, index) => {
            // let polylineArr = [];
            // console.log(item);
            if ('3' == item.posLevel) {
              // polylineArr.push(new BMapGL.Point(_this.reguaranteeLogicList[0].posLng, _this.reguaranteeLogicList[0].posLat));
              _this.polylineArrFly.push(item.posLng, item.posLat);

              if (null != item.posId) {
                let colorArr = _this.reguaranteeLogicList2.filter(cc => cc.posIdTarget == item.posId);
                if (undefined != colorArr[0]) {
                  color = colorArr[0].lineColor;
                  border = colorArr[0].lineBold;
                }
              }
            }
          });
          // console.log(_this.polylineArrFly);
          if (_this.polylineArrFly.length == 1) {
            a.push(_this.polylineArrFly[0], _this.polylineArrFly[1]);
            all.push(a);

          } else if (_this.polylineArrFly.length == 3) {
            a.push(_this.polylineArrFly[0], _this.polylineArrFly[1]);
            b.push(_this.polylineArrFly[2], _this.polylineArrFly[3]);
            all.push(a, b);

          } if (_this.polylineArrFly.length == 5) {
            a.push(_this.polylineArrFly[0], _this.polylineArrFly[1]);
            b.push(_this.polylineArrFly[2], _this.polylineArrFly[3]);
            c.push(_this.polylineArrFly[4], _this.polylineArrFly[5]);
            all.push(a, b, c);


          } if (_this.polylineArrFly.length == 7) {
            a.push(_this.polylineArrFly[0], _this.polylineArrFly[1]);
            b.push(_this.polylineArrFly[2], _this.polylineArrFly[3]);
            c.push(_this.polylineArrFly[4], _this.polylineArrFly[5]);
            d.push(_this.polylineArrFly[6], _this.polylineArrFly[7]);



          } if (_this.polylineArrFly.length == 8) {
            a.push(_this.polylineArrFly[0], _this.polylineArrFly[1]);
            b.push(_this.polylineArrFly[2], _this.polylineArrFly[3]);
            c.push(_this.polylineArrFly[4], _this.polylineArrFly[5]);
            d.push(_this.polylineArrFly[6], _this.polylineArrFly[7]);



          }
          a.push(_this.polylineArrFly[0], _this.polylineArrFly[1]);
          b.push(_this.polylineArrFly[2], _this.polylineArrFly[3]);
          c.push(_this.polylineArrFly[4], _this.polylineArrFly[5]);
          // d.push(_this.polylineArrFly[6], _this.polylineArrFly[7]);
          all.push(a, b, c);

          let arrAll = [];
          let objAll = {};
          for (let index = 0; index < all.length; index++) {
            objAll.lng = all[index][0];
            objAll.lat = all[index][1];
            arrAll.push(objAll);


            // console.log(all[0]);
            // aaaaa.lng = Number(all);
            // aaaaa.lat = Number(all);
            // console.log(all[index][0]);
            curve.setOptions({
              start: [_this.reguaranteeLogicList[0].posLng, _this.reguaranteeLogicList[0].posLat],
              end: [objAll.lng, objAll.lat]
            });
            var curveModelData = curve.getPoints();

            data.push({
              geometry: {
                type: 'LineString',
                coordinates: curveModelData
              },
              properties: {
                count: 1
              }
            });

          }

          this.view = new mapvgl.View({
            map: this.map
          });
          var flylineLayer = new mapvgl.FlyLineLayer({
            style: 'chaos',
            step: 0.1,
            color: "#4DCAD4",
            textureColor: function (data) {
              return data.properties.count > 0.9 ? "#4DCAD4" : "#4DCAD4";
            },
            textureWidth: 20,
            textureLength: 100
          });
          // // this.map.removeOverlay(this.map.getOverlays()[4]);

          this.view.addLayer(flylineLayer);
          flylineLayer.setData(data);
        }
      });
    },
    getAllPointLineBus4() {
      let _this = this;
      let queryData = {};
      this.$axios.post("/protect-api/pointLine/getAllPointLineBus4", queryData).then((data) => {
        if (data.data.code === "0000") {
          _this.reguaranteeTopologyList = data.data.data.zbLineList;
          _this.reguaranteeTopologyList2 = data.data.data.zbPosList;
          _this.markList = _this.reguaranteeTopologyList2;
          _this.reguaranteeTopologyList.map(item => {
            let lineStrBDList = item.lineStr.split(";");
            let polylineArr = [];
            lineStrBDList.map(itemNext => {
              let point = new BMapGL.Point(itemNext.split(',')[0], itemNext.split(',')[1]);
              polylineArr.push(point);
            });
            let polyline = new BMapGL.Polyline(polylineArr, { strokeColor: item.lineColor, strokeWeight: item.lineBold[0], strokeOpacity: 0.9 });   //创建折线
            _this.map.addOverlay(polyline);
            //告警点打!
            if (undefined != item && item.lineType === "event") {
              let ppp = item.lineStr.split(";");
              let ll = ppp[0].split(",");
              let ll2 = ppp[1].split(",");
              let pointArr = [new BMapGL.Point(Number(ll[0]), Number(ll[1])), new BMapGL.Point(Number(ll2[0]), Number(ll2[1]))];
              let middenP = _this.getMidpoiont(pointArr);
              let urlP = require("../assets/zxx/warning.png");
              let myIcon = new BMapGL.Icon(urlP, new BMapGL.Size(25, 25));
              let marker3 = new BMapGL.Marker(middenP, { icon: myIcon, "aa": item });  // 创建标注
              _this.map.addOverlay(marker3);
            }
            //告警点打X
            if (undefined != item && item.lineType === "alarm") {
              let ppp = item.lineStr.split(";");
              let ll = ppp[0].split(",");
              let ll2 = ppp[1].split(",");
              let pointArr = [new BMapGL.Point(Number(ll[0]), Number(ll[1])), new BMapGL.Point(Number(ll2[0]), Number(ll2[1]))];
              let middenP = _this.getMidpoiont(pointArr);
              let urlP = require("../assets/zxx/quit.png");
              let myIcon = new BMapGL.Icon(urlP, new BMapGL.Size(16, 16));
              let marker3 = new BMapGL.Marker(middenP, { icon: myIcon, "aa": item });  // 创建标注
              _this.map.addOverlay(marker3);
            }
          });
          _this.reguaranteeTopologyList2.map((item, index) => {
            let point = new BMapGL.Point(item.posLng, item.posLat);
            let myIcon = new BMapGL.Icon(item.posUrl, new BMapGL.Size(23, 33));
            let marker2 = new BMapGL.Marker(point, { icon: myIcon });  // 创建标注
            _this.map.addOverlay(marker2);
            let lableText = _this.initTabchuang(item);
            marker2.setLabel(lableText);
            document.getElementsByClassName("  BMapLabel")[index].style.border = "1px dashed #1ef1a5";
            document.getElementsByClassName("  BMapLabel")[index].style.padding = "";
            marker2.customData = item;
            _this.clickInfomation = true;
            marker2.addEventListener("mouseover", async function (param) {
              let list2Length1 = _this.reguaranteeTopologyList2.length;
              if (_this.clickInfomation && undefined == document.getElementsByClassName("BMapLabel")[list2Length1]) {
                let lableText2 = await _this.tabchuang(item);
                marker2.setLabel(lableText2);
                document.getElementsByClassName("BMapLabel")[list2Length1].style.border = "1px #1ef1a5";
                document.getElementsByClassName("BMapLabel")[list2Length1].style.padding = "";
                _this.queryData = { "busType": '5', "posId": param.target.customData.posId };
                _this.getSTData(_this.queryData);
                _this.getYjData(_this.queryData);
                $("#openZhongbaoTeam").val(param.target.customData);
                document.getElementById("openZhongbaoTeam").onclick = function (e) {
                  let jj = e.target.value;
                  let p = {};
                  p.parentId = "4";
                  if (undefined != jj && null != jj) {
                    _this.$router.push({ path: "/home", query: { p } });
                  }
                };
                document.getElementById("informationExplains").onmouseover = function (e) {
                  $("#informationExplainsDetails").show();
                };
                document.getElementById("informationExplains").onmouseout = function (e) {
                  $("#informationExplainsDetails").hide();
                };
              }
            });
            marker2.addEventListener("mouseout", function () {
              if (_this.clickInfomation) {
                _this.queryData.busType = "5";
                _this.queryData.posId ="";
                _this.getSTData(_this.queryData);
                _this.getYjData(_this.queryData);
                let list2Length2 = _this.reguaranteeTopologyList2.length;
                if (undefined != document.getElementsByClassName("BMapLabel")[list2Length2]) {
                  document.getElementsByClassName("BMapLabel")[list2Length2].remove();
                }
              }
            });
            marker2.addEventListener("click", function () {
              if (_this.clickInfomation) {
                marker2.removeEventListener("mouseout", function () { });
                _this.clickInfomation = false;
              } else {
                marker2.addEventListener("mouseout", function () {
                  _this.queryData.busType = "5";
                  _this.queryData.posId ="";
                  _this.getSTData(_this.queryData);
                  _this.getYjData(_this.queryData);
                  let list2Length2 = _this.reguaranteeTopologyList2.length;
                  if (undefined != document.getElementsByClassName("BMapLabel")[list2Length2]) {
                    document.getElementsByClassName("BMapLabel")[list2Length2].remove();
                  }
                });
                _this.clickInfomation = true;
              }
            });
          });
          _this.map.addEventListener("zoomend", function (e) {
            if (_this.markShowSt) { return false; };
            let zoom = _this.map.getZoom(); // 获取缩放级别
            if (zoom < 14) {
              for (let i = 0; i < _this.reguaranteeTopologyList2.length; i++) {
                if (undefined != document.getElementsByClassName("BMapLabel")[i]) {
                  document.getElementsByClassName("BMapLabel")[i].style.display = "none";
                }
              }
            } else {
              for (let i = 0; i < _this.reguaranteeLogicList.length; i++) {
                if (undefined != document.getElementsByClassName("BMapLabel")[i]) {
                  document.getElementsByClassName("BMapLabel")[i].style.display = "";
                }
              }
            }
          });
          $(".BMapLabel").hide();
        }
      });
    },
    initTabchuang(param) {
      let url = param.posRealUrl;
      let bottom2 = require("../assets/zxx/bottom2.png");
      let locationTab = require("../assets/zxx/locationTab.png");
      let text = '<div style="z-index: 1; width:170Px;height: 112Px;background-image: url(' + locationTab + ') ;background-repeat: no-repeat;background-size: 100% 100%;">' +
          '<div style="z-index: 1;display: flex;' +
          'flex-direction: row;align-content: center;justify-content: space-between; align-items: center;">' +
          '            <img src="' + url + ' " id="imgDemo" alt="" style="height:90px;width:100%">' +
          '    </div>' +
          '<div style="width: 100%;height: 18px;font-size: 14px;text-align:center;font-family: Source Han Sans CN;font-weight: 400;color: #FFFFFF;line-height: 22px;">' + param.posName + '</div>' +
          '</div>' +
          '<div style="position: relative;"> <img src="' + bottom2 + ' "  alt="" style="position: absolute;left: 45%;"></div>';
      var label = new BMapGL.Label(text, { offset: new BMapGL.Size(-91, -150) });
      return label;
    },

    initTabchuang1(param) {
      let url = param.posRealUrl;
      let bottom2 = require("../assets/zxx/bottom2.png");
      let locationTab = require("../assets/zxx/locationTab.png");
      let text = '<h1 style="width: 100%;height: 18px;font-size: 14px;text-align:center;font-family: Source Han Sans CN;font-weight: 400;color: #000000;line-height: 22px;">111111</h1>';
      var label = new BMapGL.Label(text, { offset: new BMapGL.Size(-91, -150) });
      return label;
    },
    async tabchuang(param) {
      let _this = this;
      let bottom = require("../assets/zxx/bottom.png");
      let explains = require("../assets/zxx/explains.png");
      let query = {};
      query.dataType = "1";
      if (param.posLevel == 'X') {
        if (param.posName === "联通省公司") {
          query.userGroupType = "GROUP_SF";
          query.posId = "";
        } else if (param.posName === "联通西安分公司") {
          query.userGroupType = "GROUP_XF";
          query.posId = "";
        }
      } else {
        query.posId = param.posId;
        query.userGroupType = "GROUP_XC";
      }
      /*      let query={};*/
      await _this.$axios.post("/protect-api/person/getAllPersonList", query).then((data) => {
        if (data.data.code === "0000") {
          _this.zbPosiNFO = data.data.data;
          /*          param.target.customInfo = _this.zbPosiNFO;*/
        } else {
          _this.zbPosiNFO = null;
        }
      });
      let text1 = '<div id="information" style="z-index: 1003;width: 365Px;height: 265Px;border: 1Px solid;border-image: linear-gradient(0deg, #4AB38C, #105745) 10 10;background: linear-gradient(0deg, #0E2D28 0%, rgba(7,70,83,0.8) 100%);">' +
          '    <div>' +
          '        <div style="z-index:3;width: 365Px;height: 39Px;background: linear-gradient(90deg, #25BFAB 0%, rgba(25,146,156,0.5) 100%);opacity: 0.3;">' +
          '        </div>' +
          '        <span style="z-index:3;margin-top:-38px;margin-left:10px;position:fixed;width: 78Px;opacity: 1;height: 18Px;font-size: 18Px;font-family: Source Han Sans CN;' +
          '        font-weight: 500;color: #FFFFFF;line-height: 38Px;text-shadow: 0Px 2Px 8Px rgba(5,28,55,0.42);">重保团队</span>' +
          '        <span id="informationExplains" style="position: fixed;margin-top: -33px;margin-left: 85px;"><img src="' + explains + '" style="width: 16px;height:16px;"></span>' +
          '        <span id="informationExplainsDetails" style="position: fixed;z-index:4;margin-top: -70px;margin-left: 80px;' +
          'background: #07607c;position: absolute;border-radius: 4px;padding: 10px;font-size: 12px;line-height: 1.2;min-width: 10px;word-wrap: break-word;' +
          ' color: #FFF;display: none">注：再次点击图标隐藏此弹窗（点击定位图标）</span>' +
          '        <div id="openZhongbaoTeam" style="margin-top:-32px;margin-left:313px;position:fixed;width: 50px;height: 14px;font-size: 14px;font-family: Source Han Sans CN;font-weight: 500;color: #FFFFFF;line-height: 29px;cursor:pointer;">详情 ></div>' +
          '    </div>' +
          '    <div class="contont" style="z-index:1003; position:relative; display: inline-block;width: 360px;height: 217px;font-size: 14px;font-family: Source Han Sans CN;font-weight: 400;color: #FFFEFE;line-height: 28px; overflow-y:auto;overflow-x:hidden;">';
      let text3 = '    </div>' +
          '</div>' +
          '<div style="position: relative;margin-top: -1px;"> <img src="' + bottom + ' "  alt="" style="position: absolute;left: 45%;"></div>';
      let text2 = "";
      if (undefined != _this.zbPosiNFO && null != _this.zbPosiNFO&& _this.zbPosiNFO.length>0) {
        _this.zbPosiNFO.map(item => {
          text2 = text2 + ' <div style="width: 352px;height: 28px;background: #0E4950;margin: 7px 6px">' +
              '            <span style="width: 6Px;height: 6Px;margin-left: 10Px; background: #F19149;border-radius: 50%;display: inline-block;"></span>' +
              '            <span style="display: inline-block;margin-left:3px;width: 60px;height: 25px;font-size: 14px;font-family: Source Han Sans CN;font-weight: 400;color: #30DFE9;line-height:28px;' +
              '  ">' + item.userName + '</span>' +
              '            <span style="margin-left:13px;width: 41px;height: 14px;font-size: 14px;font-family: Source Han Sans CN;font-weight: 400;line-height:28px;">电话：</span>' +
              '            <span style="margin-left:-2px;width: 41px;height: 14px;font-size: 14px;font-family: Source Han Sans CN;font-weight: 400;color: #30DFE9;line-height:28px;">' + item.userPhone + '</span>' +
              '            <span style="margin-left:13px;width: 41px;height: 14px;font-size: 14px;font-family: Source Han Sans CN;font-weight: 400;line-height:28px;">专业：</span>' +
              '            <span style="width: 41px;height: 14px;font-size: 14px;font-family: Source Han Sans CN;font-weight: 400;color: #30DFE9;line-height:28px;">' + item.userProfession + '</span>' +
              '        </div>';
        });
      } else {
        text2 = '        <div style="width: 352px;height: 28px;background: #0E4950;margin: 7px 6px">' +
            '            <span style="width: 6Px;height: 6Px;margin-left: 10Px; background: #F19149;border-radius: 50%;display: inline-block;"></span>' +
            '            <span style="margin-left:3px;width: 41px;height: 14px;font-size: 14px;font-family: Source Han Sans CN;font-weight: 400;color: #30DFE9;line-height:28px;">暂无信息</span>' +
            '        </div>';
      }
      let label = new BMapGL.Label(text1 + text2 + text3, { offset: new BMapGL.Size(-177, -301) });
      return label;
    },



    changeDarkStyle() {
      this.map.setOptions({
        style: {
          styleJson: this.darkStyle
        },
        styleUrl: 'https://gis.10010.com:8219/dugis-baidu/baidumap/bmapgl/mapstyle/mapstyle.json',
      });
    },

    changeNormalStyle() {
      this.map.setOptions({
        style: 'default',
        styleUrl: '',
      });
    }

  },
};
</script>

<style lang="less" scoped>
#allmap {
  width: 100%;
  height: 100%;
  overflow: hidden;
  z-index: 1;
  font-family: "微软雅黑";
}

.numberStyle {
  color: red;
}

.fixed-search-box {
  width: 100%;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 2;
  box-shadow: 0 2Px 10Px 0 rgb(0 0 0 / 10%);
}

.header-long {
  position: fixed;
  width: 100%;
  height: 70Px;
  line-height: 70Px;
  z-index: 2;
  background-image: url('../assets/img_slices/bg.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
}

.header-right-middle {
  position: fixed;
  width: 390Px;
  height: 63Px;
  margin-left: 764Px;
  background-image: url('../assets/zxx/middleText2.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
}

.header-right-middle-span {
  width: 356Px;
  height: 34Px;
  font-size: 36Px;
  font-family: Alimama ShuHeiTi;
  font-weight: bold;
  color: #C6F6FF;
  line-height: 50Px;
  opacity: 0.89;
  text-shadow: 0Px 4Px 6Px rgba(7, 46, 26, 0.7);

  background: linear-gradient(0deg, rgba(119, 255, 253, 0.45) 0%, rgba(233, 248, 255, 0.45) 73.3154296875%, rgba(255, 255, 255, 0.45) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;

}

.imgLT {
  background-image: url('../assets/zxx/lt.png');
  background-repeat: no-repeat;
  background-size: 81% 81%;
  width: 84Px;
  height: 46Px;
  margin: 4Px 10Px;
}

.dateSpan {
  margin-left: 1740Px;
  margin-top: 12Px;
  position: absolute;
}

.date {
  text-align: left;
  width: 200Px;
  line-height: 14Px;
  color: #07697f;
  font-size: 14Px;
}

.buttonZ1 {
  z-index: 2;
  position: fixed;
  margin-left: 678Px;
  margin-top: 88Px;
  width: 126Px;
  height: 38Px;
  box-shadow: 0 0 2Px #107093;
  text-align: center;
  background-image: url('../assets/zxx/button2.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
}

.buttonZ2 {
  z-index: 2;
  position: fixed;
  margin-left: 819Px;
  margin-top: 88Px;
  width: 126Px;
  box-shadow: 0 0 2Px #107093;
  height: 38Px;
  text-align: center;
  background-image: url('../assets/zxx/button2.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
}

.buttonZ3 {
  z-index: 2;
  position: fixed;
  margin-left: 959Px;
  margin-top: 88Px;
  width: 136Px;
  height: 38Px;
  box-shadow: 0 0 2Px #107093;
  text-align: center;
  background-image: url('../assets/zxx/button2.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
}

.buttonZ4 {
  z-index: 2;
  position: fixed;
  margin-left: 1109Px;
  margin-top: 88Px;
  box-shadow: 0 0 2Px #107093;
  width: 136Px;
  height: 38Px;
  text-align: center;
  background-image: url('../assets/zxx/button2.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
}

.buttonSpanCur {
  z-index: 2;
  width: 82Px;
  cursor:pointer;
  height: 19Px;
  font-size: 20Px;
  font-family: Alimama ShuHeiTi;
  font-weight: bold;
  color: #DAFFFC;
  line-height: 35Px;
  /*  background: linear-gradient(0deg, rgba(239,251,255,0.34) 0%, rgba(0,0,0,0.34) 11.9140625%, rgba(255,254,254,0.34) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;*/
}

.buttonSpan {
  cursor:pointer;
  width: 82Px;
  height: 19Px;
  font-size: 20Px;
  font-family: Alimama ShuHeiTi;
  font-weight: bold;
  color: #8DE8EE;
  line-height: 35Px;
  opacity: 0.85;
}

.mapLeftTop1 {
  z-index: 2;
  position: fixed;
  margin-left: 32Px;
  margin-top: 191Px;
  width: 420Px;
  height: 180Px;
  background-image: url('../assets/zxx/mapLeftTop3.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
}

/*滚动条样式*/
.mapLeftTop1div::-webkit-scrollbar {
  width: 4px;
  /*height: 4px;*/
}

.mapLeftTop1div::-webkit-scrollbar-thumb {
  border-radius: 10px;
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  background: rgba(0, 0, 0, 0.2);
}

.mapLeftTop1div::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  border-radius: 0;
  background: rgba(0, 0, 0, 0.1);
}

.mapLeftTopTitle {
  margin-left: 43Px;
  margin-top: 99Px;
  width: 95Px;
  height: 23Px;
  font-size: 18Px;
  font-family: Alimama ShuHeiTi;
  font-weight: bold;
  color: #FFFFFF;
  line-height: 44Px;
  text-shadow: 0Px 2Px 8Px rgba(14, 197, 236, 0.36);
}

.mapRightTopTitle {
  z-index: 2;
  width: 18Px;
  height: 18Px;
  line-height: 44Px;
  margin: 13Px 13Px;
  float: right;
  background-image: url('../assets/zxx/reset3.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
  display: inline-block;
  transition: all 0.3s ease;
}

.mapRightTopTitle:hover {
  transform: rotate(360deg) scale(2.0);

  -webkit-transform: rotate(360deg) scale(2.0);

  -moz-transform: rotate(360deg) scale(2.0);

  -o-transform: rotate(360deg) scale(2.0);

  -ms-transform: rotate(360deg) scale(2.0);
}

.mapRightTopTitle:active {
  transform: scale(0.8);
}

.mapLeftTop1div {
  margin-left: 16Px;
  margin-top: 25Px;
  width: 388Px;
  height: 97Px;
  overflow: auto;
  z-index: 1;
  background: #0C3038;
  display: inline-block;
  opacity: 0.8;
}

.mapLeftTop1Radis {
  width: 8Px;
  height: 8Px;
  margin-left: 16Px;
  background: #39D4CD;
  border-radius: 50%;
  display: inline-block;
}

.mapLeftTopName {
  width: 100%;
  // height: 100px;
  font-size: 15Px;
  font-family: Source Han Sans CN;
  font-weight: 500;
  color: #39D4CD;
  line-height: 35Px;
  margin-left: 7Px;
}

.mapLeftTopText {
  width: 335Px;
  height: 50Px;
  font-size: 14Px;
  font-family: Source Han Sans CN;
  font-weight: 400;
  color: #FFFFFF;
  line-height: 20Px;
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
}

.mapLeftTop2 {
  z-index: 2;
  position: fixed;
  float: left;
  margin-left: 32Px;
  margin-top: 397Px;
  width: 420Px;
  height: 390Px;
  /*  height: 282Px;*/
  background-image: url('../assets/zxx/mapLeftTop23.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
  display: inline-block;
}

.mapLeftTop2Img1 {
  margin-top: 17Px;
  margin-left: 16Px;
  width: 66Px;
  height: 66Px;
  background-image: url('../assets/zxx/mapLeftTop2Img1.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
  display: inline-block;
}

.mapLeftTop2Img2 {
  margin-top: 17Px;
  margin-left: 16Px;
  width: 66Px;
  height: 66Px;
  background-image: url('../assets/zxx/mapLeftTop2Img2.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
  display: inline-block;
}

.mapLeftTop2Text {
  margin-top: 41Px;
  margin-left: 45Px;
  width: 75Px;
  font-size: 16Px;
  font-family: Source Han Sans CN;
  font-weight: 400;
  color: #FFFFFF;
  position: absolute;
  display: inline-block;
}

.mapLeftTop2Text2 {
  position: absolute;
  margin-left: 200Px;
  margin-top: 27Px;
  width: 62Px;
  height: 27Px;
  font-size: 28Px;
  font-family: D-DIN-PRO;
  font-weight: 500;
  color: #FABB61;
  line-height: 50Px;
  display: inline-block;
}

.mapLeftTop2Text3 {
  position: absolute;
  width: 62Px;
  height: 27Px;
  font-size: 28Px;
  font-family: D-DIN-PRO;
  font-weight: 500;
  color: #FABB61;
  line-height: 50Px;
  display: inline-block;
}

.mapLeftTop2Div {
  width: 388Px;
  height: 97Px;
  z-index: 1;
  background: #0C3038;
  display: inline-block;
  opacity: 0.8;
}

.mapLeftTop3 {
  z-index: 2;
  position: fixed;
  margin-left: 32Px;
  margin-top: 812Px;
  width: 420Px;
  height: 175Px;
  background-image: url('../assets/zxx/mapLeftTop3.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
}

.mapLeftTop2Img3 {
  margin-top: 17Px;
  margin-left: 16Px;
  width: 66Px;
  height: 66Px;
  background-image: url('../assets/zxx/mapLeftTop2Img3.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
  display: inline-block;
}

.mapLeftTop2Text2Top {
  width: 140Px;
  height: 24Px;
  font-size: 28Px;
  font-family: D-DIN-PRO;
  font-weight: 500;
  text-align: center;
  /*  color: #2DC3BA;*/
  position: absolute;
}

.mapLeftTop2Text2Bottom {
  width: 140Px;
  height: 38Px;
  font-size: 14Px;
  text-align: center;
  font-family: Source Han Sans CN;
  font-weight: 400;
  color: #FFFFFF;
  position: absolute;
}

.mapRightTop1 {
  z-index: 2;
  position: fixed;
  margin-left: 1473Px;
  margin-top: 617Px;
  width: 420Px;
  height: 175Px;
  background-image: url('../assets/zxx/mapLeftTop3.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
}

.mapRightTop2 {
  z-index: 2;
  position: fixed;
  margin-left: 1473Px;
  margin-top: 815Px;
  width: 420Px;
  height: 175Px;
  background-image: url('../assets/zxx/mapLeftTop3.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
}

.mapRightTop3 {
  z-index: 2;
  position: fixed;
  margin-left: 1473Px;
  margin-top: 180Px;
  width: 420Px;
  height: 420Px;
  background-image: url('../assets/zxx/glbg.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
}


.mapLeftTopTitleDetalis {
  margin-left: 4Px;
  margin-top: 15Px;
  width: 17Px;
  height: 16Px;
  background-image: url('../assets/zxx/details.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
  display: inline-block;
  position: absolute;
}

/deep/.BMap_bubble_pop {
  background-color: transparent;
}
.mapLeftTopz {
  z-index: 3;
  position: fixed;
  margin-left: 32Px;
  margin-top: 191Px;
  width: 420Px;
  height: 50Px;
}
.left {
  z-index: 11;
  width: 20Px;
  height: 20Px;
  background-image: url('../assets/zxx/details.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
  position: relative;
  float: left;
  margin: 12Px 12Px;
}

.right {
  z-index: 11;
  width: 20Px;
  height: 20Px;
  background-image: url('../assets/zxx/left.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
  position: relative;
  float: right;
  margin: 12Px 12Px;
}

.topologyBackground {
  width: 100%;
  height: 100%;
  z-index: 1;
  width: 1920Px;
  height: 1080Px;
  position: relative;
  background-image: url('../assets/zxx/topologyBack.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
}

.topology {
  z-index: 1;
  width: 100%;
  // margin: 0 auto;
  height: 100%;
  display: flex;
  justify-content: center;
  // background-image: url('../assets/zxx/topology.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
  // position: absolute;

}

.item-list {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
}
.item-list2 {
  width: 100%;
  height: 100%;
  position: relative;
  top: 0;
}

.item {
  position: relative;
  display: flex;
  align-items: center;
  flex-direction: column;
  padding-right: .2rem;
  // color: #333;
  // line-height: .17rem;
  overflow: hidden;
  width: 100%;
  height: 7.35rem;
  // width: 388Px;
  // height: 97Px;
  z-index: 1;
  background: #0C3038;
  display: inline-block;
  opacity: 0.8;

  .mapLeftTop1Radis {
    width: 8Px;
    height: 8Px;
    margin-left: 16Px;
    background: rgba(252, 164, 58, 1);
    border-radius: 50%;
    display: inline-block;
  }

  .mapLeftTop1Text {
    font-size: 16px;
    font-family: Source Han Sans CN;
    font-weight: 400;
    color: #FFFFFF;
    // line-height: 50px;
  }

  .mapLeftTopText {
    font-size: 14px;
    font-family: Source Han Sans CN;
    font-weight: 400;
    color: #FFFFFF;
  }

}

.fix_content {
  position: absolute;
  width: 98%;
  height: 310px;
  overflow: hidden;
  top: 53px;
  left: 0;
}

.alog {
  width: 100%;
  height: 53%;
}

/* 定义滚动条样式 */
/deep/.contont::-webkit-scrollbar {
  width: 3px;
  height: 3px;
  background-color: rgba(240, 240, 240, 1);
}

/deep/.contont::-webkit-scrollbar:hover {
  width: 6px !important;
}

/*定义滚动条轨道 内阴影+圆角*/
/deep/.contont::-webkit-scrollbar-track {
  box-shadow: inset 0 0 0px rgba(240, 240, 240, .5);
  border-radius: 10px;
  background-color: rgba(240, 240, 240, .5);
}

/deep/.contont:hover::-webkit-scrollbar-corner {
  width: 6px;
  border: 5px solid #06363b;
  background-color: #06363b;
  /*background-color: red !important;*/
}

/*定义滑块 内阴影+圆角*/
/deep/.contont::-webkit-scrollbar-thumb {
  border-radius: 10px;
  box-shadow: inset 0 0 0px #317179;
  background-color: #317179;
}

/deep/.contont::-webkit-scrollbar-thumb:hover {
  border: 5px solid #06363b;
  background-color: #06363b;
}

.mapRightTop1Img1 {
  margin-top: 17Px;
  margin-left: 16Px;
  width: 66Px;
  height: 66Px;
  background-image: url('../assets/zxx/mapRightTop1Img1.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
  display: inline-block;
}

.mapRightTop1Img2 {
  margin-top: 17Px;
  margin-left: 16Px;
  width: 66Px;
  height: 66Px;
  background-image: url('../assets/zxx/mapRightTop1Img2.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
  display: inline-block;
}

.mapRightTop1Img3 {
  margin-top: 17Px;
  margin-left: 147Px;
  width: 66Px;
  height: 66Px;
  background-image: url('../assets/zxx/mapRightTop1Img3.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
  display: inline-block;
}

/deep/.stDataDiv1 {
  width: 112px;
  height: 40px;
  background: linear-gradient(-51deg, #035fd5 0%, #4FA1FF 100%);
  border: 2px solid;
  border-image: linear-gradient(-55deg, rgba(145, 202, 254, 0.5), rgba(59, 134, 220, 0.5)) 2 2;
  opacity: 0.9;
  text-align: center;
}

/deep/.stDataDiv2 {
  width: 112px;
  height: 40px;
  background: linear-gradient(-51deg, #306747 0%, #47F195 100%);
  border: 2px solid;
  border-image: linear-gradient(-55deg, rgba(133, 255, 189, 0.5), rgba(90, 219, 149, 0.5)) 2 2;
  opacity: 0.9;
  text-align: center;
}

/deep/.stDataDiv3 {
  width: 141px;
  height: 40px;
  display: inline-block;
  background: linear-gradient(-51deg, #734c1c 0%, #ECB26A 100%);
  border: 2px solid;
  border-image: linear-gradient(-55deg, rgba(253, 215, 169, 0.5), rgba(255, 172, 71, 0.5)) 2 2;
  opacity: 0.9;
  text-align: center;
}

/deep/.stDataDiv4 {
  width: 130px;
  height: 40px;
  background: linear-gradient(-51deg, #035fd5 0%, #4FA1FF 100%);
  border-image: linear-gradient(-55deg, rgba(145, 202, 254, 0.5), rgba(59, 134, 220, 0.5)) 2 2;
  opacity: 0.9;
  text-align: center;
  border-radius: 20px;
}

/deep/.stDataDiv5 {
  width: 112px;
  height: 40px;
  background: linear-gradient(-51deg, #035fd5 0%, #1668c4 100%);
  border: 2px solid;
  border-image: linear-gradient(-55deg, rgba(145, 202, 254, 0.5), rgba(59, 134, 220, 0.5)) 2 2;
  opacity: 0.9;
  text-align: center;
}

/deep/.stDataDiv6 {
  width: 141px;
  height: 40px;
  display: inline-block;
  background: linear-gradient(-51deg, #734c1c 0%, #ec9a37 100%);
  border: 2px solid;
  border-image: linear-gradient(-55deg, rgba(253, 215, 169, 0.5), rgba(255, 172, 71, 0.5)) 2 2;
  opacity: 0.9;
  text-align: center;
}

/deep/.stDataSpan1 {
  width: 91px;
  height: 18px;
  font-size: 16px;
  font-family: Source Han Sans CN;
  font-weight: 500;
  color: #091220;
  display: inline-block;
  line-height: 40px;
}

/deep/.stDataSpan4 {
  width: 91px;
  height: 18px;
  font-size: 16px;
  font-family: Source Han Sans CN;
  font-weight: 500;
  background: transparent;
  color: #55A4FE;
  display: inline-block;
  line-height: 40px;
}
.noticePopover{
  z-index: 6;
  margin-left: 560Px;
  margin-top: -730Px;
  position: absolute;
  width: 800Px;
  height: 312px;
  background-image: url('../assets/zxx/noticePopover.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
}
.alarmPopover{
  z-index: 6;
  margin-left: 500Px;
  margin-top: -730Px;
  position: absolute;
  width: 929Px;
  height: 450Px;
  background-image: url('../assets/zxx/alarm.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
}
.noticePopoverTitle{
  position: relative;
  margin-top: 17Px;
  margin-left: 35Px;
  width: 198Px;
  height: 19Px;
  font-size: 18Px;
  font-family: Alimama ShuHeiTi;
  font-weight: bold;
  color: #FFFFFF;
  line-height: 50Px;
}
.alarmPopoverTitle{
  position: relative;
  margin-top:7Px;
  margin-left: 35Px;
  width: 900Px;
  height: 19Px;
  font-size: 18Px;
  font-family: Alimama ShuHeiTi;
  font-weight: bold;
  color: #FFFFFF;
  line-height: 50Px;
}
.alarmPopoverText4{
  position: relative;
  text-align: center;
  width: 900Px;
  font-size: 16Px;
  margin-top:25Px;
  margin-left: 18Px;
  font-family: Source Han Sans CN;
  font-weight: 400;
  color: #FFFFFF;
  height: 24Px;
  line-height: 24Px;
}
.alarmPopoverX{
  z-index: 6;
  position: fixed;
  margin-top: 0Px;
  margin-left: 883Px;
  width: 14Px;
  height: 14Px;
  cursor:pointer;
  background-image: url('../assets/zxx/X.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
}
.noticePopoverX{
  z-index: 6;
  position: fixed;
  margin-top: 0Px;
  margin-left: 750Px;
  width: 14Px;
  height: 14Px;
  cursor:pointer;
  background-image: url('../assets/zxx/X.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
}
.noticePopoverText{
  position: relative;
  margin-top: 10Px;
  margin-left: 30Px;
  width: 757Px;
  min-height: 88Px;
  max-height: 190Px;
  font-size: 16Px;
  font-family: Source Han Sans CN;
  font-weight: 400;
  color: #FFFFFF;
  line-height: 24Px;
  overflow-y:auto ;
}
.noticePopoverText2{
  position: relative;
/*  margin-top: 12Px;*/
  text-align: right;
  font-size: 16Px;
  width: 770Px;
  height: 24Px;
  font-family: Source Han Sans CN;
  font-weight: 400;
  color: #FFFFFF;
  line-height: 24Px;
}
.noticePopoverText3{
  position: relative;
  text-align: right;
  width: 770Px;
  font-size: 16Px;
  font-family: Source Han Sans CN;
  font-weight: 400;
  color: #FFFFFF;
  height: 24Px;
  line-height: 24Px;
}
.noticePopoverText4{
  position: relative;
  text-align: center;
  width: 770Px;
  font-size: 16Px;
  margin-top: 46Px;
  font-family: Source Han Sans CN;
  font-weight: 400;
  color: #FFFFFF;
  height: 24Px;
  line-height: 24Px;
}
/* 定义滚动条样式 */
/deep/.noticePopoverText::-webkit-scrollbar {
  width: 3px;
  height: 3px;
  background-color: rgba(240, 240, 240, 1);
}

/deep/.noticePopoverText::-webkit-scrollbar:hover {
  width: 6px !important;
}

/*定义滚动条轨道 内阴影+圆角*/
/deep/.noticePopoverText::-webkit-scrollbar-track {
  box-shadow: inset 0 0 0px rgba(240, 240, 240, .5);
  border-radius: 10px;
  background-color: rgba(240, 240, 240, .5);
}

/deep/.noticePopoverText:hover::-webkit-scrollbar-corner {
  width: 6px;
  border: 5px solid #06363b;
  background-color: #06363b;
  /*background-color: red !important;*/
}

/*定义滑块 内阴影+圆角*/
/deep/.noticePopoverText::-webkit-scrollbar-thumb {
  border-radius: 10px;
  box-shadow: inset 0 0 0px #317179;
  background-color: #317179;
}

/deep/.noticePopoverText::-webkit-scrollbar-thumb:hover {
  border: 5px solid #1f5c65;
  background-color: #1f5c65;
}
/deep/.el-table .el-table__cell.gutter {
  width: 0px !important;
  height: 0px;
  display: none;
}
/deep/ .el-table__body-wrapper::-webkit-scrollbar:hover {
  width: 8px !important;
}

/*定义滚动条轨道 内阴影+圆角*/
/deep/ .el-table__body-wrapper::-webkit-scrollbar-track {
  box-shadow: inset 0 0 0px rgba(240, 240, 240, .5);
  border-radius: 10px;
  background-color: rgba(240, 240, 240, .5);
}

/deep/ .el-table__body-wrapper:hover::-webkit-scrollbar-corner {
  width: 6px;
}

/*定义滑块 内阴影+圆角*/
/deep/ .el-table__body-wrapper::-webkit-scrollbar-thumb {
  border-radius: 10px;
  box-shadow: inset 0 0 0px #206977;
  background: linear-gradient(-55deg, #0A3038 0%, #114D59 100%);
}

/deep/ .el-table__body-wrapper::-webkit-scrollbar {
  width: 5px; // 横向滚动条
  height: 5px; // 纵向滚动条 必写
}
/deep/ .el-pagination.is-background .el-pager li {
  background-color: transparent;
}

/deep/ .el-table td.el-table__cell div {
  color: #E1E1E1;
}

/deep/.el-table,
.el-table__expanded-cell {
  background-color: transparent;
}
/deep/.el-table tr th.el-table__cell>.cell {
  font-size: 16Px;
  font-family: Source Han Sans CN;
  font-weight: 400;
  color: #2AB8B7;
  line-height: 38Px;
}
/deep/.el-table .cell {
  font-size: 15Px;
  font-family: Source Han Sans CN;
  font-weight: 400;
  color: #2AB8B7;
  line-height: 36Px;
}
/deep/.el-table .el-table__cell {
  padding: 2Px 0;
  min-width: 0;
  box-sizing: border-box;
  text-overflow: ellipsis;
  vertical-align: middle;
  position: relative;
  text-align: left;
}
/deep/.el-table tr {
  background-color: transparent;
}
/deep/.el-table--striped .el-table__body tr.el-table__row--striped td.el-table__cell {
  background: transparent !important;
}
/deep/ .el-table .warning-row {
  background: transparent !important;
}

/deep/.el-table .success-row {
  background: #094a57 !important;
}
/deep/.el-table--enable-row-hover .el-table__body tr:hover>td.el-table__cell {
  background-color: #082a37FF !important;

}
/deep/.el-table--enable-row-hover .el-table__body tr:hover {
  background-color: transparent !important;
}
/deep/.el-table--enable-row-transition .el-table__body td.el-table__cell {
  border: none;
}
/deep/.el-table th.el-table__cell.is-leaf {
  border: none;
  border-bottom: none;
}

.el-pagination {
  float: right;
}

/deep/ .el-table__body-wrapper::-webkit-scrollbar:hover {
  width: 8px !important;
}

/*定义滚动条轨道 内阴影+圆角*/
/deep/ .el-table__body-wrapper::-webkit-scrollbar-track {
  box-shadow: inset 0 0 0px rgba(240, 240, 240, .5);
  border-radius: 10px;
  background-color: rgba(240, 240, 240, .5);
}

/deep/ .el-table__body-wrapper:hover::-webkit-scrollbar-corner {
  width: 6px;
}

/*定义滑块 内阴影+圆角*/
/deep/ .el-table__body-wrapper::-webkit-scrollbar-thumb {
  border-radius: 10px;
  box-shadow: inset 0 0 0px #206977;
  background: linear-gradient(-55deg, #0A3038 0%, #114D59 100%);
}

/deep/ .el-table__body-wrapper::-webkit-scrollbar {
  width: 5px; // 横向滚动条
  height: 5px; // 纵向滚动条 必写
}
.el-button.is-plain:focus, .el-button.is-plain:hover {
  // width: 58px;
  height: 32Px;
  font-size: 14Px;
  font-family: Source Han Sans CN;
  font-weight: 400;
  color: #2AB8B7;
  line-height: 29Px;
  padding: 0px;
  padding-left: 10px;
  padding-right: 10px;
  /*      padding-top: 10px;*/
  padding-bottom: 10px;
  border: 1px solid #1a9695;
  background-color: #038793;
  box-shadow: 0 0 5px #1a9695;
  color: #fff;
}
</style>
