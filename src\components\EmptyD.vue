<template>
  <!-- 空状态 -->
  <div class="empty"></div>
</template>
 
<script>
export default {
  name: "Empty",
  components: {},
  data() {
    return {};
  },
  created() {
    this.$router.replace({ path: "/customer" });
  },
};
</script>
 
<style scoped lang='scss'>
.empty {
  flex-direction: column;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>