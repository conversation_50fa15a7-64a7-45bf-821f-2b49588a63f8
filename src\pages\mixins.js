import { formatTime } from "../assets/js/index.js";
import darkStyle from '../assets/js/darkStyle.json';
import urlCar from '../assets/zxx/car.gltf';
import $ from "jquery";
import begin from '../assets/zxx/begin.png';
import ing from '../assets/zxx/ing.png';

import NoticeComponent from './cardList/noticeComponent.vue';      // 公告信息组件
import NetworkComponent from './cardList/NetworkComponent.vue';    // 数通设备组件
import TeamComponent from './cardList/TeamComponent.vue';          // 重保团队组件
import CircuitComponent from './cardList/CircuitComponent.vue';    // 线路状态组件
import AlarmComponent from './cardList/AlarmComponent.vue';        // 当前告警数量组件
import SuppliesComponent from './cardList/SuppliesComponent.vue';  // 应急物资组件
export default {
    components: {
        NoticeComponent,
        NetworkComponent,
        TeamComponent,
        CircuitComponent,
        AlarmComponent,
        SuppliesComponent
      },
    data() {
        return {
          startItem:'',
          alarmNoZ:'',
          alarmSyncType:'0',
          curAlarmSyn: false,
          alarmSyncCount: '0',
          alarmSync:'0',
          alarmSync2:'0',
          busTypeZ:'其它',
          curAlarm : '10',
          tableData :[],
          noticePopoverShow:false,
          alarmPopoverShow:false,
          urlButton: ing,
          beginUrl: begin,
          ingUrl: ing,
          beginUrlShow: true,
          showTopology: false,
          urlCar: urlCar,
          sTDataInfo: { "fhqNum": 0, "jhjNum": 0 },
          showTail: true,
          darkStyle: darkStyle,
          dateDay: null,
          dateYear: null,
          dateDay2: null,
          dateYear2: null,
          dateWeek: null,
          weekday: ["周日", "周一", "周二", "周三", "周四", "周五", "周六"],
          eventData: [],
          zbGroup: [],
          alarmData: {},
          yjData: [],
          title: "",
          carlineLayer: null,
          rightTitle: "业务信息",
          lastArr: [],
          XlInfoData: [],
          titleZm: "带宽",
          titleZmLt: "告警",
          polylineArrFly: [],
          timeA: null,
          timeB: null,
          numberStyle: "",
          numberColor: "#fff",
          markShowSt: false,
          noticePopoverObj :null,
          alarmPopoverObj:null,
          cerrorColor: "",
          canvasObj :document.getElementsByTagName("canvas"),
          canvasCount :0,
        //   拖拽
        showTail: true,
      // 组件基础配置（ID、标题、样式类名）
      
      // draggableItems: [], // 可拖拽组件列表（动态排序）
      // leftItems: [],   // 左侧画布组件
      // rightItems: [],  // 右侧画布组件
      // 模拟数据
      eventData: [
        { id: 1, mesTitle: '系统升级通知', mesTime: '2025-06-17', mesName: '系统将于今晚23:00-01:00进行例行维护升级' },
        { id: 2, mesTitle: '安全告警', mesTime: '2025-06-16', mesName: '检测到异常登录尝试，已自动拦截并加强安全防护' },
        { id: 3, mesTitle: '性能优化', mesTime: '2025-06-15', mesName: '网络性能优化已完成，响应速度提升约30%' }
      ],
      sTDataInfo: {
        fhqNum: 12,
        jhjNum: 36,
        posAqNum: 8
      },
      zbGroup: {
        num: 20,
        ydNum: 18,
        dgNum: 20,
        dgRate: '90%'
      },
      rightTitle: '线路状态',
      busTypeZ: '拓扑',
      titleZm: '带宽',
      titleZmLt: '告警数',
      XlInfoData: [
        { id: 1, lineName: '核心专线', zxNum: 'ZX-202506', busType: '拓扑', bandWidth: '10G', lineState: 'normal' },
        { id: 2, lineName: '业务专线A', zxNum: 'ZX-202507', busType: '线路', alarmNum: 0, status: 'normal' },
        { id: 3, lineName: '业务专线B', zxNum: 'ZX-202508', busType: '线路', alarmNum: 1, status: 'alarm', 
          circuitStart: '机房A', circuitEnd: '机房B', circuitNo: 'CX-2025003' }
      ],
      alarmData: { alarmNum: 5 },
      yjData: {
        cataNum: 15,
        num: 120,
        emergElecNum: 3
      }
        };
      },
      watch: {
        
      },
      methods: {
        getSTData(type,id){
          this.$axios.get(`/protect-api/pointLine/getSTData?${type}=${id}`).then(res => {
            if (res.data.code === '0000') {
              this.sTDataInfo = res.data.data;
            }
          });
        },
        getYjwzData(type,id){
          this.$axios.get(`/protect-api/pointLine/getYjwz?${type}=${id}`).then(res => {
            if (res.data.code === '0000') {
              this.yjData = res.data.data;
            }
          });
        },
        getMessageData(){
          this.$axios.post("/protect-api/zb/getMessage", {}).then((data) => {
            if (data.data.code === '0000') {
              this.eventData = data.data.data;
            }
          });
        },
        getZbPerson(type,id){
          this.$axios.get(`/protect-api/pointLine/getZbPerson?${type}=${id}`).then(res => {
            if (res.data.code === '0000') {
              this.zbGroup = res.data.data;
            }
          });
        },
        getGjNumData(type,id){
          this.$axios.get(`/protect-api/pointLine/getGjNum?${type}=${id}`).then(res => {
            if (res.data.code === '0000') {
              this.alarmData = res.data.data;
            }
          });
        },
        getZxNumData(type,id){
          this.$axios.get(`/protect-api/pointLine/getZxNum?${type}=${id}`).then(res => {
            if (res.data.code === '0000') {
              this.XlInfoData = res.data.data;
              // console.log(res);
              
            }
          });
        },
        onEnd(evt) {
            console.log('Drag ended', evt);
          },
            tableRowClassName({row, rowIndex}) {
              if (rowIndex%2==0) {
                return 'warning-row';
              } else {
                return 'success-row';
              }
              return '';
            },
            async tableSearch() {
              let _this = this;
        /*      let queryData = { "parentId": 2, "dataType": "2" };*/
                  let queryData = {
                    "busType": _this.curAlarm
                  };
              await _this.$axios.post("/protect-api/zb/getGjList", queryData).then((data) => {
                if (data.data.code === "0000") {
                  _this.tableData = data.data.data.alarmList;
                }
              });
            },
            alarmPopoverQuit(){
              let _this = this;
              _this.alarmPopoverShow = false;
              _this.alarmPopoverObj = null;
            },
            noticePopoverQuit(){
              let _this = this;
              _this.noticePopoverShow = false;
              _this.noticePopoverObj = null;
            },
            alarmPopoverClick(param){
              let _this = this;
              _this.alarmPopoverShow = true;
              _this.tableSearch();
            },
            noticePopoverClick(param){
              let _this = this;
              _this.noticePopoverShow = true;
              _this.noticePopoverObj = param;
              _this.noticePopoverObj.mesDate = _this.formatDateTime(new Date(_this.noticePopoverObj.mesTime));
            },
            addZero(num) {
              return num < 10 ? '0' + num : num;
            },
            formatDateTime(date) {
              const time = new Date(Date.parse(date));
              time.setTime(time.setHours(time.getHours()));
              const Y = time.getFullYear() + '年';
              const M = this.addZero(time.getMonth() + 1) + '月';
              const D = this.addZero(time.getDate()) + '日';
              return Y + M + D ;
            },
            sTDataClick(param) {
              let _this = this;
              _this.map.clearOverlays();
              _this.showTopology = false;
              if (null != _this.view && undefined != _this.view) {
                _this.view.removeAllLayers();
              }
              _this.beginUrlShow = false;
              _this.urlButton = _this.beginUrl;
              if (_this.timing3) {
                clearInterval(_this.timing3);
                _this.timing3 = null;
              }
              _this.indexA = 0;
              _this.markList.map((item, index) => {
                /*        debugger*/
                let point = new BMapGL.Point(item.posLng, item.posLat);
                let myIcon = new BMapGL.Icon(item.posUrl, new BMapGL.Size(23, 33));
                let marker4 = new BMapGL.Marker(point, { icon: myIcon, "aa": item });  // 创建标注
                _this.map.addOverlay(marker4);
                _this.initTabchuangSt(item, param,marker4,index);
              });
              _this.markShowSt = true;
              _this.map.setZoom(13);
            },
            reset(param,marker4) {
              let _this = this;
              _this.curAlarm = '10';
              _this.markShowSt = false;
              _this.map.clearOverlays();
              _this.showTopology = false;
              if (null != _this.view && undefined != _this.view) {
                _this.view.removeAllLayers();
              }
              this.jobIndex = 1;
              this.beginUrlShow = false;
              this.urlButton = this.beginUrl;
              if (_this.timing3) {
                clearInterval(_this.timing3);
                _this.timing3 = null;
              }
              /*      _this.jonButton();*/
              _this.addReguaranteeLogic();  //增加重保逻辑
              _this.addReguaranteeLogicFly();  //增加重保逻辑
              _this.map.setZoom(13);
              $("#buttonSpanId1").removeClass("buttonSpan");
              $("#buttonSpanId1").addClass("buttonSpanCur");
              $("#buttonSpanId2").removeClass("buttonSpanCur");
              $("#buttonSpanId2").addClass("buttonSpan");
              $("#buttonSpanId3").removeClass("buttonSpanCur");
              $("#buttonSpanId3").addClass("buttonSpan");
              $("#buttonSpanId4").removeClass("buttonSpanCur");
              $("#buttonSpanId4").addClass("buttonSpan");
              _this.canvasCount++;
            },
            // initTabchuangSt(param, type,marker4,index) {
            //   let bottom2 = require("../assets/zxx/bottom2.png");
            //   let text1 = '<div class="stDataDiv1">';
            //   let text2 = '';
            //   let text4 = '';
            //   var label = "";
            //   if (type === "1") {
            //     if(param.fhqNum == 0){
            //       this.indexA++;
            //       return "";
            //     }else {
            //       text1 = '<div class="stDataDiv1">';
            //       text2 = '    <span class="stDataSpan1">防火墙：' + param.fhqNum + '</span>';
            //       text4 = '<div style="margin-top:13px;text-align: center;width: 128px;height: 40px;font-size: 16px;font-family: Source Han Sans CN;' +
            //           'font-weight: 500; color: #ffffff;"><span>' + param.posName + '</span></div>';
            //       let text3 = '</div><div style="position: relative;margin-top: 22px;"> <img src="' + bottom2 + ' "  alt="" style="position: absolute;left: 45%;"></div>';
            //       label = new BMapGL.Label(text1 + text2 + text4 + text3, { offset: new BMapGL.Size(-71, -108) });
            //       marker4.setLabel(label);
            //       document.getElementsByClassName("BMapLabel")[index-this.indexA].style.border = "1px #1ef1a5";
            //       document.getElementsByClassName("BMapLabel")[index-this.indexA].style.padding = "";
            //       document.getElementsByClassName("BMapLabel")[index-this.indexA].style.background = "transparent";
            //     }
            //   } else if (type === "2") {
            //     if(param.jhjNum == 0){
            //       this.indexA++;
            //       return "";
            //     }else {
            //       text1 = '<div class="stDataDiv2">';
            //       text2 = '    <span class="stDataSpan1">交换机：' + param.jhjNum + '</span>';
            //       text4 = '<div style="margin-top:13px;text-align: center;width: 128px;height: 40px;font-size: 16px;font-family: Source Han Sans CN;' +
            //           'font-weight: 500; color: #ffffff;"><span>' + param.posName + '</span></div>';
            //       let text3 = '</div><div style="position: relative;margin-top: 22px;"> <img src="' + bottom2 + ' "  alt="" style="position: absolute;left: 45%;"></div>';
            //       label = new BMapGL.Label(text1 + text2 + text4 + text3, { offset: new BMapGL.Size(-71, -108) });
            //       marker4.setLabel(label);
            //       document.getElementsByClassName("BMapLabel")[index-this.indexA].style.border = "1px #1ef1a5";
            //       document.getElementsByClassName("BMapLabel")[index-this.indexA].style.padding = "";
            //       document.getElementsByClassName("BMapLabel")[index-this.indexA].style.background = "transparent";
            //     }
            //   } else if (type === "3") {
            //     if(param.posAqNum == 0){
            //       this.indexA++;
            //       return "";
            //     }else {
            //       text1 = '<div class="stDataDiv3">';
            //       text2 = '    <span class="stDataSpan1">安全设备：' + param.posAqNum + '</span>';
            //       text4 = '<div style="margin-top:13px;text-align: center;width: 161px;height: 40px;font-size: 16px;font-family: Source Han Sans CN;' +
            //           'font-weight: 500; color: #ffffff;"><span>' + param.posName + '</span></div>';
            //       let text3 = '</div><div style="position: relative;margin-top:-2px;"> <img src="' + bottom2 + ' "  alt="" style="position: absolute;left: 45%;"></div>';
            //       label = new BMapGL.Label(text1 + text2 + text4 + text3, { offset: new BMapGL.Size(-85, -108) });
            //       marker4.setLabel(label);
            //       document.getElementsByClassName("BMapLabel")[index-this.indexA].style.border = "1px #1ef1a5";
            //       document.getElementsByClassName("BMapLabel")[index-this.indexA].style.padding = "";
            //       document.getElementsByClassName("BMapLabel")[index-this.indexA].style.background = "transparent";
            //     }
            //   } else if (type === "4") {
            //     if(param.zbNum == 0){
            //       this.indexA++;
            //       return "";
            //     }else {
            //       text1 = '<div class="stDataDiv4">';
            //       text2 = '    <span class="stDataSpan1">重保人数：' + param.zbNum + '</span>';
            //       text4 = '<div style="margin-top:13px;text-align: center;width: 148px;height: 40px;font-size: 16px;font-family: Source Han Sans CN;' +
            //           'font-weight: 500; color: #ffffff;"><span>' + param.posName + '</span></div>';
            //       let text3 = '</div><div style="position: relative;margin-top: 20px;"> <img src="' + bottom2 + ' "  alt="" style="position: absolute;left: 45%;"></div>';
            //       label = new BMapGL.Label(text1 + text2 + text4 + text3, { offset: new BMapGL.Size(-79, -108) });
            //       marker4.setLabel(label);
            //       document.getElementsByClassName("BMapLabel")[index-this.indexA].style.border = "1px #1ef1a5";
            //       document.getElementsByClassName("BMapLabel")[index-this.indexA].style.padding = "";
            //       document.getElementsByClassName("BMapLabel")[index-this.indexA].style.background = "transparent";
            //     }
            //   } else if (type === "5") {
            //     if(param.upsNum == 0){
            //       this.indexA++;
            //       return "";
            //     }else {
            //       text1 = '<div class="stDataDiv5" style="width: 220Px">';
            //       text2 = '    <span class="stDataSpan1" style="font-size: 17px;width: 220Px">备品备件数量：' + param.num + '类/' + param.cataNum + '件</span>';
            //       text4 = '<div style="margin-top:13px;text-align: center;width: 128px;height: 40px;font-size: 16px;font-family: Source Han Sans CN;' +
            //           'font-weight: 500; color: #ffffff;"><span>' + param.posName + '</span></div>';
            //       let text3 = '</div><div style="position: relative;margin-top: 22px;"> <img src="' + bottom2 + ' "  alt="" style="position: absolute;left: 45%;"></div>';
            //       label = new BMapGL.Label(text1 + text2 + text4 + text3, { offset: new BMapGL.Size(-111, -108) });
            //       marker4.setLabel(label);
            //       document.getElementsByClassName("BMapLabel")[index-this.indexA].style.border = "1px #1ef1a5";
            //       document.getElementsByClassName("BMapLabel")[index-this.indexA].style.padding = "";
            //       document.getElementsByClassName("BMapLabel")[index-this.indexA].style.background = "transparent";
            //     }
            //   } else if (type === "6") {
            //     if(param.yjclNum == 0){
            //       this.indexA++;
            //       return "";
            //     }else {
            //       text1 = '<div class="stDataDiv6" style="width: 175px;">';
            //       text2 = '    <span class="stDataSpan1" style="width: 175px;">应急发电机组：' + param.yjclNum + '</span>';
            //       text4 = '<div style="margin-top:13px;text-align: center;width: 175px;height: 40px;font-size: 16px;font-family: Source Han Sans CN;' +
            //           'font-weight: 500; color: #ffffff;"><span>' + param.posName + '</span></div>';
            //       let text3 = '</div><div style="position: relative;margin-top:-2px;"> <img src="' + bottom2 + ' "  alt="" style="position: absolute;left: 45%;"></div>';
            //       label = new BMapGL.Label(text1 + text2 + text4 + text3, { offset: new BMapGL.Size(-90, -108) });
            //       marker4.setLabel(label);
            //       document.getElementsByClassName("BMapLabel")[index-this.indexA].style.border = "1px #1ef1a5";
            //       document.getElementsByClassName("BMapLabel")[index-this.indexA].style.padding = "";
            //       document.getElementsByClassName("BMapLabel")[index-this.indexA].style.background = "transparent";
            //     }
            //   }
            // },
            abc() {
              this.addReguaranteeLogicFHQ();
            },
            changeJob() {
              if (this.timing3) {
                clearInterval(this.timing3);
                this.timing3 = null;
              }
              if (!this.beginUrlShow) {
                this.beginUrlShow = true;
                this.urlButton = this.ingUrl;
                this.jobIndex = 1;
                this.jonButton();
              } else {
                this.beginUrlShow = false;
                this.urlButton = this.beginUrl;
              }
            },
            jonButton() {
              this.arrJob = ['1', '4', '3', '2'];
              this.timing3 = setInterval(() => {
                let _this = this;
                let param = this.arrJob[_this.jobIndex];
                _this.map.clearOverlays();
                _this.showTopology = false;
                if (null != _this.view && undefined != _this.view) {
                  _this.view.removeAllLayers();
                }
                if (param == '1') {
                  $("#buttonSpanId1").removeClass("buttonSpan");
                  $("#buttonSpanId1").addClass("buttonSpanCur");
                  $("#buttonSpanId2").removeClass("buttonSpanCur");
                  $("#buttonSpanId2").addClass("buttonSpan");
                  $("#buttonSpanId3").removeClass("buttonSpanCur");
                  $("#buttonSpanId3").addClass("buttonSpan");
                  $("#buttonSpanId4").removeClass("buttonSpanCur");
                  $("#buttonSpanId4").addClass("buttonSpan");
                  this.rightTitle = "业务信息";
                  clearInterval(_this.timeA);
                  _this.timeA = null;
                  clearInterval(_this.timeB);
                  _this.timeB = null;
                  clearInterval(_this.timeC);
                  _this.timeC = null;
                  clearInterval(_this.timeD);
                  _this.timeD = null;
                  this.getBusInfo(10);
                  this.queryData.busType = 10;
                  this.getYjData(this.queryData);
                  this.getAlarmData(10);
                  _this.curAlarm = '10';
                  /*          $('.right').css({ marginTop: -875 });
                            $('.left').css({ marginTop: -875 });*/
        
                  this.map.clearOverlays();
                  _this.addReguaranteeLogicFly();
                  _this.addReguaranteeLogic();  //增加重保逻辑
                } else if (param == '2') {
                  $("#buttonSpanId1").removeClass("buttonSpanCur");
                  $("#buttonSpanId1").addClass("buttonSpan");
                  $("#buttonSpanId2").removeClass("buttonSpanCur");
                  $("#buttonSpanId2").addClass("buttonSpan");
                  $("#buttonSpanId3").removeClass("buttonSpan");
                  $("#buttonSpanId3").addClass("buttonSpanCur");
                  $("#buttonSpanId4").removeClass("buttonSpanCur");
                  $("#buttonSpanId4").addClass("buttonSpan");
                  this.rightTitle = "业务信息";
                  this.getBusInfo("拓扑");
        
                  clearInterval(_this.timeA);
                  _this.timeA = null;
                  clearInterval(_this.timeB);
                  _this.timeB = null;
                  clearInterval(_this.timeC);
                  _this.timeC = null;
                  clearInterval(_this.timeD);
                  _this.timeD = null;
                  this.queryData.busType = "拓扑";
                  this.getYjData(this.queryData);
                  this.getAlarmData('拓扑');
                  _this.curAlarm = '拓扑';
                  this.map.clearOverlays();
                  /*          $('.right').css({ marginTop: 200 });
                            $('.left').css({ marginTop: 200 });*/
                  _this.addReguaranteeTopology();  //增加重保拓扑
                } else if (param == '3') {
                  $("#buttonSpanId1").removeClass("buttonSpanCur");
                  $("#buttonSpanId1").addClass("buttonSpan");
                  $("#buttonSpanId2").removeClass("buttonSpan");
                  $("#buttonSpanId2").addClass("buttonSpanCur");
                  $("#buttonSpanId3").removeClass("buttonSpanCur");
                  $("#buttonSpanId3").addClass("buttonSpan");
                  $("#buttonSpanId4").removeClass("buttonSpanCur");
                  $("#buttonSpanId4").addClass("buttonSpan");
                  this.rightTitle = "光缆信息";
                  clearInterval(_this.timeA);
                  _this.timeA = null;
                  clearInterval(_this.timeB);
                  _this.timeB = null;
                  clearInterval(_this.timeC);
                  _this.timeC = null;
                  clearInterval(_this.timeD);
                  _this.timeD = null;
                  this.getXlInfo();
                  this.queryData.busType = "线路";
                  this.getYjData(this.queryData);
                  this.getAlarmData('线路');
                  _this.curAlarm = '线路';
                  /*         $('.right').css({ marginTop: -875 });
                           $('.left').css({ marginTop: -875 });*/
                  this.map.clearOverlays();
        
                  _this.addLineMonitoring();  //增加线路监控
                } else if (param == '4') {
                  $("#buttonSpanId1").removeClass("buttonSpanCur");
                  $("#buttonSpanId1").addClass("buttonSpan");
                  $("#buttonSpanId2").removeClass("buttonSpanCur");
                  $("#buttonSpanId2").addClass("buttonSpan");
                  $("#buttonSpanId3").removeClass("buttonSpanCur");
                  $("#buttonSpanId3").addClass("buttonSpan");
                  $("#buttonSpanId4").removeClass("buttonSpan");
                  $("#buttonSpanId4").addClass("buttonSpanCur");
                  this.rightTitle = "业务信息";
                  this.getBusInfo(5);
        
                  clearInterval(_this.timeA);
                  _this.timeA = null;
                  clearInterval(_this.timeB);
                  _this.timeB = null;
                  clearInterval(_this.timeC);
                  _this.timeC = null;
                  clearInterval(_this.timeD);
                  _this.timeD = null;
                  this.queryData.busType = 5;
                  this.getYjData(this.queryData);
                  _this.curAlarm = '5';
                  this.getAlarmData(5);
                  /*          $('.right').css({ marginTop: -875 });
                            $('.left').css({ marginTop: -875 });*/
                  this.map.clearOverlays();
        
                  _this.getAllPointLineBus4();  //增加-5
                };
                if (_this.jobIndex == 3) {
                  _this.jobIndex = 0;
                } else {
                  _this.jobIndex++;
                }
                _this.canvasCount++;
              }, 30000);
            },
            closeShowTail() {
              let _this = this;
              if (_this.showTail) {
                _this.showTail = false;
              } else {
                _this.showTail = true;
              }
            },
            // 获取光缆信息
            getXlInfo() {
              let _this = this;
              let queryData = {
        /*        "posId": "1",*/
                "busType": "线路"
              };
              this.XlInfoData = [];
              clearInterval(_this.timeA);
              _this.timeA = null;
              clearInterval(_this.timeB);
              _this.timeB = null;
              clearInterval(_this.timeC);
              _this.timeC = null;
              clearInterval(_this.timeD);
              _this.timeD = null;
              let url = '/protect-api/zb/getBusInfoCjAndXl';
              _this.busTypeZ="其它";
              _this.$axios.post(url, queryData).then((data) => {
                if (data.data.code === '0000') {
                  // console.log(data);
                  this.XlInfoData = data.data.data;
                  var itemList = document.querySelector('.item-list');
                  var scrollDis =  7.78;
                  var length = this.XlInfoData.length;
                  var index = 1;
                  const _this = this;
                  _this.timeD = setInterval(() => {
                    if (index < length + 1) {
                      itemList.style.top = -(scrollDis * index) + 'rem';
                      itemList.style.transitionDuration = '1s';
                      if (index === length) {
                        clearInterval(this.timeC);
                        this.timeC = null;
                        _this.timeC = setTimeout(() => {
                          itemList.style.top = 0;
                          itemList.style.transitionDuration = '0s';
                        }, 10);
                        index = 0;
                      }
                    }
                    ++index;
                  }, 3000);
                }
              });
            },
            // 获取线路信息
            getBusInfo(data) {
              let _this = this;
              let queryData = {
                "busType": data
              };
              this.XlInfoData = [];
              clearInterval(_this.timeA);
              _this.timeA = null;
              clearInterval(_this.timeB);
              _this.timeB = null;
              clearInterval(_this.timeC);
              _this.timeC = null;
              clearInterval(_this.timeD);
              _this.timeD = null;
              let url = '/protect-api/zb/getBusInfoCjAndXl';
              if(data=='拓扑'){
                url = '/protect-api/zb/getBusInfo';
                _this.busTypeZ="拓扑";
              }else {
                _this.busTypeZ="其它";
              }
              _this.$axios.post(url, queryData).then((data) => {
                if (data.data.code === '0000') {
                  // console.log(data);
                  this.XlInfoData = data.data.data;
                  var itemList = document.querySelector('.item-list');
                  var scrollDis = 7.78;
                  var length = this.XlInfoData.length;
                  var index = 1;
                  const _this = this;
                  _this.timeA = setInterval(() => {
                    if (index < length + 1) {
                      itemList.style.top = -(scrollDis * index) + 'rem';
                      itemList.style.transitionDuration = '1s';
                      if (index === length) {
                        clearInterval(this.timeB);
                        this.timeB = null;
                        _this.timeB = setTimeout(() => {
                          itemList.style.top = 0;
                          itemList.style.transitionDuration = '0s';
                        }, 10);
                        index = 0;
                      }
                    }
                    ++index;
                  }, 3000);
                }
              });
            },
            getEventData() {
              let _this = this;
              let queryData = {
                "posId": "1",
                "busType": "1"
              };
              _this.$axios.post("/protect-api/zb/getMessage", {}).then((data) => {
                if (data.data.code === '0000') {
                  // console.log(data);
                  this.eventData = data.data.data;
                  let itemList = document.querySelector('.item-list2');
                  let scrollDis =  7.75;
                  let length = this.eventData.length;
                  let index = 1;
                  const _this = this;
        
        
                  _this.timeZxx1 = setInterval(() => {
                    if (index < length + 1) {
                      itemList.style.top = -(scrollDis * index) + 'rem';
                      itemList.style.transitionDuration = '1s';
                      if (index === length) {
                        clearInterval(this.timeZxx2);
                        this.timeZxx2 = null;
                        _this.timeZxx2 = setTimeout(() => {
                          itemList.style.top = 0;
                          itemList.style.transitionDuration = '0s';
                        }, 10);
                        index = 0;
                      }
                    }
                    ++index;
                  }, 3000);
                }
              });
            },
            getzbGroupData() {
              let _this = this;
              let queryData = {
                "posId": "1",
                "busType": "10"
              };
              _this.$axios.post("/protect-api/zb/getZbGroup", {}).then((data) => {
                if (data.data.code === '0000') {
                  // console.log(data);
                  this.zbGroup = data.data.data;
                }
              });
            },
            getAlarmData(params) {
              let _this = this;
              let queryData = {
                "busType": params
              };
              _this.$axios.post("/protect-api/zb/getGjNum", queryData).then((data) => {
                if (data.data.code === '0000') {
                  // console.log(data);
                  this.alarmData = data.data.data;
                }
              });
            },
            getYjData(params) {
              let _this = this;
        /*      let queryData = {
                "busType": params
              };*/
              _this.$axios.post("/protect-api/zb/getYjwz", params).then((data) => {
                if (data.data.code === '0000') {
                  // console.log(data);
                  this.yjData = data.data.data;
                }
              });
            },
            // getSTData(queryData) {
            //   let _this = this;
            //   _this.$axios.post("/protect-api/zb/getSTData", queryData).then((data) => {
            //     if (data.data.code === '0000') {
            //       _this.sTDataInfo = data.data.data;
            //     }
            //   });
            // },
            reinsuranceTeam() {
              let p= {};
              p.parentId = 2030;
              this.$router.push({ path: "/home", query: {p} });
            },
            // changeScene(param) {
            //   let _this = this;
            //   _this.map.clearOverlays();
            //   _this.showTopology = false;
            //   if (null != _this.view && undefined != _this.view) {
            //     _this.view.removeAllLayers();
            //   }
            //   _this.map.removeEventListener("zoomend", function () { });
            //   if (param == '1') {
            //     $("#buttonSpanId1").removeClass("buttonSpan");
            //     $("#buttonSpanId1").addClass("buttonSpanCur");
            //     $("#buttonSpanId2").removeClass("buttonSpanCur");
            //     $("#buttonSpanId2").addClass("buttonSpan");
            //     $("#buttonSpanId3").removeClass("buttonSpanCur");
            //     $("#buttonSpanId3").addClass("buttonSpan");
            //     $("#buttonSpanId4").removeClass("buttonSpanCur");
            //     $("#buttonSpanId4").addClass("buttonSpan");
            //     this.rightTitle = "业务信息";
            //     _this.getBusInfo(10);
        
            //     clearInterval(this.timeA);
            //     this.timeA = null;
            //     clearInterval(this.timeB);
            //     this.timeB = null;
            //     clearInterval(_this.timeC);
            //     _this.timeC = null;
            //     clearInterval(_this.timeD);
            //     _this.timeD = null;
            //     /*      $('.right').css({ marginTop: -875 });
            //           $('.left').css({ marginTop: -875 });*/
            //     // this.map.clearOverlays();
            //     _this.addReguaranteeLogicFly();
        
            //     _this.addReguaranteeLogic();  //增加重保逻辑
            //     _this.curAlarm = '10';
            //     _this.getAlarmData(10);
            //     _this.queryData.busType = "10";
            //     _this.getSTData(_this.queryData);
            //     _this.getYjData(_this.queryData);
            //     _this.getzbGroupData();
            //   } else if (param == '2') {
            //     $("#buttonSpanId1").removeClass("buttonSpanCur");
            //     $("#buttonSpanId1").addClass("buttonSpan");
            //     $("#buttonSpanId2").removeClass("buttonSpanCur");
            //     $("#buttonSpanId2").addClass("buttonSpan");
            //     $("#buttonSpanId3").removeClass("buttonSpan");
            //     $("#buttonSpanId3").addClass("buttonSpanCur");
            //     $("#buttonSpanId4").removeClass("buttonSpanCur");
            //     $("#buttonSpanId4").addClass("buttonSpan");
            //     _this.rightTitle = "业务信息";
            //     _this.getBusInfo("拓扑");
        
            //     clearInterval(this.timeA);
            //     _this.timeA = null;
            //     clearInterval(this.timeB);
            //     _this.timeB = null;
            //     clearInterval(_this.timeC);
            //     _this.timeC = null;
            //     clearInterval(_this.timeD);
            //     _this.timeD = null;
            //     _this.curAlarm = '拓扑';
            //     _this.getAlarmData('拓扑');
            //     _this.queryData.busType = "拓扑";
            //     _this.getSTData(_this.queryData);
            //     _this.getYjData(_this.queryData);
            //     _this.getzbGroupData();
            //     // this.map.clearOverlays();
            //     /*        $('.right').css({ marginTop: 200 });
            //             $('.left').css({ marginTop: 200 });*/
            //     _this.addReguaranteeTopology();  //增加重保拓扑
            //   } else if (param == '3') {
            //     $("#buttonSpanId1").removeClass("buttonSpanCur");
            //     $("#buttonSpanId1").addClass("buttonSpan");
            //     $("#buttonSpanId2").removeClass("buttonSpan");
            //     $("#buttonSpanId2").addClass("buttonSpanCur");
            //     $("#buttonSpanId3").removeClass("buttonSpanCur");
            //     $("#buttonSpanId3").addClass("buttonSpan");
            //     $("#buttonSpanId4").removeClass("buttonSpanCur");
            //     $("#buttonSpanId4").addClass("buttonSpan");
            //     this.rightTitle = "光缆信息";
            //     this.getXlInfo();
        
            //     clearInterval(this.timeA);
            //     this.timeA = null;
            //     clearInterval(this.timeB);
            //     this.timeB = null;
            //     clearInterval(_this.timeC);
            //     _this.timeC = null;
            //     clearInterval(_this.timeD);
            //     _this.timeD = null;
            //     this.getYjData('线路');
            //     _this.curAlarm = '线路';
            //     this.getAlarmData('线路');
        
        
            //     /*        $('.right').css({ marginTop: -875 });
            //             $('.left').css({ marginTop: -875 });*/
            //     this.map.clearOverlays();
        
            //     _this.addLineMonitoring();  //增加线路监控
            //     // _this.getBusInfo("线路");
            //     _this.getAlarmData("线路");
            //     _this.queryData.busType = "线路";
            //     _this.getSTData(_this.queryData);
            //     _this.getYjData(_this.queryData);
            //     _this.getzbGroupData();
            //   } else if (param == '4') {
            //     $("#buttonSpanId1").removeClass("buttonSpanCur");
            //     $("#buttonSpanId1").addClass("buttonSpan");
            //     $("#buttonSpanId2").removeClass("buttonSpanCur");
            //     $("#buttonSpanId2").addClass("buttonSpan");
            //     $("#buttonSpanId3").removeClass("buttonSpanCur");
            //     $("#buttonSpanId3").addClass("buttonSpan");
            //     $("#buttonSpanId4").removeClass("buttonSpan");
            //     $("#buttonSpanId4").addClass("buttonSpanCur");
            //     this.rightTitle = "业务信息";
            //     this.getBusInfo(5);
        
            //     clearInterval(this.timeA);
            //     this.timeA = null;
            //     clearInterval(this.timeB);
            //     this.timeB = null;
            //     clearInterval(_this.timeC);
            //     _this.timeC = null;
            //     clearInterval(_this.timeD);
            //     _this.timeD = null;
            //     _this.curAlarm = '5';
            //     this.getAlarmData(5);
        
        
            //     /*        $('.right').css({ marginTop: -875 });
            //             $('.left').css({ marginTop: -875 });*/
            //     this.map.clearOverlays();
            //     _this.getAllPointLineBus4();  //增加-5
            //     _this.getYjData(5);
            //     _this.getAlarmData(5);
            //     _this.queryData.busType = "5";
            //     _this.getSTData(_this.queryData);
            //     _this.getYjData(_this.queryData);
            //     _this.getzbGroupData();
            //   }
            //   _this.canvasCount++;
            // },
            timeFn() {
              this.timing1 = setInterval(() => {
                this.dateDay = formatTime(new Date(), "HH: mm: ss");
                this.dateYear = formatTime(new Date(), "yyyy年MM月dd日");
                this.dateWeek = this.weekday[new Date().getDay()];
              }, 1000);
              this.timing2 = setInterval(() => {
                this.dateDay2 = formatTime(new Date(), "HH: mm: ss");
                this.dateYear2 = formatTime(new Date(), "yyyy/MM/dd日");
              }, 1000);
            },
            initMap() {
              // GL版命名空间为BMapGL
        
              this.map = new BMapGL.Map('allmap'); // 创建Map实例
              this.map.clearOverlays();
              this.map.centerAndZoom(new BMapGL.Point(108.953418, 34.274803), 13); // 初始化地图,设置中心点坐标和地图级别
              this.map.enableScrollWheelZoom(true); // 开启鼠标滚轮缩放
              /*     this.map.setHeading(84.5)*/
              this.map.setTilt(56.8);
              this.changeDarkStyle();
              this.addReguaranteeLogic();  //增加重保逻辑
              this.addReguaranteeLogicFly();  //增加重保逻辑
        
              /*      this.addReguaranteeTopology();  //增加重保拓扑
                    this.addLineMonitoring();  //增加线路监控*/
            },
            // 地图线路监控功能
addLineMonitoring() {
  const _this = this;
  
  // 获取场景下的所有线路数据
  this.$axios.get(`/protect-api/pointLine/getAllLineStrBySceneId/${this.sceneList.id}`)
    .then(response => {
      const data = response.data;
      if (data.code === "0000") {
        // 保存线路和站点数据
        _this.reguaranteeTopologyList = data.data.zbLineStrList;
        _this.reguaranteeTopologyList2 = data.data.zbPos;
        
        // 添加站点标记
        _this.addStationMarkers();
        
        // 处理线路数据并添加到地图
        _this.processLineData(_this.reguaranteeTopologyList);

        // 初始化右键菜单
        _this.initContextMenu();
      } else {
        // console.error('获取线路数据失败:', data.msg);
        // _this.$message.error('获取线路数据失败');
      }
    })
    .catch(error => {
      // console.error('请求出错:', error);
      // _this.$message.error('网络请求错误');
    });
},

// 添加站点标记
addStationMarkers() {
  const _this = this;
  
  _this.reguaranteeTopologyList2.forEach((station, index) => {
    // 创建站点坐标点
    const point = new BMapGL.Point(station.posLng, station.posLat);
    
    // 创建自定义图标
    const myIcon = new BMapGL.Icon(station.posUrl || "http://10.93.15.92/images/zb/zhd.png", new BMapGL.Size(23, 33));
    
    // 创建标注并添加到地图
    const marker = new BMapGL.Marker(point, { icon: myIcon });
    _this.map.addOverlay(marker);
    
    // 设置标注标签
    const label = _this.createStationLabel(station);
    marker.setLabel(label);
    
    // 自定义标签样式
    setTimeout(() => {
      const labels = document.querySelectorAll(".BMapLabel");
      if (labels[index]) {
        labels[index].style.border = "1px #1ef1a5";
        labels[index].style.padding = "";
        labels[index].style.background = "transparent";
      }
    }, 0);
  });
},

// 创建站点标签
createStationLabel(station) {
  const text = `<div style="width: 100px;height: 18px;font-size: 12px;text-align:center;font-family: Source Han Sans CN;font-weight: 400;
               text-shadow: 0Px 2Px 8Px rgb(14 197 236 / 36%);color: #ffffff;line-height: 22px;">${station.alias}</div>`;
  
  return new BMapGL.Label(text, { offset: new BMapGL.Size(-51, -40) });
},

// 处理线路数据并添加到地图
processLineData(lines) {
  const _this = this;
  
  // 初始化视图
  _this.view = new mapvgl.View({
    map: _this.map
  });
  
  // 准备不同类型的线路数据
  const baseLines = [];     // 基础线路（半透明背景）
  const normalLines = [];   // 正常运行线路（带流动动画）
  const alarmPoints = [];   // 告警点数据
  
  // 分类处理线路数据
  lines.forEach(line => {
    console.log('line',line.lineColor);
    const lineArrList = line.lineArr;
    line.lineBold = '10px';
    
    // 添加基础线路
    // baseLines.push({ 
    //   "geometry": { "type": "LineString", "coordinates": lineArrList }, 
    //   "color": line.lineColor + "2e", 
    //   "width": JSON.stringify(parseFloat(line.lineBold) * 1.1) 
    // });

    // 创建并添加线段
  let polylineArr = [];
  let color = ["#dfde3a", "#dfde3a", "#dfde3a"];
   let border = 5;
   if (line.lineArr) {
    const points = line.lineArr;
    points.forEach(pointStr => {
      const [lng, lat] = pointStr;
      polylineArr.push(new BMapGL.Point(parseFloat(lng), parseFloat(lat)));
    });
  }
  let polyline = new BMapGL.Polyline(polylineArr, {
    strokeColor: line.lineColor,
    strokeWeight: border,
    strokeOpacity: 0.5
  });
  polyline.lineData = line;
  _this.map.addOverlay(polyline);
  // 为线段添加右键事件（编辑模式下）
  if (this.editMode && this.setUpType === '线路') {
    polyline.addEventListener('rightclick', function (e) {
      _this.selectedPolyline = this;
      _this.handlePolylineRightClick(e);
    });
  }
  
    
    // 根据线路状态处理
    if (line.lineState !== "alarm") {
      // 正常线路
      normalLines.push({ 
        "geometry": { "type": "LineString", "coordinates": lineArrList }, 
        "color": line.lineColor, 
        "width": '3' 
      });
    } else {
      // 告警线路，在线路中间位置添加告警标记
      const middleIndex = Math.round(lineArrList.length / 2);
      const middlePoint = new BMapGL.Point(lineArrList[middleIndex][0], lineArrList[middleIndex][1]);
      
      // 添加告警图标
      const alarmIcon = new BMapGL.Icon(require("../assets/zxx/quit.png"), new BMapGL.Size(16, 16));
      const marker = new BMapGL.Marker(middlePoint, { icon: alarmIcon, "aa": line });
      _this.map.addOverlay(marker);
      
      // 添加告警点数据
      alarmPoints.push({ 
        "geometry": { "type": "Point", "coordinates": lineArrList[middleIndex] }, 
        "properties": { "height": 10 } 
      });
    }
  });
  
  // 添加告警点涟漪效果图层
  const rippleLayer = new mapvgl.RippleLayer({
    size: 3500,
    unit: 'm',
    renderOrder: 2,
    color: 'rgb(218,61,40)',
    enablePicked: false,
    onClick: (e) => {
      // 点击事件处理
    },
  });
  _this.view.addLayer(rippleLayer);
  rippleLayer.setData(alarmPoints);
  
  // 添加基础线路图层
  const baseLineLayer = new mapvgl.LineLayer({
    width: 5,
    style: 'road',
    enablePicked: true,
    onClick: e => {
      // 点击事件处理
    }
  });
  _this.view.addLayer(baseLineLayer);
  baseLineLayer.setData(baseLines);
  
  // 添加带动画效果的正常线路图层
  const animatedLineLayer = new mapvgl.LineLayer({
    animation: true,
    duration: 2.5,
    trailLength: 0.6,
    interval: 0.2
  });
  _this.view.addLayer(animatedLineLayer);
  animatedLineLayer.setData(normalLines);

},
            addReguaranteeTopology() {
              let _this = this;
              _this.showTopology = true;
            },
            addReguaranteeLogic() {
              let _this = this;
              let queryData = { "busType": '1' };
              _this.$axios.post("/protect-api/pointLine/getAllPointLineByBus", queryData).then((data) => {
                if (data.data.code === "0000") {
                  _this.reguaranteeLogicList = data.data.data.zbPosList;
                  _this.reguaranteeLogicList2 = data.data.data.zbLines;
                  _this.markList = _this.reguaranteeLogicList;
                  var data = [];
                  let aaaaa = {};
                  var randomCount = 2; // 模拟的飞线的数量
                  var curve = new mapvgl.BezierCurve();
                  _this.reguaranteeLogicList.map((item, index) => {
                    let polylineArr1 = [];
                    let polylineArr2 = [];
                    let polylineArr3 = [];
                    polylineArr1.push(new BMapGL.Point(_this.reguaranteeLogicList[0].posLng, _this.reguaranteeLogicList[0].posLat));
                    let point = new BMapGL.Point(item.posLng, item.posLat);
                    let myIcon = new BMapGL.Icon(item.posUrl, new BMapGL.Size(23, 33));
                    let marker2 = new BMapGL.Marker(point, { icon: myIcon, "aa": item });  // 创建标注
                    if ('1' != item.posType) {
                      polylineArr1.push(new BMapGL.Point(item.posLng, item.posLat));
                      marker2.customData = item;
                      let color = ["#dfde3a", "#dfde3a", "#dfde3a"];
                      let border = "2px";
                      if (null != item.posId) {
                        let colorArr = _this.reguaranteeLogicList2.filter(cc => cc.posIdTarget == item.posId);
                        if (undefined != colorArr[0]) {
                          /*       color = colorArr[0].lineColor;*/
                          border = colorArr[0].lineBold;
                        }
                        if (undefined != colorArr && colorArr.length > 0) {
                          color = colorArr[0].lineColor.split(",");
                        }
                        //告警点打!
                        if (undefined != colorArr[0] && colorArr[0].lineType === "event") {  //event
                          let ppp = colorArr[0].lineStr.split(";");
                          let ll = ppp[0].split(",");
                          let ll2 = ppp[1].split(",");
                          let pointArr = [new BMapGL.Point(Number(ll[0]), Number(ll[1])), new BMapGL.Point(Number(ll2[0]), Number(ll2[1]))];
                          let middenP = _this.getMidpoiont(pointArr);
                          let urlP = require("../assets/zxx/warning.png");
                          let myIcon = new BMapGL.Icon(urlP, new BMapGL.Size(25, 25));
                          let marker3 = new BMapGL.Marker(middenP, { icon: myIcon, "aa": item });  // 创建标注
                          _this.map.addOverlay(marker3);
                        }
                        //告警点打X
                        if (undefined != colorArr[0] && colorArr[0].lineType === "alarm") {
                          let ppp = colorArr[0].lineStr.split(";");
                          let ll = ppp[0].split(",");
                          let ll2 = ppp[1].split(",");
                          let pointArr = [new BMapGL.Point(Number(ll[0]), Number(ll[1])), new BMapGL.Point(Number(ll2[0]), Number(ll2[1]))];
                          let middenP = _this.getMidpoiont(pointArr);
                          let urlP = require("../assets/zxx/quit.png");
                          let myIcon = new BMapGL.Icon(urlP, new BMapGL.Size(16, 16));
                          let marker3 = new BMapGL.Marker(middenP, { icon: myIcon, "aa": item });  // 创建标注
                          _this.map.addOverlay(marker3);
                        }
                      }
                      let polyline1 = new BMapGL.Polyline(polylineArr1, { strokeColor: color[0], strokeWeight: border[0], strokeOpacity: 0.9 });   //创建折线
                      if (item.posName === "联通省公司" || item.posName === "联通西安分公司") {
                      }else {
                        _this.map.addOverlay(polyline1);
                      }
                      /*              let polyline2 = new BMapGL.Polyline(polylineArr2, { strokeColor: color[1], strokeWeight: border[0], strokeOpacity: 0.9 });   //创建折线
                                    _this.map.addOverlay(polyline2);
                                    let polyline3 = new BMapGL.Polyline(polylineArr3, { strokeColor: color[2], strokeWeight: border[0], strokeOpacity: 0.9 });   //创建折线
                                    _this.map.addOverlay(polyline3);*/
                      _this.map.addOverlay(marker2);
                      marker2.customData = item;
                    }
                    if (index === 0) {
                      _this.map.addOverlay(marker2);
                      marker2.setAnimation(BMAP_ANIMATION_BOUNCE);
                    }
                    _this.marker2 = marker2;
                    marker2.customData = item;
                    let lableText = _this.initTabchuang(item);
                    marker2.setLabel(lableText);
                    _this.clickInfomation = true;
                    marker2.addEventListener("mouseover", async function (param) {
                      let list2Length1 = _this.reguaranteeLogicList.length;
                      if (_this.clickInfomation && undefined == document.getElementsByClassName("BMapLabel")[list2Length1]) {
                        let lableText2 = await _this.tabchuang(item);
                        marker2.setLabel(lableText2);
                        document.getElementsByClassName("BMapLabel")[list2Length1].style.border = "1px #1ef1a5";
                        document.getElementsByClassName("BMapLabel")[list2Length1].style.padding = "";
                        _this.queryData = { "busType": '10', "posId": param.target.customData.posId };
                        _this.getSTData(_this.queryData);
                        _this.getYjData(_this.queryData);
                        $("#openZhongbaoTeam").val(param.target.customData);
                        document.getElementById("openZhongbaoTeam").onclick = function (e) {
                          let jj = e.target.value;
                          let p = {};
                          if(e.target.value.posName==="联通省公司"){
                            p.parentId = "2";
                          } else if (param.posName === "联通西安分公司") {
                            p.parentId = "3";
                          }else {
                            p.parentId = "4";
                          }
                          if (undefined != jj && null != jj) {
                            _this.$router.push({ path: "/home", query: { p } });
                          }
                        };
                        document.getElementById("informationExplains").onmouseover = function (e) {
                          $("#informationExplainsDetails").show();
                        };
                        document.getElementById("informationExplains").onmouseout = function (e) {
                          $("#informationExplainsDetails").hide();
                        };
                      }
                    });
                    marker2.addEventListener("mouseout", function () {
                          if (_this.clickInfomation) {
                            _this.queryData.busType = "10";
                            _this.queryData.posId ="";
                            _this.getSTData(_this.queryData);
                            _this.getYjData(_this.queryData);
                            let list2Length2 = _this.reguaranteeLogicList.length;
                            if (undefined != document.getElementsByClassName("BMapLabel")[list2Length2]) {
                              document.getElementsByClassName("BMapLabel")[list2Length2].remove();
                            }
                          }
                        }
                    );
                    marker2.addEventListener("click", function () {
                      if (_this.clickInfomation) {
                        marker2.removeEventListener("mouseout", function () { });
                        _this.clickInfomation = false;
                      } else {
                        marker2.addEventListener("mouseout", function () {
                          _this.queryData.busType = "10";
                          _this.queryData.posId ="";
                          _this.getSTData(_this.queryData);
                          _this.getYjData(_this.queryData);
                          let list2Length2 = _this.reguaranteeLogicList.length;
                          if (undefined != document.getElementsByClassName("BMapLabel")[list2Length2]) {
                            document.getElementsByClassName("BMapLabel")[list2Length2].remove();
                          }
                        });
                        _this.clickInfomation = true;
                      }
                    });
                  });
                  setTimeout(() => {
                    for (let i = 0; i < _this.reguaranteeLogicList.length; i++) {
                      if (undefined != document.getElementsByClassName("BMapLabel")[i]) {
                        document.getElementsByClassName("BMapLabel")[i].style.border = "1px dashed #1ef1a5";
                        document.getElementsByClassName("BMapLabel")[i].style.padding = "";
                      }
                    }
                    $(".BMapLabel").hide();
                  }, 250);
                  _this.map.addEventListener("zoomend", function (e) {
                    if (_this.markShowSt) { return false; };
                    let zoom = _this.map.getZoom(); // 获取缩放级别
                    if (zoom < 14) {
                      for (let i = 0; i < _this.reguaranteeLogicList.length; i++) {
                        if (undefined != document.getElementsByClassName("BMapLabel")[i]) {
                          document.getElementsByClassName("BMapLabel")[i].style.display = "none";
                        }
                      }
                    } else {
                      for (let i = 0; i < _this.reguaranteeLogicList.length; i++) {
                        if (undefined != document.getElementsByClassName("BMapLabel")[i]) {
                          document.getElementsByClassName("BMapLabel")[i].style.display = "";
                        }
                      }
                    }
                  });
                }
              });
            },
            //获取两点之间中间点
            getMidpoiont(pointArr) {
              const lngca =
                  (Math.max(parseFloat(pointArr[0].lng), parseFloat(pointArr[1].lng)) -
                      Math.min(parseFloat(pointArr[0].lng), parseFloat(pointArr[1].lng))) /
                  2;
              const latca =
                  (Math.max(parseFloat(pointArr[0].lat), parseFloat(pointArr[1].lat)) -
                      Math.min(parseFloat(pointArr[0].lat), parseFloat(pointArr[1].lat))) /
                  2;
              const lngcenter = Math.min(parseFloat(pointArr[0].lng), parseFloat(pointArr[1].lng)) + lngca;
              const latcenter = Math.min(parseFloat(pointArr[0].lat), parseFloat(pointArr[1].lat)) + latca;
              const pointcenter = new BMapGL.Point(lngcenter, latcenter);
              return pointcenter;
            },
            addReguaranteeLogicFHQ() {
              let _this = this;
              let queryData = { "busType": '1' };
              _this.$axios.post("/protect-api/pointLine/getAllPointLineByBus", queryData).then((data) => {
                if (data.data.code === "0000") {
                  _this.reguaranteeLogicList = data.data.data.zbPosList;
                  _this.reguaranteeLogicList2 = data.data.data.zbLines;
                  var data = [];
                  let aaaaa = {};
                  var randomCount = 2; // 模拟的飞线的数量
                  var curve = new mapvgl.BezierCurve();
                  _this.reguaranteeLogicList.map((item, index) => {
                    let polylineArr1 = [];
                    let polylineArr2 = [];
                    let polylineArr3 = [];
                    polylineArr1.push(new BMapGL.Point(_this.reguaranteeLogicList[0].posLng, _this.reguaranteeLogicList[0].posLat));
                    polylineArr2.push(new BMapGL.Point(_this.reguaranteeLogicList[0].posLng, _this.reguaranteeLogicList[0].posLat - 0.0003));
                    polylineArr3.push(new BMapGL.Point(_this.reguaranteeLogicList[0].posLng, _this.reguaranteeLogicList[0].posLat - 0.0006));
                    let point = new BMapGL.Point(item.posLng, item.posLat);
                    let myIcon = new BMapGL.Icon(item.posUrl, new BMapGL.Size(23, 33));
                    let marker2 = new BMapGL.Marker(point, { icon: myIcon, "aa": item });  // 创建标注
                    if ('1' != item.posType) {
                      polylineArr1.push(new BMapGL.Point(item.posLng, item.posLat));
                      polylineArr2.push(new BMapGL.Point(item.posLng, item.posLat - 0.0003));
                      polylineArr3.push(new BMapGL.Point(item.posLng, item.posLat - 0.0006));
                      marker2.customData = item;
                      let color = ["#dfde3a", "#dfde3a", "#dfde3a"];
                      let border = "2px";
                      if (null != item.posId) {
                        let colorArr = _this.reguaranteeLogicList2.filter(cc => cc.posIdTarget == item.posId);
                        if (undefined != colorArr[0]) {
                          /*       color = colorArr[0].lineColor;*/
                          border = colorArr[0].lineBold;
                        }
                        if (undefined != colorArr && colorArr.length > 0) {
                          color = colorArr[0].lineColor.split(",");
                        }
                      }
                      let polyline1 = new BMapGL.Polyline(polylineArr1, { strokeColor: color[0], strokeWeight: border[0], strokeOpacity: 0.9 });   //创建折线
                      _this.map.addOverlay(polyline1);
                      let polyline2 = new BMapGL.Polyline(polylineArr2, { strokeColor: color[1], strokeWeight: border[0], strokeOpacity: 0.9 });   //创建折线
                      _this.map.addOverlay(polyline2);
                      let polyline3 = new BMapGL.Polyline(polylineArr3, { strokeColor: color[2], strokeWeight: border[0], strokeOpacity: 0.9 });   //创建折线
                      _this.map.addOverlay(polyline3);
                      _this.map.addOverlay(marker2);
                      marker2.customData = item;
                    }
                    if (index === 0) {
                      _this.map.addOverlay(marker2);
                      marker2.setAnimation(BMAP_ANIMATION_BOUNCE);
                    }
                    marker2.customData = item;
                    let lableText = _this.initTabchuang1(item);
                    marker2.setLabel(lableText);
                    _this.clickInfomation = true;
        
                    marker2.addEventListener("mouseout", function () {
                          if (_this.clickInfomation) {
                            let list2Length2 = _this.reguaranteeLogicList.length;
                            if (undefined != document.getElementsByClassName("BMapLabel")[list2Length2]) {
                              document.getElementsByClassName("BMapLabel")[list2Length2].remove();
                            }
                          }
                        }
                    );
                    marker2.addEventListener("click", function () {
                      if (_this.clickInfomation) {
                        marker2.removeEventListener("mouseout", function () { });
                        _this.clickInfomation = false;
                      } else {
                        marker2.addEventListener("mouseout", function () {
                          let list2Length2 = _this.reguaranteeLogicList.length;
                          if (undefined != document.getElementsByClassName("BMapLabel")[list2Length2]) {
                            document.getElementsByClassName("BMapLabel")[list2Length2].remove();
                          }
                        });
                        _this.clickInfomation = true;
                      }
                    });
                  });
        
                }
              });
            },
        
            addReguaranteeLogicFly() {
              let _this = this;
              let queryData = { "busType": '1' };
              _this.$axios.post("/protect-api/pointLine/getAllPointLineByBus", queryData).then((data) => {
                if (data.data.code === "0000") {
                  _this.reguaranteeLogicList = data.data.data.zbPosList;
                  _this.reguaranteeLogicList2 = data.data.data.zbLines;
                  var data = [];
                  let aaaaa = {};
                  let color = "#dfde3a";
                  let border = "3px";
                  var randomCount = 2; // 模拟的飞线的数量
                  let a = [];
                  let b = [];
        
                  let c = [];
                  let d = [];
        
                  let all = [];
                  var curve = new mapvgl.BezierCurve();
                  // let polylineArrFly = [];
                  // for (let index = 0; index < _this.reguaranteeLogicList.length; index++) {
                  //   if (_this.reguaranteeLogicList[index].posLevel == "3") {
                  //   }
                  // }
                  _this.reguaranteeLogicList.map((item, index) => {
                    // let polylineArr = [];
                    // console.log(item);
                    if ('3' == item.posLevel) {
                      // polylineArr.push(new BMapGL.Point(_this.reguaranteeLogicList[0].posLng, _this.reguaranteeLogicList[0].posLat));
                      _this.polylineArrFly.push(item.posLng, item.posLat);
        
                      if (null != item.posId) {
                        let colorArr = _this.reguaranteeLogicList2.filter(cc => cc.posIdTarget == item.posId);
                        if (undefined != colorArr[0]) {
                          color = colorArr[0].lineColor;
                          border = colorArr[0].lineBold;
                        }
                      }
                    }
                  });
                  // console.log(_this.polylineArrFly);
                  if (_this.polylineArrFly.length == 1) {
                    a.push(_this.polylineArrFly[0], _this.polylineArrFly[1]);
                    all.push(a);
        
                  } else if (_this.polylineArrFly.length == 3) {
                    a.push(_this.polylineArrFly[0], _this.polylineArrFly[1]);
                    b.push(_this.polylineArrFly[2], _this.polylineArrFly[3]);
                    all.push(a, b);
        
                  } if (_this.polylineArrFly.length == 5) {
                    a.push(_this.polylineArrFly[0], _this.polylineArrFly[1]);
                    b.push(_this.polylineArrFly[2], _this.polylineArrFly[3]);
                    c.push(_this.polylineArrFly[4], _this.polylineArrFly[5]);
                    all.push(a, b, c);
        
        
                  } if (_this.polylineArrFly.length == 7) {
                    a.push(_this.polylineArrFly[0], _this.polylineArrFly[1]);
                    b.push(_this.polylineArrFly[2], _this.polylineArrFly[3]);
                    c.push(_this.polylineArrFly[4], _this.polylineArrFly[5]);
                    d.push(_this.polylineArrFly[6], _this.polylineArrFly[7]);
        
        
        
                  } if (_this.polylineArrFly.length == 8) {
                    a.push(_this.polylineArrFly[0], _this.polylineArrFly[1]);
                    b.push(_this.polylineArrFly[2], _this.polylineArrFly[3]);
                    c.push(_this.polylineArrFly[4], _this.polylineArrFly[5]);
                    d.push(_this.polylineArrFly[6], _this.polylineArrFly[7]);
        
        
        
                  }
                  a.push(_this.polylineArrFly[0], _this.polylineArrFly[1]);
                  b.push(_this.polylineArrFly[2], _this.polylineArrFly[3]);
                  c.push(_this.polylineArrFly[4], _this.polylineArrFly[5]);
                  // d.push(_this.polylineArrFly[6], _this.polylineArrFly[7]);
                  all.push(a, b, c);
        
                  let arrAll = [];
                  let objAll = {};
                  for (let index = 0; index < all.length; index++) {
                    objAll.lng = all[index][0];
                    objAll.lat = all[index][1];
                    arrAll.push(objAll);
        
        
                    // console.log(all[0]);
                    // aaaaa.lng = Number(all);
                    // aaaaa.lat = Number(all);
                    // console.log(all[index][0]);
                    curve.setOptions({
                      start: [_this.reguaranteeLogicList[0].posLng, _this.reguaranteeLogicList[0].posLat],
                      end: [objAll.lng, objAll.lat]
                    });
                    var curveModelData = curve.getPoints();
        
                    data.push({
                      geometry: {
                        type: 'LineString',
                        coordinates: curveModelData
                      },
                      properties: {
                        count: 1
                      }
                    });
        
                  }
        
                  this.view = new mapvgl.View({
                    map: this.map
                  });
                  var flylineLayer = new mapvgl.FlyLineLayer({
                    style: 'chaos',
                    step: 0.1,
                    color: "#4DCAD4",
                    textureColor: function (data) {
                      return data.properties.count > 0.9 ? "#4DCAD4" : "#4DCAD4";
                    },
                    textureWidth: 20,
                    textureLength: 100
                  });
                  // // this.map.removeOverlay(this.map.getOverlays()[4]);
        
                  this.view.addLayer(flylineLayer);
                  flylineLayer.setData(data);
                }
              });
            },
            getAllPointLineBus4() {
              let _this = this;
              let queryData = {};
              this.$axios.post("/protect-api/pointLine/getAllPointLineBus4", queryData).then((data) => {
                if (data.data.code === "0000") {
                  _this.reguaranteeTopologyList = data.data.data.zbLineList;
                  _this.reguaranteeTopologyList2 = data.data.data.zbPosList;
                  _this.markList = _this.reguaranteeTopologyList2;
                  _this.reguaranteeTopologyList.map(item => {
                    let lineStrBDList = item.lineStr.split(";");
                    let polylineArr = [];
                    lineStrBDList.map(itemNext => {
                      let point = new BMapGL.Point(itemNext.split(',')[0], itemNext.split(',')[1]);
                      polylineArr.push(point);
                    });
                    let polyline = new BMapGL.Polyline(polylineArr, { strokeColor: item.lineColor, strokeWeight: item.lineBold[0], strokeOpacity: 0.9 });   //创建折线
                    _this.map.addOverlay(polyline);
                    //告警点打!
                    if (undefined != item && item.lineType === "event") {
                      let ppp = item.lineStr.split(";");
                      let ll = ppp[0].split(",");
                      let ll2 = ppp[1].split(",");
                      let pointArr = [new BMapGL.Point(Number(ll[0]), Number(ll[1])), new BMapGL.Point(Number(ll2[0]), Number(ll2[1]))];
                      let middenP = _this.getMidpoiont(pointArr);
                      let urlP = require("../assets/zxx/warning.png");
                      let myIcon = new BMapGL.Icon(urlP, new BMapGL.Size(25, 25));
                      let marker3 = new BMapGL.Marker(middenP, { icon: myIcon, "aa": item });  // 创建标注
                      _this.map.addOverlay(marker3);
                    }
                    //告警点打X
                    if (undefined != item && item.lineType === "alarm") {
                      let ppp = item.lineStr.split(";");
                      let ll = ppp[0].split(",");
                      let ll2 = ppp[1].split(",");
                      let pointArr = [new BMapGL.Point(Number(ll[0]), Number(ll[1])), new BMapGL.Point(Number(ll2[0]), Number(ll2[1]))];
                      let middenP = _this.getMidpoiont(pointArr);
                      let urlP = require("../assets/zxx/quit.png");
                      let myIcon = new BMapGL.Icon(urlP, new BMapGL.Size(16, 16));
                      let marker3 = new BMapGL.Marker(middenP, { icon: myIcon, "aa": item });  // 创建标注
                      _this.map.addOverlay(marker3);
                    }
                  });
                  _this.reguaranteeTopologyList2.map((item, index) => {
                    let point = new BMapGL.Point(item.posLng, item.posLat);
                    let myIcon = new BMapGL.Icon(item.posUrl, new BMapGL.Size(23, 33));
                    let marker2 = new BMapGL.Marker(point, { icon: myIcon });  // 创建标注
                    _this.map.addOverlay(marker2);
                    let lableText = _this.initTabchuang(item);
                    marker2.setLabel(lableText);
                    document.getElementsByClassName("  BMapLabel")[index].style.border = "1px dashed #1ef1a5";
                    document.getElementsByClassName("  BMapLabel")[index].style.padding = "";
                    marker2.customData = item;
                    _this.clickInfomation = true;
                    marker2.addEventListener("mouseover", async function (param) {
                      let list2Length1 = _this.reguaranteeTopologyList2.length;
                      if (_this.clickInfomation && undefined == document.getElementsByClassName("BMapLabel")[list2Length1]) {
                        let lableText2 = await _this.tabchuang(item);
                        marker2.setLabel(lableText2);
                        document.getElementsByClassName("BMapLabel")[list2Length1].style.border = "1px #1ef1a5";
                        document.getElementsByClassName("BMapLabel")[list2Length1].style.padding = "";
                        _this.queryData = { "busType": '5', "posId": param.target.customData.posId };
                        _this.getSTData(_this.queryData);
                        _this.getYjData(_this.queryData);
                        $("#openZhongbaoTeam").val(param.target.customData);
                        document.getElementById("openZhongbaoTeam").onclick = function (e) {
                          let jj = e.target.value;
                          let p = {};
                          p.parentId = "4";
                          if (undefined != jj && null != jj) {
                            _this.$router.push({ path: "/home", query: { p } });
                          }
                        };
                        document.getElementById("informationExplains").onmouseover = function (e) {
                          $("#informationExplainsDetails").show();
                        };
                        document.getElementById("informationExplains").onmouseout = function (e) {
                          $("#informationExplainsDetails").hide();
                        };
                      }
                    });
                    marker2.addEventListener("mouseout", function () {
                      if (_this.clickInfomation) {
                        _this.queryData.busType = "5";
                        _this.queryData.posId ="";
                        _this.getSTData(_this.queryData);
                        _this.getYjData(_this.queryData);
                        let list2Length2 = _this.reguaranteeTopologyList2.length;
                        if (undefined != document.getElementsByClassName("BMapLabel")[list2Length2]) {
                          document.getElementsByClassName("BMapLabel")[list2Length2].remove();
                        }
                      }
                    });
                    marker2.addEventListener("click", function () {
                      if (_this.clickInfomation) {
                        marker2.removeEventListener("mouseout", function () { });
                        _this.clickInfomation = false;
                      } else {
                        marker2.addEventListener("mouseout", function () {
                          _this.queryData.busType = "5";
                          _this.queryData.posId ="";
                          _this.getSTData(_this.queryData);
                          _this.getYjData(_this.queryData);
                          let list2Length2 = _this.reguaranteeTopologyList2.length;
                          if (undefined != document.getElementsByClassName("BMapLabel")[list2Length2]) {
                            document.getElementsByClassName("BMapLabel")[list2Length2].remove();
                          }
                        });
                        _this.clickInfomation = true;
                      }
                    });
                  });
                  _this.map.addEventListener("zoomend", function (e) {
                    if (_this.markShowSt) { return false; };
                    let zoom = _this.map.getZoom(); // 获取缩放级别
                    if (zoom < 14) {
                      for (let i = 0; i < _this.reguaranteeTopologyList2.length; i++) {
                        if (undefined != document.getElementsByClassName("BMapLabel")[i]) {
                          document.getElementsByClassName("BMapLabel")[i].style.display = "none";
                        }
                      }
                    } else {
                      for (let i = 0; i < _this.reguaranteeLogicList.length; i++) {
                        if (undefined != document.getElementsByClassName("BMapLabel")[i]) {
                          document.getElementsByClassName("BMapLabel")[i].style.display = "";
                        }
                      }
                    }
                  });
                  $(".BMapLabel").hide();
                }
              });
            },
            initTabchuang(param) {
              let url = param.posRealUrl;
              let bottom2 = require("../assets/zxx/bottom2.png");
              let locationTab = require("../assets/zxx/locationTab.png");
              let text = '<div style="z-index: 1; width:170Px;height: 112Px;background-image: url(' + locationTab + ') ;background-repeat: no-repeat;background-size: 100% 100%;">' +
                  '<div style="z-index: 1;display: flex;' +
                  'flex-direction: row;align-content: center;justify-content: space-between; align-items: center;">' +
                  '            <img src="' + url + ' " id="imgDemo" alt="" style="height:90px;width:100%">' +
                  '    </div>' +
                  '<div style="width: 100%;height: 18px;font-size: 14px;text-align:center;font-family: Source Han Sans CN;font-weight: 400;color: #FFFFFF;line-height: 22px;">' + param.posName + '</div>' +
                  '</div>' +
                  '<div style="position: relative;"> <img src="' + bottom2 + ' "  alt="" style="position: absolute;left: 45%;"></div>';
              var label = new BMapGL.Label(text, { offset: new BMapGL.Size(-91, -150) });
              return label;
            },
        
            initTabchuang1(param) {
              let url = param.posRealUrl;
              let bottom2 = require("../assets/zxx/bottom2.png");
              let locationTab = require("../assets/zxx/locationTab.png");
              let text = '<h1 style="width: 100%;height: 18px;font-size: 14px;text-align:center;font-family: Source Han Sans CN;font-weight: 400;color: #000000;line-height: 22px;">111111</h1>';
              var label = new BMapGL.Label(text, { offset: new BMapGL.Size(-91, -150) });
              return label;
            },
            async tabchuang(param) {
              let _this = this;
              let bottom = require("../assets/zxx/bottom.png");
              let explains = require("../assets/zxx/explains.png");
              let query = {};
              query.dataType = "1";
              if (param.posLevel == 'X') {
                if (param.posName === "联通省公司") {
                  query.userGroupType = "GROUP_SF";
                  query.posId = "";
                } else if (param.posName === "联通西安分公司") {
                  query.userGroupType = "GROUP_XF";
                  query.posId = "";
                }
              } else {
                query.posId = param.posId;
                query.userGroupType = "GROUP_XC";
              }
              /*      let query={};*/
              await _this.$axios.post("/protect-api/person/getAllPersonList", query).then((data) => {
                if (data.data.code === "0000") {
                  _this.zbPosiNFO = data.data.data;
                  /*          param.target.customInfo = _this.zbPosiNFO;*/
                } else {
                  _this.zbPosiNFO = null;
                }
              });
              let text1 = '<div id="information" style="z-index: 1003;width: 365Px;height: 265Px;border: 1Px solid;border-image: linear-gradient(0deg, #4AB38C, #105745) 10 10;background: linear-gradient(0deg, #0E2D28 0%, rgba(7,70,83,0.8) 100%);">' +
                  '    <div>' +
                  '        <div style="z-index:3;width: 365Px;height: 39Px;background: linear-gradient(90deg, #25BFAB 0%, rgba(25,146,156,0.5) 100%);opacity: 0.3;">' +
                  '        </div>' +
                  '        <span style="z-index:3;margin-top:-38px;margin-left:10px;position:fixed;width: 78Px;opacity: 1;height: 18Px;font-size: 18Px;font-family: Source Han Sans CN;' +
                  '        font-weight: 500;color: #FFFFFF;line-height: 38Px;text-shadow: 0Px 2Px 8Px rgba(5,28,55,0.42);">重保团队</span>' +
                  '        <span id="informationExplains" style="position: fixed;margin-top: -33px;margin-left: 85px;"><img src="' + explains + '" style="width: 16px;height:16px;"></span>' +
                  '        <span id="informationExplainsDetails" style="position: fixed;z-index:4;margin-top: -70px;margin-left: 80px;' +
                  'background: #07607c;position: absolute;border-radius: 4px;padding: 10px;font-size: 12px;line-height: 1.2;min-width: 10px;word-wrap: break-word;' +
                  ' color: #FFF;display: none">注：再次点击图标隐藏此弹窗（点击定位图标）</span>' +
                  '        <div id="openZhongbaoTeam" style="margin-top:-32px;margin-left:313px;position:fixed;width: 50px;height: 14px;font-size: 14px;font-family: Source Han Sans CN;font-weight: 500;color: #FFFFFF;line-height: 29px;cursor:pointer;">详情 ></div>' +
                  '    </div>' +
                  '    <div class="contont" style="z-index:1003; position:relative; display: inline-block;width: 360px;height: 217px;font-size: 14px;font-family: Source Han Sans CN;font-weight: 400;color: #FFFEFE;line-height: 28px; overflow-y:auto;overflow-x:hidden;">';
              let text3 = '    </div>' +
                  '</div>' +
                  '<div style="position: relative;margin-top: -1px;"> <img src="' + bottom + ' "  alt="" style="position: absolute;left: 45%;"></div>';
              let text2 = "";
              if (undefined != _this.zbPosiNFO && null != _this.zbPosiNFO&& _this.zbPosiNFO.length>0) {
                _this.zbPosiNFO.map(item => {
                  text2 = text2 + ' <div style="width: 352px;height: 28px;background: #0E4950;margin: 7px 6px">' +
                      '            <span style="width: 6Px;height: 6Px;margin-left: 10Px; background: #F19149;border-radius: 50%;display: inline-block;"></span>' +
                      '            <span style="display: inline-block;margin-left:3px;width: 60px;height: 25px;font-size: 14px;font-family: Source Han Sans CN;font-weight: 400;color: #30DFE9;line-height:28px;' +
                      '  ">' + item.userName + '</span>' +
                      '            <span style="margin-left:13px;width: 41px;height: 14px;font-size: 14px;font-family: Source Han Sans CN;font-weight: 400;line-height:28px;">电话：</span>' +
                      '            <span style="margin-left:-2px;width: 41px;height: 14px;font-size: 14px;font-family: Source Han Sans CN;font-weight: 400;color: #30DFE9;line-height:28px;">' + item.userPhone + '</span>' +
                      '            <span style="margin-left:13px;width: 41px;height: 14px;font-size: 14px;font-family: Source Han Sans CN;font-weight: 400;line-height:28px;">专业：</span>' +
                      '            <span style="width: 41px;height: 14px;font-size: 14px;font-family: Source Han Sans CN;font-weight: 400;color: #30DFE9;line-height:28px;">' + item.userProfession + '</span>' +
                      '        </div>';
                });
              } else {
                text2 = '        <div style="width: 352px;height: 28px;background: #0E4950;margin: 7px 6px">' +
                    '            <span style="width: 6Px;height: 6Px;margin-left: 10Px; background: #F19149;border-radius: 50%;display: inline-block;"></span>' +
                    '            <span style="margin-left:3px;width: 41px;height: 14px;font-size: 14px;font-family: Source Han Sans CN;font-weight: 400;color: #30DFE9;line-height:28px;">暂无信息</span>' +
                    '        </div>';
              }
              let label = new BMapGL.Label(text1 + text2 + text3, { offset: new BMapGL.Size(-177, -301) });
              return label;
            },
        
        
        
            changeDarkStyle() {
              this.map.setOptions({
                style: {
                  styleJson: this.darkStyle
                },
                styleUrl: 'https://gis.10010.com:8219/dugis-baidu/baidumap/bmapgl/mapstyle/mapstyle.json',
              });
            },
        
            changeNormalStyle() {
              this.map.setOptions({
                style: 'default',
                styleUrl: '',
              });
            },
            // 加载本地存储的布局
    
          },
  };
  