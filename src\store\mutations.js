export default{
    setFullHeight (state, fullHeight) {
        state.fullHeight = fullHeight;
        sessionStorage.setItem('fullHeight', fullHeight);
    },
    setsubtoken (state, subtoken) {
        state.subtoken = subtoken;
        sessionStorage.setItem('subtoken', subtoken);
    },
    set_5G_fn_curruser (state, set_5G_fn_curruser) {
        state.set_5G_fn_curruser = set_5G_fn_curruser;
        sessionStorage.setItem('set_5G_fn_curruser', set_5G_fn_curruser);
    },
    pers (state, pers){
        state.pers = pers;
        sessionStorage.setItem('pers', pers);
    },
    menus (state, menus){
        state.menus = menus;
        sessionStorage.setItem('menus', menus);
    },
    setResize (state, setResize){
        state.setResize = setResize;
        sessionStorage.setItem('setResize', setResize);
    },
	
};