let dataB = {
    // name: '流程B',
    nodeList: [
        {
            id: 'nodeA',
            name: '节点A-不可拖拽',
            type: 'task',
            from: 'nodeA',
            to: 'nodeB',
            label: '条件A', 
        },
        {
            id: 'nodeB',
            type: 'task',
            name: '流程B-节点B',
            from: 'nodeB',
            to: 'nodeD'
            // left: '351px',
            // top: '223px',
            // ico: 'el-icon-goods',
            // state: 'error'
        },
        {
            id: 'nodeD',
            name: '流程B-节点D',
            type: 'task',
            // left: '723px',
            // top: '215px',
            // ico: 'el-icon-present',
            // state: 'running'
        }
    ],
    // lineList: [{
    //     from: 'nodeA',
    //     to: 'nodeB',
    //     label: '条件A'
    // }, {
    //     from: 'nodeB',
    //     to: 'nodeD'
    // }
    // ]
}

export function getDataB() {
    return dataB
}
