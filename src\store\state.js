
let fullHeight=document.documentElement.clientHeight;
let subtoken = '';//登录后权限token
let set_5G_fn_curruser = '';//登录后权限token
let pers = []; // 按钮权限信息
let menus = []; // 侧边栏菜单
let setResize = ""; // resize
try {
    if(sessionStorage.getItem('fullHeight')){
        fullHeight=sessionStorage.getItem('fullHeight');
    }
    
    if(sessionStorage.getItem('subtoken')){
        subtoken=sessionStorage.getItem('subtoken');
    }

    if(sessionStorage.getItem('set_5G_fn_curruser')){
        set_5G_fn_curruser=sessionStorage.getItem('set_5G_fn_curruser');
    }
    
    
}catch (err){

}
export default {
    fullHeight,
    set_5G_fn_curruser,
    subtoken,
    pers,
    menus,
    setResize
};