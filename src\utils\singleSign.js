
import $ from "jquery";
import { platforminfo } from "@/apis/api-login";
import { remove } from "./util";
let token;
const query = window.location.search.substring(1) || window.location.hash.split('?')[1];
query?.split?.('&')?.forEach?.(element => {
    if (element?.includes('token')) {
        token = element.split('=')[1];
    }
});
if (token) {
    $.ajax({
        url: "/cyberwing/upms/authpool/findAuth?sessionId=" + token,
        async: false,
        success(res) {
            if (res.success) {
                saveUserInfo(res);
            } else {
                window.open('/#/login', '_self');
            }
        },
        error() {
            window.open('/#/login', '_self');
        }
    });
}

// 向storage中储存用户信息
async function saveUserInfo(result, requestData) {
    sessionStorage.setItem("usid", token);
    sessionStorage.setItem(
        "permprops",
        JSON.stringify(result.data.permprops || {})
    );
    let permlist = result.data.permlist || [];
    const common =
        permlist.filter((item) => item.name === "公共配置权限")[0] || {};
    // common.action = (common.action ?? "") + homedepment.join(",");
    // 公共权限
    sessionStorage.setItem("common", common.action || "");
    remove(permlist, common);
    // 菜单
    sessionStorage.setItem("permlist", JSON.stringify(permlist));
    // 基本信息
    sessionStorage.setItem(
        "baseinfo",
        JSON.stringify(result.data.baseinfo)
    );
    sessionStorage.setItem("account", result.data.baseinfo.username);
    sessionStorage.setItem("alias", result.data.baseinfo.alias);
    sessionStorage.setItem("subtoken", result.data.baseinfo.subtoken);
    console.log(location.href.replace('?' + query, ''));
    window.open(location.href.replace('?' + query, ''), '_self');
    await getPlatforminfo();
}

async function getPlatforminfo() {
    var params = { keys: ["平台名称", "运营商信息", "登录页logo"] };
    let result = await platforminfo(params);
    for (let index = 0; index < result.data.data.kvs.length; index++) {
        if (result.data.data.kvs[index].key == "平台名称") {
            sessionStorage.setItem("login_title", this.login_title);
        }
    }
}
