<template>
    <div id="header-ombs">
        <el-row :gutter="10">
            <el-col :xs="22" :sm="22" :md="22" :lg="22" :xl="22">
                <div class="col_a">
                    <el-menu
                        :default-active="activeIndex2"
                        class="el-menu-demo"
                        mode="horizontal"
                    >
                        <el-menu-item index="1" style="padding-right: 0">
                            <img
                                src="../assets/logo_ombs.png"
                                alt=""
                                class="logo"
                            />
                        </el-menu-item>
                        <el-menu-item index="1" style="padding-right: 0">
                            <span
                                style="
                                    font-size: 16px;
                                    color: #000;
                                    font-weight: 700;
                                    padding-left: 20px;
                                "
                            >
                                {{ obmsLoginTitle }}后台配置系统
                            </span>
                        </el-menu-item>
                    </el-menu>
                </div>
            </el-col>

            <el-col
                :xs="2"
                :sm="2"
                :md="2"
                :lg="2"
                :xl="2"
                style="padding-left: 0"
            >
                <el-row class="block-col-1 new_user">
                    <el-col :span="12" style="padding-top: 5px">
                        <el-dropdown>
                            <div>{{ username }}</div>

                            <el-dropdown-menu slot="dropdown">
                                <el-dropdown-item>
                                    <div @click="changePwd">
                                        <span
                                            class="
                                                iconfont
                                                icon-yonghuziliao-xianxing
                                            "
                                        />

                                        修改密码
                                    </div>
                                </el-dropdown-item>
                                <el-dropdown-item>
                                    <div @click="logout()">
                                        <span
                                            class="iconfont icon-tuichu"
                                        />安全退出
                                    </div>
                                </el-dropdown-item>
                            </el-dropdown-menu>
                        </el-dropdown>
                    </el-col>
                </el-row>
            </el-col>
        </el-row>
        <el-dialog
            append-to-body
            title="修改密码"
            :visible.sync="dialogVisible"
            destroy-on-close
            width="500px"
            class="form-dialog"
        >
            <el-form
                ref="changeForm"
                inline
                :model="changeForm"
                :rules="rules"
                style="padding-top: 50px"
                label-width="130px"
            >
                <el-form-item label="原密码" prop="oldPwd" :error="psdError">
                    <el-input
                        v-model="changeForm.oldPwd"
                        type="password"
                        placeholder="请输入"
                        clearable
                    />
                </el-form-item>
                <el-form-item label="新密码" prop="newPwd" :error="psdError">
                    <el-input
                        v-model="changeForm.newPwd"
                        type="password"
                        placeholder="请输入"
                        clearable
                    />
                </el-form-item>
                <el-form-item
                    label="确认密码"
                    prop="confirmPwd"
                    :error="psdError"
                >
                    <el-input
                        v-model="changeForm.confirmPwd"
                        type="password"
                        placeholder="请输入"
                        clearable
                    />
                </el-form-item>

                <el-form-item class="change_button">
                    <div class="form-button" style="text-align: right">
                        <el-button
                            type="primary"
                            class="s_btn"
                            @click="ConfirmNewPassword(changeForm)"
                        >
                            确定
                        </el-button>
                        <el-button class="c_btn" @click="chanel">
                            取消
                        </el-button>
                    </div>
                </el-form-item>
            </el-form>
        </el-dialog>
    </div>
</template>
<script>
// import $ from "jquery";
import { logout, auth, pswEncode } from "@/apis/api-login";
import { getKeyValue } from "@/apis/api-platformConfiguration";
import { GetNotice } from "@/apis/api-csptIndex";
import { Notification } from "element-ui";
import { isComplexPassword } from "@/utils/validate";
export default {
    name: "HeaderOmbs",
    components: {},
    props: {},
    data() {
        var validateoldPwd = (rule, value, callback) => {
            if (value === "") {
                callback(new Error("请输入旧密码"));
            } else {
                this.nameError = "";
                callback();
            }
        };
        var validatenewPwd = (rule, value, callback) => {
            if (value === "") {
                callback(new Error("请输入新密码"));
            } else if (value == this.changeForm.oldPwd) {
                callback(new Error("新密码与旧密码相同"));
            } else if (!isComplexPassword(value)) {
                callback(
                    new Error(
                        "密码必须大于八位，且包含大写字母、小写字母、数字、特殊字符中的任意三类"
                    )
                );
            } else {
                this.psdError = "";
                callback();
            }
        };
        var validateconfirmPwd = (rule, value, callback) => {
            if (value === "") {
                // this.$message({ duration: 1000, message: '请输入验证码', type: 'error' })
                callback(new Error("请输入确认密码"));
            } else if (value != this.changeForm.newPwd) {
                callback(new Error("密码输入不一致"));
            } else {
                this.codeError = "";
                callback();
            }
        };
        return {
            obmsFroodTitle: "",
            obmsLoginTitle: "",
            username: "",
            usid: "",
            activeIndex: "1",
            activeIndex2: "5",
            dialogVisible: false,
            changeForm: { oldPwd: "", newPwd: "", confirmPwd: "" },
            psdError: "",
            nameError: "",
            options: [
                {
                    value: "通信重保场景",
                    label: "通信重保场景",
                },
            ],
            value: "通信重保场景",
            pmenu: [],
            rules: {
                oldPwd: [{ validator: validateoldPwd, trigger: "blur" }],
                newPwd: [{ validator: validatenewPwd, trigger: "blur" }],
                confirmPwd: [
                    { validator: validateconfirmPwd, trigger: "blur" },
                ],
            },
            defaultActive: "首页",
            // 系统公告
            GetNotice: [
                // { notice_title: "国庆节放假运维值班公告", notice_time: "9-29" },
                // { notice_title: "系统版本v2.0.1更新通知", notice_time: "9-29" },
                // { notice_title: "关于银行汇款延迟到账的公告", notice_time: "9-29" },
                // { notice_title: "关于延续疫情期间税收政策关于延续疫情期间税收政策关于延续疫情期间税收政策关于延续疫情期间税收政策关于延续疫情期间税收政策关于延续疫情期间税收政策", notice_time: "9-29" },
                // { notice_title: "自助冲红发票功能上线啦！", notice_time: "9-29" },
            ],
        };
    },
    computed: {},
    watch: {},
    created() {
        // this.$nextTick(() => {
        //     this.pmenu = JSON.parse(sessionStorage.getItem("permlist"));
        // });
        // console.log(this.pmenu);
    },
    mounted() {
        // this.username = sessionStorage.getItem("alias");
        this.username = sessionStorage.getItem("account");
        this.usid = sessionStorage.getItem("usid");
        this.getAccess({
            sessionId: this.usid,
        });
        this.$forceUpdate();
        this.defaultActive = this.$route.name;
        // console.log(this.$route.name);
        // this.getNotice();
        this.getkey();
    },
    activited() {},
    update() {},
    beforeRouteUpdate() {},
    methods: {
        async getkey() {
            const res = await getKeyValue({
                keys: ["平台名称", "运营商信息"],
            });
            // console.log(res);
            for (let index = 0; index < res.data.data.kvs.length; index++) {
                if (res.data.data.kvs[index].key == "平台名称") {
                    this.obmsLoginTitle = res.data.data.kvs[index].value;
                } else if (res.data.data.kvs[index].key == "运营商信息") {
                    this.obmsFroodTitle = res.data.data.kvs[index].value;
                }
            }
        },
        async getNotice() {
            // window.location.href = "/login";
            const res = await GetNotice({});
            if (res.data.success && res.data.data != null) {
                this.GetNotice = res.data.data;
            }
            // console.log(res);
            // console.log(this.GetNotice);
        },
        changePwd() {
            const _this = this;
            _this.dialogVisible = true;
        },
        ConfirmNewPassword(formName) {
            // console.log(formName);
            this.$refs["changeForm"].validate(async (valid) => {
                if (valid) {
                    let params = {
                        password: this.changeForm.oldPwd,
                        newpassword: this.changeForm.newPwd,
                    };
                    const res = await pswEncode({
                        value: formName.oldPwd,
                    });
                    const res1 = await pswEncode({
                        value: formName.newPwd,
                    });
                    params.password = res.data.data;
                    params.newpassword = res1.data.data;
                    // console.log(params);
                    this.$axios
                        .post(
                            "/cyberwing/upms/ui/user/updateDuserPassword",
                            {},
                            {
                                params: params,
                                headers: {
                                    usid: sessionStorage.getItem("usid"),
                                    subtoken:
                                        sessionStorage.getItem("subtoken"),
                                },
                            }
                        )
                        .then((res) => {
                            if (res.data.success) {
                                this.$message({
                                    type: "success",
                                    message: `密码修改成功,准备前往登录页...`,
                                    duration: 800,
                                    onClose: (res) => {
                                        this.$refs["changeForm"].resetFields();
                                        this.dialogVisible = false;
                                        //清空token并跳转
                                        localStorage.clear();
                                        sessionStorage.clear();
                                        // if(store.state.token){//如果是单点登录退回到login.vue就直接给转接到门户的登录页去
                                        //window.location.href = this.global_variable.relogin_baseUrl;
                                        this.$router.push("/login");
                                        // }else{
                                        //     this.$router.push('/login')
                                        // }
                                        history.pushState(
                                            null,
                                            null,
                                            document.URL
                                        );
                                        window.addEventListener(
                                            "popstate",
                                            function (e) {
                                                history.pushState(
                                                    null,
                                                    null,
                                                    document.URL
                                                );
                                            },
                                            false
                                        );
                                    },
                                });
                            } else {
                                this.$message({
                                    type: "error",
                                    message: res.data.error_msg,
                                    duration: 800,
                                    onClose: (res) => {
                                        this.$refs["changeForm"].resetFields();
                                        this.changeForm = {};
                                        // this.dialogVisible = false;
                                        //清空token并跳转
                                        // if(store.state.token){//如果是单点登录退回到login.vue就直接给转接到门户的登录页去
                                        //window.location.href = this.global_variable.relogin_baseUrl;

                                        // }else{
                                        //     this.$router.push('/login')
                                        // }

                                        window.addEventListener(
                                            "popstate",
                                            function (e) {
                                                history.pushState(
                                                    null,
                                                    null,
                                                    document.URL
                                                );
                                            },
                                            false
                                        );
                                    },
                                });
                            }
                        });
                } else {
                    return false;
                }
            });
        },
        chanel() {
            this.dialogVisible = false;
            // this.visible = false;
            this.changeForm = {};
        },
        async getAccess(requestData) {
            const _this = this;
            const result = await auth(requestData);
            // console.log(result, _this.pmenu, result.data.data);
            if (result.data.success && result.data.data != null) {
                try {
                    // this.saveUserInfo(result, requestData);
                } catch (error) {
                    // _this.$notify({
                    //     title: "提示",
                    //     message: "服务器错误",
                    //     type: "error",
                    // });
                    Notification({
                        title: "提示",
                        message: error,
                        type: "error",
                    });
                }
                _this.pmenu = result.data.data.permlist;
                // console.log(_this.pmenu);
            } else {
                // _this.$notify({
                //     title: "授权",
                //     type: "error",
                //     message: result.error_msg,
                //     duration: 2500,
                // });
                Notification({
                    title: "提示",
                    message: "请重新登录",
                    type: "error",
                });
                this.$router.push({ path: "/login", replace: true });
            }
        },
        async logout() {
            // window.location.href = "/login";

            const res = await logout({
                sessionIdv: this.usid,
            });
            // if (res.success) {
            var login_title = sessionStorage.getItem("login_title");
            document.title = login_title;
            this.$router.push({ path: "/login", replace: true });
            // }
            sessionStorage.clear();
            // console.log(res);
        },
        //时间转换
        transformTimestamp(timestamp) {
            let a = new Date(timestamp).getTime();
            const date = new Date(a);
            const Y = date.getFullYear() + "-";
            const M =
                (date.getMonth() + 1 < 10
                    ? "0" + (date.getMonth() + 1)
                    : date.getMonth() + 1) + "-";
            const D =
                (date.getDate() < 10 ? "0" + date.getDate() : date.getDate()) +
                "  ";
            const h =
                (date.getHours() < 10
                    ? "0" + date.getHours()
                    : date.getHours()) + ":";
            const m =
                date.getMinutes() < 10
                    ? "0" + date.getMinutes()
                    : date.getMinutes();
            const s = date.getSeconds(); // 秒
            const dateString = Y + M + D + h + m + ":" + s;
            // console.log('dateString', dateString); // > dateString 2021-07-06 14:23
            return dateString;
        },
    },
    filter: {},
};
</script>

<style >
#header-ombs {
    height: 50px;
    box-shadow: 0px 5px 5px rgb(0 0 0 / 10%);
}
.el-menu--horizontal > .el-submenu .el-submenu__title {
    height: 60px;
    line-height: 60px;
    color: #fff;
}

.el-menu--horizontal > .el-menu-item {
    border-bottom: none !important;
}

.el-menu--horizontal > .el-menu-item {
    color: #fff;
}

#header-ombs .el-menu--horizontal > .el-menu-item.is-active {
    border-bottom: none !important;
}

.el-menu--horizontal > .el-submenu .el-submenu__icon-arrow {
    display: none;
}
.el-submenu__title {
    padding: 0 13px;
}

.el-menu--horizontal > .el-submenu .el-submenu__title {
    border-bottom: none;
}
</style>

<style scoped>
* {
    /* padding: 0px; */
    /* margin: 0px; */
    /* background: #080c3a; */
}

html {
    height: 100%;
    width: 100%;
}

.el-row {
    width: 100%;
}

.s_btn,
.c_btn {
    padding: 9px 14px;
}

#setTab {
    height: 100%;
}

.el-container.is-vertical {
    height: 100%;
}

.wrap ul {
    overflow: hidden;
    height: 120px;
    line-height: 120px;
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    align-content: stretch;
    justify-content: flex-end;
    align-items: center;
    padding-right: 330px;
}
.over_hidden {
    width: 450px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    height: 2.3rem;
    line-height: 2.3rem;
    font-size: 0.9rem;
    cursor: pointer;
    padding: 0 6px;
}

.wrap li {
    float: left;
    list-style: none;
    margin-right: 10px;
    cursor: pointer;
    padding: 2px 5px;
    width: 45px;
    height: 55px;
    color: #fff;
}

.link {
    cursor: pointer;
    color: #f00;
}

/* .wrap {
            width: 200px;
            margin: 50px auto
        } */

.wrap,
.ellipsis {
    font-size: 12px;
    /* width: 200px; */
}

.tab_div div {
    display: none;
    padding: 10px;
}

.tab_div {
    text-align: left;
    border: 1px #ccc solid;
    /* height: 200px; */
    clear: both;
}

.cur {
    background: #060;
    color: #fff;
}

#setTab_2 {
    margin-top: 20px;
}

.el-header,
.el-footer {
    /* background-color: #B3C0D1; */
    /* color: #333; */
    text-align: left;
    line-height: 80px;
}

.el-aside {
    /* background-color: #D3DCE6; */
    /* color: #333; */
    text-align: center;
    line-height: 200px;
}

.el-main {
    /* background-color: #E9EEF3; */
    /* color: #333; */
    text-align: center;
    line-height: 160px;
}

body > .el-container {
    margin-bottom: 40px;
}

.el-container:nth-child(5) .el-aside,
.el-container:nth-child(6) .el-aside {
    line-height: 260px;
}

.el-container:nth-child(7) .el-aside {
    line-height: 320px;
}

el-header {
    height: 50px;
    line-height: 50px;
    width: 100%;
}

.el-header {
    height: 50px !important;
}

.el-row {
    /* height: 100%; */
}

.el-col {
    /* height: 100%; */
}

.col_a {
    /* padding-top: 10px; */
    padding-left: 50px;
    margin: 10px 0;
}

.col_b {
    width: 100%;
    padding-top: 10px;
    padding-left: 45px;
    /* height: 100%; */
    /* background-color: #090e4e; */
}

.col_menu {
    width: 100%;
    /* margin-left: 88px; */
    /* padding-top: 10px; */
}

.el-main {
    width: 100%;
    height: calc(100% - 120px);
    padding: 20px;
}

.main_bg {
    padding: 20px;
    width: 100%;
    height: calc(100% - 120px);
    /* background: url(./img/base_bg.jpg) no-repeat; */
    background-size: 100% 100%;
}

.main_bg1 {
    padding: 20px;
    width: 100%;
    height: calc(100% - 120px);
    /* background: url(./img/networkResource_b.jpg) no-repeat; */
    background-size: 100% 100%;
}

.main_bg2 {
    padding: 20px;
    width: 100%;
    height: calc(100% - 120px);
    /* background: url(./img/networkResource_a.jpg) no-repeat; */
    background-size: 100% 100%;
}

.main_bg3 {
    padding: 20px;
    width: 100%;
    height: calc(100% - 120px);
    /* background: url(./img/cardManagement.jpg) no-repeat; */
    background-size: 100% 100%;
}

.main_bg4 {
    padding: 20px;
    width: 100%;
    height: calc(100% - 120px);
    /* background: url(./img/zhuanwnag.jpg) no-repeat; */
    background-size: 100% 100%;
}

.main_bg5 {
    padding: 20px;
    width: 100%;
    height: calc(100% - 120px);
    /* background: url(./img/upfchart.jpg) no-repeat; */
    background-size: 100% 100%;
}

.main_bg6 {
    padding: 20px;
    width: 100%;
    height: calc(100% - 120px);
    /* background: url(./img/TotalnumberStations.jpg) no-repeat; */
    background-size: 100% 100%;
}

.Upperleft {
    cursor: pointer;
    line-height: 100px;
    color: transparent;
    margin-top: 56px;
}

.el-submenu {
    width: 80px !important;
}

.el-menu-demo {
    /* margin: 10px 0; */
}

.el-menu {
    background: transparent;
    border-bottom: none;
    /* height: 80px;
    line-height: 80px; */
}

.el-menu.el-menu--horizontal {
    border-bottom: none;
}

.style_icon {
    width: 100%;
    height: 28px;
    text-align: center;
    font-size: 22px;
}

.el-submenu {
    width: 100px;
}

.el-menu--horizontal > .el-menu-item {
    height: 30px;
    line-height: 30px;
}

.el-menu--horizontal > .el-submenu .el-submenu__title {
    height: 30px;
    line-height: 30px;
}

.el-menu-item:focus,
.el-menu-item:hover {
    background-color: transparent !important;
}

.el-menu--horizontal > .el-menu-item:not(.is-disabled):focus,
.el-menu--horizontal > .el-menu-item:not(.is-disabled):hover,
.el-menu--horizontal > .el-submenu .el-submenu__title:hover {
    /* background-color: transparent !important; */
}

#d:hover {
    color: #000;
}

.logo {
    width: auto;
    height: auto;
    max-width: 100%;
    max-height: 100%;
}

.news_sub .el-menu--popup {
    width: 307px;
}

.el-input__inner {
    height: 30px;
    background-color: transparent;
    border-radius: 0;
    color: #3bcbe0;
    width: 286px;
}

.el-menu-item {
    padding: 0 10px;
}

.el-menu--horizontal .el-menu .el-menu-item,
.el-menu--horizontal .el-menu .el-submenu__title {
    background-color: transparent;
    color: #fff;
}

/* .el-menu--horizontal {
            position: absolute;
            top: 70px;
            left: 1264px;
            z-index: 2005;
        } */

/* #c,#d {
            font-size: 12px;
        } */

.icon-jiankong {
    font-size: 20px;
}

.icon-baobiaoshenhe {
    font-size: 24px;
}

.col_a_title {
    font-size: 22px;
    color: #ffffff;
    font-weight: 700;
    font-family: FZDHTJW--GB1-0;
    /* margin-top: 50px; */
}

.col_c {
    padding-top: 22px;
    height: 70px;
    line-height: 70px;
    margin: 15px 0;
    text-align: right;
}

.el-submenu__title i {
    display: none !important;
}

.new_user {
    padding-right: 45px;
    margin: 10px 0px;
    color: #fff;
}

.el-dropdown {
    color: #000;
}

.el-dropdown-menu {
    /* background-color: #080c3a; */
    border: none;
}

.el-dropdown-menu__item {
    color: #000;
}

.news_left {
    float: left;
}
.news_right {
    float: right;
}

.news_first {
    height: 40px;
    border-bottom: 1px solid #fff;
    margin: 15px 10px;
}

.icon-xiaoxiweidu {
    font-size: 13px;
    padding-top: 7px;
}

.change_button {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: flex-end;
}

.el-popper {
    background-color: transparent;
}
</style>
