 //方式一
 const deviceWidth = document.documentElement.clientWidth || document.body.clientWidth;
 document.querySelector('html').style.fontSize = deviceWidth / 168 + 'px';
 
//方式二
 document.documentElement.style.fontSize = document.documentElement.clientWidth / 168 + "px";
 window.addEventListener(
   "resize",
   function() {
     document.documentElement.style.fontSize =
       document.documentElement.clientWidth / 168 + "px";
   },
   false
 );
//方式一和方式二 效果一样
