<template>
  <div class="notice-container">
    <div class="item-list2">
      <div 
        v-for="(notice, idx) in data" 
        :key="idx" 
        class="mapLeftTop1div"
        @click="showNoticeDetail(notice)"
      >
        <div class="item-list2">
          <div class="notice-header">
            <span class="mapLeftTop1Radis" />
            <span class="mapLeftTopName">{{ notice.mesTitle }}</span>
            <span style="float: right;margin-right: 10Px;" class="notice-time">{{ notice.mesTime }}</span>
          </div>
          <div style="padding: 2Px 22Px;cursor:pointer;">
            <span class="mapLeftTopText">{{ notice.mesName }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
  
  <script>
  export default {
    props: {
      data: {
        type: Array,
        default: () => []
      }
    },
    methods: {
      showNoticeDetail(notice) {
        console.log('查看公告详情:', notice);
        // 实际项目中这里可能会打开详情弹窗或跳转页面
      }
    }
  };
  </script>
  
  <style scoped>
  .notice-container {
    height: 75%;
    overflow: auto;
  }
  
  .notice-item {
    padding: 8px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
    cursor: pointer;
    transition: background-color 0.2s;
  }
  
  .notice-item:hover {
    background-color: rgba(255, 255, 255, 0.05);
  }
  .mapLeftTop1div {
  margin-left: 16Px;
  margin-top: 25Px;
  width: 388Px;
  height: 97Px;
  overflow: auto;
  z-index: 1;
  background: #0C3038;
  display: inline-block;
  opacity: 0.8;
}
.mapLeftTop1Radis {
  width: 8Px;
  height: 8Px;
  margin-left: 16Px;
  background: #39D4CD;
  border-radius: 50%;
  display: inline-block;
}

.mapLeftTopName {
  width: 100%;
  height: 100px;
  font-size: 15Px;
  font-family: Source Han Sans CN;
  font-weight: 500;
  color: #39D4CD;
  line-height: 35Px;
  margin-left: 7Px;
}

.mapLeftTopText {
  width: 335Px;
  height: 50Px;
  font-size: 14Px;
  font-family: Source Han Sans CN;
  font-weight: 400;
  color: #FFFFFF;
  line-height: 20Px;
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
}
::-webkit-scrollbar {
  width: 0;
}
  </style>
  