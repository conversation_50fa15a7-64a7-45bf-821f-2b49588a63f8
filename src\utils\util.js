
import CryptoJS from 'crypto-js';
import ResizeObserver from 'resize-observer-polyfill';
import $ from "jquery";
import ClipboardJS from 'clipboard';
import Vue from 'vue';
//  树状数据进行排序
export function treeArrSort(treeArr = [], key, cb) {
  if (Array.isArray(treeArr)) {
    return treeArr.sort((current, next) => {
      treeArrSort(current[key] || [], key, cb);
      return cb(current, next);
    });
  } else if (typeof treeArr === 'object') {
    treeArr[key] = treeArrSort.call(treeArr, treeArr[key], key, cb);
    return treeArr;
  }
}

export function treeArrMap(treeArr = [], key, cb) {
  if (Array.isArray(treeArr)) {
    return treeArr.map(treeItem => {
      // 通过上下文去锁定上层数据
      const newTreeItem = cb.call(this || {}, treeItem, this || {});
      newTreeItem[key] = treeArrMap.call(newTreeItem, newTreeItem[key], key, cb);
      return newTreeItem;
    });
  } else if (typeof treeArr === 'object') {
    treeArr[key] = treeArrMap.call(treeArr, treeArr[key], key, cb);
    return treeArr;
  }
}

export function makeMap(str, expectsLowerCase) {
  var map = Object.create(null); // 创建一个新的对象
  var list = str.split(','); // 按字符串,分割
  for (var i = 0; i < list.length; i++) {
    map[list[i]] = true; // map 对象中的[name1,name2,name3,name4]  变成这样的map{name1:true,name2:true,name3:true,name4:true}
  }
  return expectsLowerCase
    ? function (val) {
      return map[val.toLowerCase()];
    } // 返回一个柯里化函数 toLowerCase转换成小写
    : function (val) {
      return map[val];
    }; // 返回一个柯里化函数 并且把map中添加一个 属性建
}

// 数组删除
export function remove(arr, item) {
  if (arr.length) {
    var index = arr.indexOf(item);
    if (index > -1) {
      return arr.splice(index, 1);
    }
  }
}

// 密匙
var cryptographicKey = 'CAPITEKIOTP';
// 加密
export const encrypt = (message) => {
  var keyHex = CryptoJS.enc.Utf8.parse(cryptographicKey);
  var encrypted = CryptoJS.DES.encrypt(message, keyHex, {
    mode: CryptoJS.mode.ECB,
    padding: CryptoJS.pad.Pkcs7
  });
  return encrypted.ciphertext.toString();
};
// 解密
export const decrypt = (ciphertext) => {
  const keyHex = CryptoJS.enc.Utf8.parse(cryptographicKey);
  // direct decrypt ciphertext
  const decrypted = CryptoJS.DES.decrypt({
    ciphertext: CryptoJS.enc.Hex.parse(ciphertext)
  }, keyHex, {
    mode: CryptoJS.mode.ECB,
    padding: CryptoJS.pad.Pkcs7
  });
  return decrypted.toString(CryptoJS.enc.Utf8);
};

var generXber = (function () {
  var today = new Date();
  var seed = today.getTime();

  function rnd() {
    seed = (seed * 9301 + 49297) % 233280;
    return seed / (233280.0);
  };
  return function generXber(number) {
    return Math.ceil(rnd() * number);
  };
})();

function insertStr(soure, start, newStr) {
  return soure.slice(0, start) + newStr + soure.slice(start);
}

export var CapitekJsUtil = {

  rdmnum: function (value) {
    return (generXber(8999999990000000) + 1000000000000000 + generXber(1000000)) / 10000000000000000;
  },

  transposing: function (str, startIndex, length, newIndex) {
    // 获取salt
    var salt = str.substr(startIndex, length);
    var code = str.substr(0, startIndex) + str.substr(startIndex + length);
    if (newIndex > code.length) {
      newIndex = code.length;
    }
    var result = insertStr(code, newIndex, salt);
    return result;
  }
};

export const transposing = (id) => {
  return CapitekJsUtil.transposing(id, 5, 8, 8);
};

export const resizeObserver = (ele = 'body', cb = () => { }) => {
  const ro = new ResizeObserver((entries, observer) => {
    entries.forEach((entry) => {
      cb(entry);
    });
  });
  $(ele).each(function () {
    ro.observe(this);
  });
  return ro;
};

export const coverObjNull = (val) => {
  if (typeof val !== 'object' || val === null) return;
  for (let key in val) {
    val[key] = val[key] ?? '-';
    coverObjNull(val[key]);
  }
};

var clipboard = new ClipboardJS('.copy', {
  text: function (tigger) {
    return tigger.getAttribute('copy-text');
  }
});

clipboard.on('success', () => {
  Vue.prototype.$message.success('复制成功');
});


export function dataURLtoFile(dataurl, filename) {//将base64转换为文件
        var arr = dataurl.split(','), mime = arr[0].match(/:(.*?);/)[1],
            bstr = atob(arr[1]), n = bstr.length, u8arr = new Uint8Array(n);
        while(n--){
            u8arr[n] = bstr.charCodeAt(n);
        }
        return new File([u8arr], filename, {type:mime});
}