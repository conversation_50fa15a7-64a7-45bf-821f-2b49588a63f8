<template>
  <div>
    <j-hover-btn
      :width="70"
      :text="textTitleS"
      :btn-style="btnStyle"
      @hoverBtnClick="changeBtnClick()"
    />
  </div>
</template>
<script>
import bus from "@/eventBus/bus.js";
export default {
  props: {
    textTitle: { type: String, default: "换肤" },
  },

  data() {
    return {
      changeImg: 0,
      btnStyle: {
        fontSize: "small",
        top: "70vh",
        left: "90vw",
        background:
          "linear-gradient(180deg, rgba(0, 60, 114, 0.2) 0%, #0d5d8e 100%)",
        border: "1px solid #48b5ff",
        color: "#5dc3ff",
      },
      textTitleS: "换肤",
    };
  },
  // 生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {
    this.textTitleS = this.textTitle;
  },
  methods: {
    changeBtnClick() {
      
      if (this.changeImg == 5) {
        this.changeImg = 1;
      }else {
        this.changeImg += 1;
      }
      this.$emit("change-btn", this.changeImg);
    },
  },
};
</script>
