<template>
  <div class="supplies-container">
    <div
      class="supply-item"
      @click="viewSupplyDetail('5')"
    >
      <div class="mapRightTop1Img2" />
      <div class="mapLeftTop2Text2Top">
        {{ data.cataNum }}<span class="unit">类</span>/{{ data.num }}<span class="unit">件</span>
      </div>
      <div class="mapLeftTop2Text2Bottom">
        备品备件
      </div>
    </div>
    <div
      class="supply-item"
      @click="viewSupplyDetail('6')"
    >
      <div class="mapRightTop1Img3" />
      <div class="mapLeftTop2Text2Top">
        {{ data.emergElecNum || 0 }}<span class="unit">台</span>
      </div>
      <div class="mapLeftTop2Text2Bottom">
        应急发电机组
      </div>
    </div>
  </div>
</template>
  
  <script>
  export default {
    props: {
      data: {
        type: Object,
        default: () => ({})
      }
    },
    methods: {
      viewSupplyDetail(type) {
        console.log(`查看${type}详情`);
        // 实际项目中这里可能会打开详情弹窗或跳转页面
        const data = {
          // type:'数通设备',
          message: type,
        };
        
        // 触发自定义事件，并传递数据
        this.$emit('customEvent', data);
      }
    }
  };
  </script>
  
  <style scoped>
  .supplies-container {
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    height: 75%;
    overflow: auto;
  }
  
  .supply-item {
    display: flex;
    align-items: center;
    cursor: pointer;
    padding: 8px;
    border-radius: 4px;
    transition: background-color 0.2s;
  }
  
  .supply-item:hover {
    background-color: rgba(255, 255, 255, 0.05);
  }
  
  .mapRightTop1Img2, .mapRightTop1Img3 {
    width: 32px;
    height: 32px;
    margin-right: 12px;
  }
  
  .supply-value {
    font-size: 18px;
    font-weight: 500;
    margin-bottom: 2px;
  }
  
  .unit {
    font-size: 14px;
    margin-left: 2px;
  }
  
  .supply-label {
    font-size: 14px;
    color: rgba(255, 255, 255, 0.7);
  }
  .mapRightTop1Img2 {
  margin-top: 17Px;
  margin-left: 16Px;
  width: 66Px;
  height: 66Px;
  background-image: url('../../assets/zxx/mapRightTop1Img2.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
  display: inline-block;
}

.mapRightTop1Img3 {
  margin-top: 17Px;
  margin-left: 16Px;
  width: 66Px;
  height: 66Px;
  background-image: url('../../assets/zxx/mapRightTop1Img3.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
  display: inline-block;
}
.mapLeftTop2Text2Top {
  width: 140Px;
  /* height: 24Px; */
  font-size: 28Px;
  font-family: D-DIN-PRO;
  font-weight: 500;
  text-align: center;
  color: #55A4FE;
  /* position: absolute; */
}
::-webkit-scrollbar {
  width: 0;
}
.mapLeftTop2Text2Bottom {
  width: 140Px;
  height: 38Px;
  line-height: 38Px;
  font-size: 14Px;
  text-align: center;
  font-family: Source Han Sans CN;
  font-weight: 400;
  color: #FFFFFF;
  margin: 10% 0;
  /* position: absolute; */
}
  </style>
  