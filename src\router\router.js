import Vue from "vue";
import Router from "vue-router";

// // 解决ElementUI导航栏中的vue-router在3.0版本以上重复点菜单报错问题
const originalPush = Router.prototype.push;
Router.prototype.push = function push(location) {
  return originalPush.call(this, location).catch((err) => err);
};
Vue.use(Router);
const undefindindex = () =>
    import(/* webpackChunkName: "index" */ "@/components/404"); //404
// 首页
const home = () => import(/* webpackChunkName: "home" */ "@/pages/home");
const topo = () => import(/* webpackChunkName: "home" */ "@/pages/topo");
const login = () => import( /* webpackChunkName: "login" */ '@/pages/login');

const reinsuranceBigScreen = () => import(/* webpackChunkName: "home" */ "@/pages/reinsuranceBigScreen");
const reinsuranceBigScreenChunJie = () => import(/* webpackChunkName: "home" */ "@/pages/reinsuranceBigScreenChunJie");

const empty = () =>
    import(/* webpackChunkName: "home" */ "@/components/EmptyD");


//##########
var router = new Router({
  mode: "history",
  base: '/portal',
  // base: '',
  routes: [
    // {
    //   path: "/reinsuranceBigScreen",
    //   name: "首 页",
    //   component: reinsuranceBigScreen,
    //   meta: {
    //     requireAuth: true,
    //     title: '通信重保场景-23专项保障',
    //     noCache: false
    //   }
    // },
    // {
    //   path: "/reinsuranceBigScreenChunJie",
    //   name: "首 页",
    //   component: reinsuranceBigScreenChunJie,
    //   meta: {
    //     requireAuth: true,
    //     title: '重保场景-24春晚',
    //     noCache: false
    //   }
    // },
    {
      path: "/reinsuranceBigScreen/:id",
      name: "首 页",
      component: reinsuranceBigScreen,
      // meta: {
      //   requireAuth: true,
      //   title: '重保场景-24春晚',
      //   noCache: false
      // }
    },{
      path: "/topo",
      name: "topu",
      component: topo,
    },
    {
      path: "/home",
      name: "重保人员",
      component: home,
      meta: {
        requireAuth: true,
        title: '通信重保场景-组织人员',
        noCache: false
      }
    },
    {
      path: "/empty",
      name: "",
      component: empty,
    },
    {
      path: "/reinsuranceBigScreenChunJie",
      name: "首页",
      id: 1,
      component: reinsuranceBigScreenChunJie,
      redirect: "/reinsuranceBigScreenChunJie",
      meta: {
        requireAuth: true,
        title: '通信重保场景-春节',
        noCache: false
      }
    },
    {
      path: "/",
      name: "首页",
      id: 1,
      component: reinsuranceBigScreenChunJie,
      redirect: "/reinsuranceBigScreenChunJie",
      meta: {
        requireAuth: true,
        title: '重保场景-24春晚',
        noCache: false
      }
    },
    {
      path: "/login",
      name: "login",
      component: login,
    },

  ],
});

// 配置路由权限
/*router.beforeEach((to, from, next) => {
  // let getTokenPath = to.path;
  // let xaiot_token = store.state.xaiot_token;
  let token = sessionStorage.getItem("tokenValue");
  let account = sessionStorage.getItem("account");
  if (to.meta.requireAuth) { // 判断该路由是否需要登录权限

    if (token) { // 判断本地是否存在access_token
      // if (to.meta.require_sys == "ombs") {
      //   if (account == "csptadmin") {
      //     // alert("该账号没有权限")

      //     next(from.path);
      //   } else {
      //     ;
      //     next();
      //   }
      // } else {
      //   if (account == "ombsadmin") {
      //     // alert("该账号没有权限")
      //     next(from.path);

      //   } else {
      //     next();

      //   }
      // }

      // store.commit('clearToken')

      next();
    } else {
      // 未登录,跳转到登录页面，并且带上 将要去的地址，方便登录后跳转。
      localStorage.clear();
      sessionStorage.clear();

      // store.commit('clearToken')
      // if(store.state.token){//如果是单点登录退回到login.vue就直接给转接到门户的登录页去
      console.log('未登录,跳转到登录页面，并且带上 将要去的地址，方便登录后跳转。');
      next('/login');
      //window.location.href=global_variable.relogin_baseUrl
      // }else{
      //     next('/login')
      // }
    }
  } else {
    //   store.commit('clearToken')
    next();
  }
});*/
router.afterEach((to, from) => {
  window, scrollTo(0, 0);
});

export default router;
