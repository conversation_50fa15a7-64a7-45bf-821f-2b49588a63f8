{"name": "web_base", "version": "0.1.0", "private": true, "scripts": {"dev": "npm run serve", "serve": "vue-cli-service serve --open", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "devDependencies": {"@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-eslint": "~4.5.0", "@vue/cli-service": "~4.5.0", "babel-eslint": "^10.1.0", "bootstrap": "^5.1.3", "eslint": "^6.7.2", "eslint-plugin-vue": "^6.2.2", "jquery": "^3.6.0", "script-loader": "^0.7.2", "swiper": "^4.5.1", "vue-template-compiler": "^2.6.11"}, "dependencies": {"@amap/amap-jsapi-loader": "^1.0.1", "@jiaminghi/data-view": "^2.10.0", "@jyeontu/jvuewheel": "^0.6.0", "@popperjs/core": "^2.10.2", "axios": "^0.24.0", "chart.js": "^4.4.9", "clipboard": "^2.0.8", "core-js": "^3.26.1", "crypto-js": "^4.1.1", "dayjs": "^1.10.7", "echarts": "^5.2.2", "echarts-liquidfill": "^3.1.0", "element-ui": "^2.15.6", "esbuild-loader": "^2.18.0", "file-saver": "^2.0.5", "jsencrypt": "^3.3.1", "lodash": "^4.17.21", "node-sass": "^4.13.1", "popper.js": "^1.16.1", "postcss-plugin-px2rem": "^0.8.1", "resize-observer-polyfill": "^1.5.1", "sass-loader": "^8.0.0", "three": "^0.151.3", "v-click-outside": "^3.2.0", "vue": "^2.6.11", "vue-amap": "^0.5.10", "vue-awesome-swiper": "^3.1.3", "vue-codemirror": "^4.0.6", "vue-draggable-plus": "^0.6.0", "vue-grid-layout": "^2.4.0", "vue-json-excel": "^0.3.0", "vue-layer": "^1.2.5", "vue-lottie": "^0.2.1", "vue-router": "^3.5.3", "vuedraggable": "^2.23.0", "vuex": "^3.6.2", "xlsx": "^0.18.5", "xlsx-style": "^0.8.13"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "plugin:vue/recommended"], "parserOptions": {"parser": "babel-es<PERSON>"}, "rules": {"no-unused-components": "off", "no-console": "off", "no-irregular-whitespace": "off", "no-unused-vars": "off", "no-case-declarations": "off", "semi": ["error", "always"], "camelcase": 0, "max-len": 0}}, "parserOptions": {"ecmaVersion": 7, "sourceType": "module"}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}