<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="utf-8">
  <title>MapVGL</title>
  <meta http-equiv="X-UA-Compatible" content="IE=Edge">
  <meta name="viewport" content="initial-scale=1.0, user-scalable=no">
  <style>
    html,
    body {
      width: 100%;
      height: 100%;
      margin: 0;
      padding: 0;
    }
    #map_container {
      width: 100%;
      height: 100%;
      margin: 0;
    }
    .stDataDiv1{
        width: 112px;
        height: 40px;
        background: linear-gradient(-51deg, #3DB071 0%, #47F195 100%);
        border: 2px solid;
        border-image: linear-gradient(-55deg, rgba(133,255,189,0.5), rgba(90,219,149,0.5)) 2 2;
        opacity: 0.8;
        text-align: center;
    }
    .stDataSpan1{
        width: 91px;
        height: 18px;
        font-size: 16px;
        font-family: Source <PERSON> Sans CN;
        font-weight: 500;
        color: #091220;
        line-height: 40px;
    }
  </style>
</head>
<body>
<!--<div id="map_container"></div>-->
<div style="width: 365Px;height: 264Px;border: 1Px solid;border-image: linear-gradient(0deg, #4AB38C, #105745) 10 10;background: linear-gradient(0deg, #0E2D28 0%, rgba(7,70,83,0.8) 100%);">
    <div>
        <div style="z-index:1;width: 365Px;height: 39Px;background: linear-gradient(90deg, #25BFAB 0%, rgba(25,146,156,0.5) 100%);opacity: 0.3;">
        </div>
        <span style="z-index:2;margin-top:-38px;margin-left:10px;position:fixed;width: 78Px;opacity: 1;height: 18Px;font-size: 18Px;font-family: Source Han Sans CN;
        font-weight: 500;color: #FFFFFF;line-height: 38Px;text-shadow: 0Px 2Px 8Px rgba(5,28,55,0.42);">重保团队</span>
        <span style="margin-top:-32px;margin-left:313px;position:fixed;width: 50px;height: 14px;font-size: 14px;font-family: Source Han Sans CN;font-weight: 500;color: #FFFFFF;line-height: 29px;">详情 ></span>
    </div>
    <div style="width: 126px;height: 14px;font-size: 14px;font-family: Source Han Sans CN;font-weight: 400;color: #FFFEFE;line-height: 28px;">
        <div style="width: 352px;height: 28px;background: #0E4950;margin: 7px 6px">
            <span style="width: 6Px;height: 6Px;margin-left: 10Px; background: #F19149;border-radius: 50%;display: inline-block;"></span>
            <span style="margin-left:3px;width: 41px;height: 14px;font-size: 14px;font-family: Source Han Sans CN;font-weight: 400;color: #30DFE9;line-height:28px;">哈哈哈</span>
            <span style="margin-left:13px;width: 41px;height: 14px;font-size: 14px;font-family: Source Han Sans CN;font-weight: 400;line-height:28px;">电话：</span>
            <span style="margin-left:-2px;width: 41px;height: 14px;font-size: 14px;font-family: Source Han Sans CN;font-weight: 400;color: #30DFE9;line-height:28px;">15529318421</span>
            <span style="margin-left:13px;width: 41px;height: 14px;font-size: 14px;font-family: Source Han Sans CN;font-weight: 400;line-height:28px;">专业：</span>
            <span style="width: 41px;height: 14px;font-size: 14px;font-family: Source Han Sans CN;font-weight: 400;color: #30DFE9;line-height:28px;">无线传输</span>
        </div>
        <div style="width: 352px;height: 28px;background: #0E4950;margin: 7px 6px">
            <span style="width: 6Px;height: 6Px;margin-left: 10Px; background: #F19149;border-radius: 50%;display: inline-block;"></span>
            <span style="margin-left:3px;width: 41px;height: 14px;font-size: 14px;font-family: Source Han Sans CN;font-weight: 400;color: #30DFE9;line-height:28px;">哈哈哈</span>
            <span style="margin-left:13px;width: 41px;height: 14px;font-size: 14px;font-family: Source Han Sans CN;font-weight: 400;line-height:28px;">电话：</span>
            <span style="margin-left:-2px;width: 41px;height: 14px;font-size: 14px;font-family: Source Han Sans CN;font-weight: 400;color: #30DFE9;line-height:28px;">15529318421</span>
            <span style="margin-left:13px;width: 41px;height: 14px;font-size: 14px;font-family: Source Han Sans CN;font-weight: 400;line-height:28px;">专业：</span>
            <span style="width: 41px;height: 14px;font-size: 14px;font-family: Source Han Sans CN;font-weight: 400;color: #30DFE9;line-height:28px;">无线传输</span>
        </div>
        <div style="width: 352px;height: 28px;background: #0E4950;margin: 7px 6px">
            <span style="width: 6Px;height: 6Px;margin-left: 10Px; background: #F19149;border-radius: 50%;display: inline-block;"></span>
            <span style="margin-left:3px;width: 41px;height: 14px;font-size: 14px;font-family: Source Han Sans CN;font-weight: 400;color: #30DFE9;line-height:28px;">哈哈哈</span>
            <span style="margin-left:13px;width: 41px;height: 14px;font-size: 14px;font-family: Source Han Sans CN;font-weight: 400;line-height:28px;">电话：</span>
            <span style="margin-left:-2px;width: 41px;height: 14px;font-size: 14px;font-family: Source Han Sans CN;font-weight: 400;color: #30DFE9;line-height:28px;">15529318421</span>
            <span style="margin-left:13px;width: 41px;height: 14px;font-size: 14px;font-family: Source Han Sans CN;font-weight: 400;line-height:28px;">专业：</span>
            <span style="width: 41px;height: 14px;font-size: 14px;font-family: Source Han Sans CN;font-weight: 400;color: #30DFE9;line-height:28px;">无线传输</span>
        </div>
        <div style="width: 352px;height: 28px;background: #0E4950;margin: 7px 6px">
            <span style="width: 6Px;height: 6Px;margin-left: 10Px; background: #F19149;border-radius: 50%;display: inline-block;"></span>
            <span style="margin-left:3px;width: 41px;height: 14px;font-size: 14px;font-family: Source Han Sans CN;font-weight: 400;color: #30DFE9;line-height:28px;">哈哈哈</span>
            <span style="margin-left:13px;width: 41px;height: 14px;font-size: 14px;font-family: Source Han Sans CN;font-weight: 400;line-height:28px;">电话：</span>
            <span style="margin-left:-2px;width: 41px;height: 14px;font-size: 14px;font-family: Source Han Sans CN;font-weight: 400;color: #30DFE9;line-height:28px;">15529318421</span>
            <span style="margin-left:13px;width: 41px;height: 14px;font-size: 14px;font-family: Source Han Sans CN;font-weight: 400;line-height:28px;">专业：</span>
            <span style="width: 41px;height: 14px;font-size: 14px;font-family: Source Han Sans CN;font-weight: 400;color: #30DFE9;line-height:28px;">无线传输</span>
        </div>
    </div>
</div>
<div class="stDataDiv1">
    <span class="stDataSpan1">重保人数：13</span>
</div>
<div style="width: 100%;margin-left: 40%">
    <img src="http://***********/images/zb/real/sxlt.jpg" style="height: 90px;width: 170px">
</div>
<div>

</div>
<div style="text-align: center;width: 112px;height: 40px;font-size: 16px;font-family: Source Han Sans CN;font-weight: 500; color: #ffffff;">哈哈哈</div>
<script src="https://gis.10010.com:8219/dugis-baidu/baidumap/bmapgl/api/booter.js"></script>
<script src="https://gis.10010.com:8219/dugis-baidu/bmapgl/"></script>
<script src="https://mapv.baidu.com/gl/examples/static/common.js"></script>
<script src="https://mapv.baidu.com/build/mapv.min.js"></script>
<script src="https://unpkg.com/mapvgl@1.0.0-beta.168/dist/mapvgl.min.js"></script>
<script type="text/javascript" src="https://bj.bcebos.com/v1/mapopen/github/BMapGLLib/TrackAnimation/src/TrackAnimation.min.js"></script>
<script type="text/javascript">
    /*// 百度地图API功能
    var mp = new BMap.Map("allmap");
    var point = new BMap.Point(defaultCenterPoint[0], defaultCenterPoint[1]);
    mp.centerAndZoom(point, 10);
    mp.enableScrollWheelZoom(true);
    // 复杂的自定义覆盖物
    function ComplexCustomOverlay(point, text, mouseoverText) {
        this._point = point;
        this._text = text;
        this._overText = mouseoverText;
    }
    ComplexCustomOverlay.prototype = new BMap.Overlay();
    ComplexCustomOverlay.prototype.initialize = function (map) {
        this._map = map;
        var div = this._div = document.createElement("div");
        div.style.position = "absolute";
        div.style.zIndex = BMap.Overlay.getZIndex(this._point.lat);
        div.style.backgroundColor = "#EE5D5B";
        div.style.border = "1Px solid #BC3B3A";
        div.style.color = "white";
        div.style.height = "18Px";
        div.style.padding = "2Px";
        div.style.lineHeight = "18Px";
        div.style.whiteSpace = "nowrap";
        div.style.MozUserSelect = "none";
        div.style.fontSize = "12Px"
        var span = this._span = document.createElement("span");
        div.appendChild(span);
        span.appendChild(document.createTextNode(this._text));
        var that = this;

        var arrow = this._arrow = document.createElement("div");
        arrow.style.background = "url(../../common/images/label.png) no-repeat";
        arrow.style.position = "absolute";
        arrow.style.width = "11Px";
        arrow.style.height = "10Px";
        arrow.style.top = "22Px";
        arrow.style.left = "10Px";
        arrow.style.overflow = "hidden";
        div.appendChild(arrow);

        div.onmouseover = function () {
            this.style.backgroundColor = "#6BADCA";
            this.style.borderColor = "#0000ff";
            this.getElementsByTagName("span")[0].innerHTML = that._overText; // bca-disable-line
            arrow.style.backgroundPosition = "0Px -20Px";
        }

        div.onmouseout = function () {
            this.style.backgroundColor = "#EE5D5B";
            this.style.borderColor = "#BC3B3A";
            this.getElementsByTagName("span")[0].innerHTML = that._text; // bca-disable-line
            arrow.style.backgroundPosition = "0Px 0Px";
        }

        mp.getPanes().labelPane.appendChild(div);

        return div;
    }
    ComplexCustomOverlay.prototype.draw = function () {
        var map = this._map;
        var pixel = map.pointToOverlayPixel(this._point);
        this._div.style.left = pixel.x - parseInt(this._arrow.style.left) + "Px";
        this._div.style.top = pixel.y - 30 + "Px";
    }
    var txt = "银湖海岸城", mouseoverTxt = txt + " " + parseInt(Math.random() * 1000, 10) + "套";

    var myCompOverlay = new ComplexCustomOverlay(point, "银湖海岸城", mouseoverTxt);

    mp.addOverlay(myCompOverlay);*/

</script>
</body>
</html>
