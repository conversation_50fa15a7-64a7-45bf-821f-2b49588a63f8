<template>
  <div
    id="tag"
    class="tag"
    :class="tagChang"
    style="padding-left: 5px;"
  >
    <!-- <el-tag :class="tagDarkItem" style="float: left">数字化运营大屏 </el-tag> -->
    <el-tag
      v-for="(tag, index) in tags"
      :key="tag.id"
      :class="tagDarkItem"
      :closable="tag.name !== 'home'"
      :disable-transitions="false"
      :effect="$route.name === tag.name ? 'dark' : 'plain'"
      style="float: left"
      @close="handleClose(index, tag)"
      @click="clickTag(index, tag)"
    >
      {{ tag.name }}
    </el-tag>
    <!-- <el-button
      type="primary"
      size="small"
      @click="permissions = true"
    >
      权限设置
    </el-button> -->
    <el-dialog
      title="权限设置"
      :visible.sync="permissions"
      width="40%"
      left
    >
      <el-cascader-panel
        :options="permissionsList"
        clearable
        :multiple="true"
        :props="{
          value: 'id',
          label: 'menuName',
          children: 'children',
          multiple: 'true',
        }"
        style="border: none"
      />
      <span
        slot="footer"
        class="dialog-footer"
      >
        <el-button @click="permissions = false">取 消</el-button>
        <el-button
          type="primary"
          @click="permissions = false"
        >确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { menuTreeTableData } from "@/apis/api-login";
import loginVue from "./login.vue";
import bus from "@/eventBus/bus.js";
import { onBeforeRouteLeave } from "vue-router";
export default {
  data() {
    return {
      // tags: [{
      //   jumpType: "0",
      //   menuId: "1163",
      //   name: "数字化运营大屏",
      //   path: "/DataSys/platHome"
      // }],
      tags: [{
        jumpType: "0",
        menuId: "1163",
        name: "数字化运营大屏",
        path: "/DataSys/platHome"
      }
      ],
      permissions: false,
      props: { multiple: true },
      permissionsList: [],
      tagChang: "tagDark",
      tagDarkItem: "tagDarkel-tag"
    };
  },
  watch: {
    $route(to, from) {
      this.getThisPageActive();
    },

    // $route(to, from) {

    //   // console.log(from.path);//从哪来
    //   // console.log(to.path);//到哪去
    // }
  },
  mounted() {
    //第二种方法
    if (window.performance.navigation.type == 1) {

      // console.log("页面被刷新")
      // console.log(this.$route);
      if (this.$route.path != "/DataSys/platHome") {
        this.tags.push({
          name: this.$route.name,
          // path: currentPgae.path,
          path: this.$route.path,
          // jumpType: this.$route.jumpType,
          // menuId: this.$route.menuId,
        });
      }
      if (this.$route.path != "/DataSys/platHome") {
        this.tagChang = "";
        this.tagDarkItem = "tagDarkel-tagC";
      } else {
        this.tagChang = "tagDark";
        this.tagDarkItem = "tagDarkel-tag";
      }
    } else {
      // console.log("首次被加载")
    }
    bus.$on("routerTag", (msg) => {
      this.getThisPage(msg);
    });
    // console.log(this.tags);
    bus.$on("resCharts", (msg) => {
      // console.log(msg);
      this.resCharts = msg;
      if (this.resCharts.seriesName == "3G未关联放置点基站") {
        // console.log(1);
        this.tags.push({
          name: "3G基站配置核查",
          // path: currentPgae.path,
          path: "/DataSys/baseStationVerification",
          jumpType: "0",
          menuId: 1195,
        });
      } else if (this.resCharts.seriesName == "4G未关联放置点基站") {
        this.tags.push({
          name: "4G基站配置核查",
          // path: currentPgae.path,
          path: "/DataSys/baseStationVerification",
          jumpType: "0",
          menuId: 1195,
        });

      }
      if (this.resCharts.seriesName == "3G未关联小区基站") {
        this.tags.push({
          name: "3G小区配置核查",
          // path: currentPgae.path,
          path: "/DataSys/communityVerification",
          jumpType: "0",
          menuId: 1194,
        });

      } else if (this.resCharts.seriesName == "4G未关联小区基站") {
        this.tags.push({
          name: "4G小区配置核查",
          // path: currentPgae.path,
          path: "/DataSys/communityVerification",
          jumpType: "0",
          menuId: 1194,
        });

      }
      if (this.resCharts.seriesName == "3G基站字段不完整") {
        // console.log(1);
        this.tags.push({
          name: "3G基站配置核查",
          // path: currentPgae.path,
          path: "/DataSys/baseStationVerification",
          jumpType: "0",
          menuId: 1195,
        });
      } else if (this.resCharts.seriesName == "4G基站字段不完整") {
        this.tags.push({
          name: "4G基站配置核查",
          // path: currentPgae.path,
          path: "/DataSys/baseStationVerification",
          jumpType: "0",
          menuId: 1195,
        });

      }
      if (this.resCharts.seriesName == "3G小区字段不完整") {
        this.tags.push({
          name: "3G小区配置核查",
          // path: currentPgae.path,
          path: "/DataSys/communityVerification",
          jumpType: "0",
          menuId: 1194,
        });

      } else if (this.resCharts.seriesName == "4G小区字段不完整") {
        this.tags.push({
          name: "4G小区配置核查",
          // path: currentPgae.path,
          path: "/DataSys/communityVerification",
          jumpType: "0",
          menuId: 1194,
        });
      }

      if (this.resCharts.seriesName == "3G基站放置点区域不一致") {
        // console.log(1);
        this.tags.push({
          name: "3G基站放置点区域不一致",
          // path: currentPgae.path,
          path: "/DataSys/drillDownTable",
          jumpType: "0",
          menuId: 1196,
        });
      }
      if (this.resCharts.seriesName == "4G基站放置点区域不一致") {
        // console.log(1);
        this.tags.push({
          name: "4G基站放置点区域不一致",
          // path: currentPgae.path,
          path: "/DataSys/drillDownTable",
          jumpType: "0",
          menuId: 1196,
        });

      }
      if (this.resCharts.seriesName == "铁塔局站数") {
        // console.log(1);
        this.tags.push({
          name: "铁塔局站数",
          // path: currentPgae.path,
          path: "/DataSys/drillDownTable",
          jumpType: "0",
          menuId: 1196,
        });
      }
      // 地址编码不一致数
      if (this.resCharts.seriesName == "地址编码不一致数") {
        // console.log(1);
        this.tags.push({
          name: "地址编码不一致数",
          // path: currentPgae.path,
          path: "/DataSys/drillDownTable",
          jumpType: "0",
          menuId: 1196,
        });
      }
      if (this.resCharts.seriesName == "3G基站") {
        // console.log(1);
        this.tags.push({
          name: "3G基站退网未删除",
          // path: currentPgae.path,
          path: "/DataSys/drillDownTable",
          jumpType: "0",
          menuId: 1196,
        });
      }
      if (this.resCharts.seriesName == "4G基站") {
        // console.log(1);
        this.tags.push({
          name: "4G基站退网未删除",
          // path: currentPgae.path,
          path: "/DataSys/drillDownTable",
          jumpType: "0",
          menuId: 1196,
        });
      }
      if (this.resCharts.seriesName == "3G小区") {
        // console.log(1);
        this.tags.push({
          name: "3G小区退网未删除",
          // path: currentPgae.path,
          path: "/DataSys/drillDownTable",
          jumpType: "0",
          menuId: 1196,
        });
      }
      if (this.resCharts.seriesName == "4G小区") {
        // console.log(1);
        this.tags.push({
          name: "4G小区退网未删除",
          // path: currentPgae.path,
          path: "/DataSys/drillDownTable",
          jumpType: "0",
          menuId: 1196,
        });
      }
      if (this.resCharts.seriesName == "新增4G基站字段不完整") {
        // console.log(1);
        this.tags.push({
          name: "新增4G基站字段不完整",
          // path: currentPgae.path,
          path: "/DataSys/drillDownTable",
          jumpType: "0",
          menuId: 1196,
        });
      }
      if (this.resCharts.seriesName == "新增4G小区字段不完整") {
        // console.log(1);
        this.tags.push({
          name: "新增4G小区字段不完整",
          // path: currentPgae.path,
          path: "/DataSys/drillDownTable",
          jumpType: "0",
          menuId: 1196,
        });
      }
      // if (this.resCharts.seriesName == "新增4G小区字段不完整") {
      //   // console.log(1);
      //   this.tags.push({
      //     name: "新增4G小区字段不完整",
      //     // path: currentPgae.path,
      //     path: "/DataSys/drillDownTable",
      //     jumpType: "0",
      //     menuId: 1196,
      //   });
      // }
    });
    this.getThisPage();
    window.addEventListener('beforeunload', e => this.beforeunloadFn(e));
  },


  methods: {
    beforeunloadFn(e) {
      console.log(e);
    },
    // 判断当前页
    async getThisPage(msg) {
      let currentPgae = this.$route;
      // console.log(msg.name);
      if (msg == undefined) {
        return;
      } else {
        // 判断tags里是否有当前页面
        var index = this.tags.findIndex((tag) => tag.name == msg.name);
        if (this.tags.length > 8) {
          // this.$message.warning("请先关闭标签, 不能在打开了");
          bus.$emit("length", this.tags.length);
          // return;
          this.tags[8] = {
            name: msg.name,
            // path: currentPgae.path,
            path: msg.path,
            jumpType: msg.jumpType,
            menuId: msg.menuId,
          };
          return;
        } else {
          if (msg.jumpType == "0" || msg.jumpType == "1") {
            if (index == -1) {
              //   this.tags.push({
              //     name: currentPgae.name,
              //     path: currentPgae.path
              //   });

              if (msg == undefined) {
                this.tags.push({
                  name: currentPgae.name,
                  // path: currentPgae.path,
                  path: currentPgae.path,
                  jumpType: 0,
                  menuId: 1159,
                });
                // console.log(tag.name);
                // this.active = this.tags.findIndex((tag) => tag.name == " 首页 ");
              } else {
                this.tags.push({
                  name: msg.name,
                  // path: currentPgae.path,
                  path: msg.path,
                  jumpType: msg.jumpType,
                  menuId: msg.menuId,
                });
              }
              this.active = this.tags.findIndex(
                (msg) => msg.name == currentPgae.name
              );
            }
          }
        }
      }


      const res = await menuTreeTableData();
      var jsondata = JSON.parse(
        JSON.stringify(res.data.data.menuList).replace(
          /\"children"\:\[]/g,
          '"no":0'
        )
      );
      this.permissionsList = jsondata;
      // 当前选择页
    },
    // 判断当前页
    getThisPageActive() {
      let currentPgae = this.$route;
      // 判断tags里是否有当前页面
      if (currentPgae.fullPath == "/DataSys/platHome") {
        this.tagChang = "tagDark";
        this.tagDarkItem = "tagDarkel-tag";

      } else {
        this.tagChang = "";
        this.tagDarkItem = "tagDarkel-tagC";
      }

      // var index = this.tags.findIndex((tag) => tag.name == msg.name);
      this.active = currentPgae.name;

      // 当前选择页
    },
    // 关闭标签
    handleClose(index, tag) {
      // if (index == this.tags.length) {
      //   this.active = index - 1;
      //   this.$router.push(this.tags[index - 1].path);
      // } else {
      //   this.$router.push(this.tags[index].path);
      // }
      if (this.tags.length <= 1) {
        this.$message.warning("这是最后一页, 不能在关闭了");
        return;
      } else {
        this.tags.splice(this.tags.indexOf(tag), 1);
        if (index == this.tags.length) {
          if (this.tags[index - 1].jumpType == 1) {
            sessionStorage.setItem("path", tag.path);
            this.$router.replace({
              path: "externalSystemsTy" + this.tags[index - 1].menuId,
            });
          } else {
            this.active = index - 1;
            this.$router.replace(this.tags[index - 1].path);
          }
        } else {
          if (this.tags[index].jumpType == 1) {
            this.$router.push({
              name: "externalSystemsTy",
              params: { iframeData: tag.path },
            });
          } else {
            this.active = index - 1;
            this.$router.replace(this.tags[index].path);
          }
          // this.$router.push(this.tags[index].path);
        }
      }
    },
    // 点击标签
    clickTag(index, tag) {
      // console.log(index, tag);
      this.active = index;
      // this.$router.push(tag.path);
      if (tag.jumpType == "1") {
        sessionStorage.setItem("path", tag.path);
        this.$router.replace({
          path: "externalSystemsTy" + tag.menuId,
          // params: { iframeData: tag.path },
        });
      } else {
        this.$router.push(tag.path);
      }
    },
  },
};
</script>

<style lang="scss" scoped>
// .tag{
//     height: 5.25rem ;
//     background: #fff;
//     padding-top: 8px;
// }
//  .el-tag + .el-tag {
//     margin-left: 10px;
//     // background: rgba(153,153,153,.2);
//     // color: #000;
//     // border: none;

//   }
.tag {
  height: 4.25rem;
  background: #fff;
  padding-top: 8px;
}

.tagDark {
  height: 4.25rem;
  background: #0c2134;
  padding-top: 8px;
}

.tagDarkel-tag {
  color: #fff;
  background-color: transparent;
}

.tagDarkel-tagC {
  color: #333333 !important;
  background-color: transparent;
}

/deep/.tag .el-tag:nth-child(1) .el-tag__close {
  display: none;
}

.el-tag+.el-tag {
  margin-left: 10px;
  // background: rgba(153,153,153,.2);
  // color: #000;
  // border: none;
}

/deep/.el-tag--dark {
  // width: 100px;
  height: 32px;
  //height: 3.2rem;
  background: rgba(153, 153, 153, 0.2);
  /*  background: #dcdfe6;*/
  // background: url("../assets/hisBg.png") center center no-repeat;
  background-size: 100%;
  // color: #fff;
  border: none;
  line-height: 30px;
  font-weight: 600;
  padding-top: 1px;
  // border-style: solid;
  // border-color: transparent;
  // border-width: 22px 0 22px 22px;
  // border-right-color: skyblue;
}

/deep/.el-tag--dark .el-tag__close {
  color: #333333;
}

/deep/.el-tag--plain {
  border-color: #dcdfe6;
}
</style>
