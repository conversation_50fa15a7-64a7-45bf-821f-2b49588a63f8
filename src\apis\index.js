// 入口文件
import {
  request
} from "./core.js"; //获取request函数
import API from "./config.js"; //引入API模块
const APIShow = {
  // 登录请求封装(方法)
  //   login(username, password) {
  //第一个参数: 请求方式
  //第二个参数：请求的API（网址）
  //第三个参数：请求是携带的参数
  //     return request(API.METHODS.GET, API.PATH.LOGIN, {
  //       params: {
  //         mobile: String(username),
  //         pwd: Number(password)
  //       }
  //     })
  //   },
  getList (data) {
    //   第一个参数: 请求方式
    //   第二个参数：请求的API（网址）
    //   第三个参数：请求是携带的参数
    return request(API.METHODS.POST, API.PATH.getList,
      data
    );
  },
  appenddevice (data) {
    //   第一个参数: 请求方式
    //   第二个参数：请求的API（网址）
    //   第三个参数：请求是携带的参数
    return request(API.METHODS.POST, API.PATH.appenddevice,
      data
    );
  },
  editdevice (data) {
    //   第一个参数: 请求方式
    //   第二个参数：请求的API（网址）
    //   第三个参数：请求是携带的参数
    return request(API.METHODS.POST, API.PATH.editdevice,
      data
    );
  },
  edgeDeviceLog (data) {
    //   第一个参数: 请求方式
    //   第二个参数：请求的API（网址）
    //   第三个参数：请求是携带的参数
    return request(API.METHODS.POST, API.PATH.edgeDeviceLog,
      data
    );
  },
  deleteEdgeDevice (data) {
    //   第一个参数: 请求方式
    //   第二个参数：请求的API（网址）
    //   第三个参数：请求是携带的参数
    return request(API.METHODS.POST, API.PATH.deleteEdgeDevice, {
      id: data
    });
  },
  dimensioncity (data) {
    return request(API.METHODS.POST, API.PATH.dimensioncity, {
      //  data:data,       
    });
  },
  dataDictionary (data) {
    // console.log(data)
    return request(API.METHODS.POST, API.PATH.dataDictionary, {
      //  data:data,  
      //  type: data,
      // type=1，充值方式       type=2，充值渠道      type=3，充电消费订单状态
      // type=4，充值订单状态   type=5，转账订单状态   type=6，转账类型
      // type=7，手机型号       type=8，APP版本       type=9，设备状态  type=10，日志类型
    });
  },
  chargePoint (data) {
    return request(API.METHODS.POST, API.PATH.chargePoint, {
      cityCode: data,
    });
  },
  chargingPost (data) {
    return request(API.METHODS.POST, API.PATH.chargingPost, {
        stationId: data,
    });
  }
};

export default APIShow; //将这个文件作为对象抛出去
