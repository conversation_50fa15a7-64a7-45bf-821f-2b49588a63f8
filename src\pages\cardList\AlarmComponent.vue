<template>
  <div class="alarm-container">
    <div
      class="alarm-item"
      @click="viewAlarmDetail"
    >
      <div class="mapRightTop1Img1" />
      <div class="mapLeftTop2Text">
        当前告警数量
      </div>
      <div class="mapLeftTop2Text2">
        <span style="font-weight: 500;color: #FF5656;">{{ data.alarmNum || 0 }}</span>
      </div>
    </div>
  </div>
</template>
  
  <script>
  export default {
    props: {
      data: {
        type: Object,
        default: () => ({})
      }
    },
    methods: {
      viewAlarmDetail() {
        console.log('查看告警详情');
        const data = {
          // type:'数通设备',
          message: '告警',
        };
        this.$emit('customEvent', data);
        // 实际项目中这里可能会打开详情弹窗或跳转页面
      }
    }
  };
  </script>
  
  <style scoped>
  .alarm-container {
    height: 75%;
    display: flex;
    align-items: center;
  }
  
  .alarm-item {
    display: flex;
    align-items: center;
    width: 100%;
    cursor: pointer;
    padding: 16px;
    border-radius: 4px;
    transition: background-color 0.2s;
  }
  
  .alarm-item:hover {
    background-color: rgba(255, 255, 255, 0.05);
  }
  
  .mapRightTop1Img1 {
    width: 32px;
    height: 32px;
    margin-right: 16px;
  }
  
  .mapLeftTop2Text {
    flex: 1;
    color: rgba(255, 255, 255, 0.8);
  }
  
  .alarm-count {
    text-align: right;
    font-size: 18px;
  }
  .mapRightTop1Img1 {
  /* margin-top: 17Px;
  margin-left: 16Px; */
  width: 66Px;
  height: 66Px;
  background-image: url('../../assets/zxx/mapRightTop1Img1.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
  display: inline-block;
}
.mapLeftTop2Text {
  /* margin-top: 41Px;
  margin-left: 45Px; */
  width: 75Px;
  font-size: 16Px;
  font-family: Source Han Sans CN;
  font-weight: 400;
  color: #FFFFFF;
  /* position: absolute; */
  display: inline-block;
}
.mapLeftTop2Text2 {
  /* position: absolute;
  margin-left: 200Px;
  margin-top: 27Px; */
  width: 62Px;
  height: 50Px;
  font-size: 28Px;
  font-family: D-DIN-PRO;
  font-weight: 500;
  color: #FABB61;
  line-height: 50Px;
  display: inline-block;
}
  </style>
  