<template>
  <div class="not-found">
    <img
      src="../assets/404.png"
      alt=""
    >
    <div class="tip">
      <el-button @click="back">
        返回
      </el-button>
      <el-button
        type="primary"
        plain
        @click="toHome"
      >
        去首页
      </el-button>
    </div>
  </div>
</template>

<script>
export default {
  name: "NotFound",
  methods: {
    back() {
      this.$router.go(-1);
    },
    toHome() {
      this.$router.push({
        path: "/",
      });
    },
  },
};
</script>

<style scoped>
.not-found {
  display: flex;
  align-items: center;
  flex-direction: column;
  justify-content: center;
  height: calc(100vh - 180px);
}

.tip {
  margin-top: 10px;
}
img {
  width: 320px;
  position: relative;
}
</style>
