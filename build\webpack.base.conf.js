'use strict'
const path = require('path')
const utils = require('./utils')
const webpack = require('webpack')
const config = require('../vue.config')
const vueLoaderConfig = require('./vue-loader.conf')
const {ESBuildPlugin, ESBuildMinifyPlugin} = require('esbuild-loader');
const {
    webpack
} = require('webpack')

function resolve(dir) {
    return path.join(__dirname, '..', dir)
}



module.exports = {
    context: path.resolve(__dirname, '../'),
    entry: {
        app: ["babel-polyfill", "./src/main.js"]
    },
    optimization: {
        runtimeChunk: true,
        minimizer: [
          new ESBuildMinifyPlugin({
                    target: 'es2015',
                    minifyWhitespace: true
                }),
        ],
      },
    // 增加一个plugins
    plugins: [
     new ESBuildPlugin(),
        new webpack.ProvidePlugin({
            $: "jquery",
            jQuery: "jquery"
        })
    ],
    output: {
        path: config.build.assetsRoot,
        filename: '[name].js',
        publicPath: process.env.NODE_ENV === 'production' ?
            config.build.assetsPublicPath :
            config.dev.assetsPublicPath
    },
    resolve: {
        extensions: ['.js', '.vue', '.json'],
        alias: {
            'vue$': 'vue/dist/vue.esm.js',
            '@': resolve('src'),
        }
    },
    module: {
        rules: [{
                test: /\.vue$/,
                loader: 'vue-loader',
                options: vueLoaderConfig
            },
            {
                test: /\.js$/,
                loader: 'esbuild-loader',
                options: {
                  loader: 'jsx',
                  target: 'es2015'  // Syntax to compile to (see options below for possible values)
                },
                include: [resolve('src'), resolve('test'), resolve('node_modules/webpack-dev-server/client'), resolve('/node_modules/_vuedraggable@2.15.0@vuedraggable/dist'),
                    resolve('/node_modules/element-ui/src'), resolve('/node_modules/element-ui/packages/input/src'), resolve('/node_modules/_sortablejs@1.7.0@sortablejs'),
                    resolve('/node_modules/vue-drag-zone')
                ]
            },
            {
                test: /\.(png|jpe?g|gif|svg)(\?.*)?$/,
                loader: 'url-loader',
                options: {
                    limit: 10000,
                    name: utils.assetsPath('img/[name].[hash:7].[ext]')
                }
            },
            {
                test: /\.(mp4|webm|ogg|mp3|wav|flac|aac)(\?.*)?$/,
                loader: 'url-loader',
                options: {
                    limit: 10000,
                    name: utils.assetsPath('media/[name].[hash:7].[ext]')
                }
            },
            {
                test: /\.(woff2?|eot|ttf|otf)(\?.*)?$/,
                loader: 'url-loader',
                options: {
                    limit: 10000,
                    name: utils.assetsPath('fonts/[name].[hash:7].[ext]')
                }
            },
            // {
            //   test:/\.scss$/,
            //   loaders:['style','css','sass']
            // }
        ]
    },
    node: {
        // prevent webpack from injecting useless setImmediate polyfill because Vue
        // source contains it (although only uses it if it's native).
        setImmediate: false,
        // prevent webpack from injecting mocks to Node native modules
        // that does not make sense for the client
        dgram: 'empty',
        fs: 'empty',
        net: 'empty',
        tls: 'empty',
        child_process: 'empty'
    },
    externals: {
        'AMap': 'AMap',
        'AMapUI': 'AMapUI'
    },
}