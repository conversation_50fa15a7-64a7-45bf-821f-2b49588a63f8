/*画布容器*/
#efContainer {
    position: relative;
    overflow: scroll;
    flex: 1;
  }
  
  /*顶部工具栏*/
  .ef-tooltar {
    padding-left: 10px;
    box-sizing: border-box;
    height: 42px;
    line-height: 42px;
    z-index: 3;
    border-bottom: 1px solid #dadce0;
  }
  
  .jtk-overlay {
    cursor: pointer;
    color: #4a4a4a;
  }
  
  /*节点菜单*/
  .ef-node-pmenu {
    cursor: pointer;
    height: 32px;
    line-height: 32px;
    width: 225px;
    display: block;
    font-weight: bold;
    color: #4a4a4a;
    padding-left: 5px;
  }
  
  .ef-node-pmenu:hover {
    background-color: #e0e0e0;
  }
  
  .ef-node-menu-li {
    color: #565758;
    width: 150px;
    border: 1px dashed #e0e3e7;
    margin: 5px 0 5px 0;
    padding: 5px;
    border-radius: 5px;
    padding-left: 8px;
  }
  
  .ef-node-menu-li:hover {
    /* 设置移动样式*/
    cursor: move;
    background-color: #f0f7ff;
    border: 1px dashed #1879ff;
    border-left: 4px solid #1879ff;
    padding-left: 5px;
  }
  
  .ef-node-menu-ul {
    list-style: none;
    padding-left: 20px;
  }
  
  /*节点的最外层容器*/
  .ef-node-container {
    position: absolute;
    display: flex;
    width: 200px;
    height: 192px;
    /* border: 1px solid #e0e3e7; */
    /* border-radius: 5px; */
    background-color: transparent;
    flex-direction: column;
    flex-wrap: nowrap;
    align-content: center;
    justify-content: center;
    align-items: center;
  }
  
  .ef-node-container:hover {
    /* 设置移动样式*/
    cursor: move;
    /* background-color: #f0f7ff; */
    /*box-shadow: #1879FF 0px 0px 12px 0px;*/
    /* background-color: #f0f7ff; */
    /* border: 1px dashed #1879ff; */
  }
  
  /*节点激活样式*/
  .ef-node-active {
    /* background-color: #f0f7ff; */
    /*box-shadow: #1879FF 0px 0px 12px 0px;*/
    /* background-color: #f0f7ff; */
    /* border: 1px solid #1879ff; */
  }
  
  /*节点左侧的竖线*/
  .ef-node-left {
    width: 4px;
    background-color: #1879ff;
    border-radius: 4px 0 0 4px;
  }
  
  /*节点左侧的图标*/
  .ef-node-left-ico {
    line-height: 32px;
    margin-left: 8px;
  }
  
  .ef-node-left-ico:hover {
    /* 设置拖拽的样式 */
    cursor: crosshair;
  }
  
  /*节点显示的文字*/
  .ef-node-text {
    color: #565758;
    font-size: 12px;
    line-height: 17px;
    margin-left: 8px;
    width: 200px;
    white-space: break-spaces;
    overflow: hidden;
    text-overflow: ellipsis;
    text-align: left;
    /* color: #fff; */
  }
  
  /*节点右侧的图标*/
  .ef-node-right-ico {
    line-height: 32px;
    position: absolute;
    right: 5px;
    color: #84cf65;
    cursor: default;
  }
  
  /*节点的几种状态样式*/
  .el-node-state-success {
    line-height: 32px;
    position: absolute;
    right: 5px;
    color: #84cf65;
    cursor: default;
  }
  
  .el-node-state-error {
    line-height: 32px;
    position: absolute;
    right: 5px;
    color: #f56c6c;
    cursor: default;
  }
  
  .el-node-state-warning {
    line-height: 32px;
    position: absolute;
    right: 5px;
    color: #e6a23c;
    cursor: default;
  }
  
  .el-node-state-running {
    line-height: 32px;
    position: absolute;
    right: 5px;
    color: #84cf65;
    cursor: default;
  }
  
  /*node-form*/
  .ef-node-form-header {
    height: 32px;
    border-top: 1px solid #dce3e8;
    border-bottom: 1px solid #dce3e8;
    background: #f1f3f4;
    color: #000;
    line-height: 32px;
    padding-left: 12px;
    font-size: 14px;
  }
  
  .ef-node-form-body {
    margin-top: 10px;
    padding-right: 10px;
    padding-bottom: 20px;
  }
  
  /* 连线中的label 样式*/
  .jtk-overlay.flowLabel:not(.aLabel) {
    padding: 4px 10px;
    /* color: #fff !important; */
    /* border: 0.1rem solid #e0e3e7; */
    /* border-radius: 0.5rem; */
    /* top: 178px !important; */
    min-height: 50px;
    min-width: 200px;
  }
  
  /* label 为空的样式 */
  .emptyFlowLabel {
  }
  
  .ef-dot {
    background-color: #1879ff;
    border-radius: 10px;
  }
  
  .ef-dot-hover {
    background-color: red;
  }
  
  .ef-rectangle {
    background-color: #1879ff;
  }
  
  .ef-rectangle-hover {
    background-color: red;
  }
  
  .ef-img {
  }
  
  .ef-img-hover {
  }
  
  .ef-drop-hover {
    border: 1px dashed #1879ff;
  }
  