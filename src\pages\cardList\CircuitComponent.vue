<template>
  <div class="circuit-container">
    <div class="fix_content">
      <div class="item-list">
        <div 
          v-for="(line, index) in data" 
          :key="index" 
          class="circuit-item"
        >
          <div class="circuit-header">
            <span class="mapLeftTop1Radis" />
            <span class="mapLeftTop1Text" style="margin-left: 5px;">{{ line.lineName || `${line.circuitStart}/${line.circuitEnd}` }}</span>
          </div>
            
          <div class="circuit-info">
            <div class="mapLeftTopText">
              专线号 
              <span 
                :class="{ 'text-alarm': line.lineState === 'alarm' || line.status === 'alarm' }"
              >{{ line.zxNum || line.circuitNo }}</span>
            </div>
              
            <div class="mapLeftTopText" style="margin: 0 20px;">
              <span>
                带宽 {{ line.bandWidth }}
              </span>
              <!-- <span v-if="line.busType === '线路'">
                告警数 {{ line.alarmNum || 0 }}
              </span> -->
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
  
  <script>
  export default {
    props: {
      data: {
        type: Array,
        default: () => []
      }
    }
  };
  </script>
  
  <style scoped>
  .circuit-container {
    height: 100%;
    overflow: auto;
  }
  
  .circuit-item {
    padding: 8px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  }
  
  .circuit-header {
    display: flex;
    align-items: center;
    margin-bottom: 4px;
  }
  
  .circuit-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: rgba(255, 255, 255, 0.8);
  }
  
  .circuit-no {
    flex: 1;
  }
  
  .text-alarm {
    color: #FF5656 !important;
  }
  .mapLeftTop1Radis {
    width: 8Px;
    height: 8Px;
    margin-left: 16Px;
    background: rgba(252, 164, 58, 1);
    border-radius: 50%;
    display: inline-block;
  }

  .mapLeftTop1Text {
    font-size: 16px;
    font-family: Source Han Sans CN;
    font-weight: 400;
    color: #FFFFFF;
    line-height: 50px;
  }
  .mapLeftTopText {
    font-size: 14px;
    font-family: Source Han Sans CN;
    font-weight: 400;
    color: #FFFFFF;
    margin-left: 20Px;
  }
  </style>
  