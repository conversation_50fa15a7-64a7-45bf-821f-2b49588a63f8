<template>
  <!-- AntTable 的列组件 -->
  <el-table-column
    :show-overflow-tooltip="showOverflowTooltip || column.showOverflowTooltip"
    :align="column.align || align"
    :label="column.label"
    v-bind="{ ...column }"
  >
    <template
      v-if="!column.children"
      slot-scope="{ row }"
    >
      <template v-if="column.slotRender">
        <slot
          :name="column.slotRender"
          :row="row"
          :column="column"
          :pagination="pagination"
          :value="row[column.prop]"
        />
      </template>
   <template v-else-if="column.formatter">{{ column.formatter(row, column, row[column.prop]) | emptyContent }}</template>
      <template v-else>{{ row[column.prop] | emptyContent }}</template>
    </template>
    <template v-if="column.children">
      <template v-for="(_column, idx) in column.children">
        <ant-column
          v-if="!_column.hidden"
          :key="idx"
          :pagination="pagination"
          :column="_column"
          :show-overflow-tooltip="
            showOverflowTooltip ||
              _column.showOverflowTooltip ||
              column.showOverflowTooltip
          "
          :align="_column.align || align"
        />
      </template>
    </template>
  </el-table-column>
</template>

<script>
export default {
  name: 'AntColumn',
  components: {},
  filters: {
    emptyContent (value) {
      return value || value === 0 ? value : '-';
    }
  },
  props: {
    column: {
      type: Object,
      default: () => {}
    },
    /** **表格溢出变点 */
    showOverflowTooltip: {
      type: [Boolean],
      default: false
    },
    /** **表格对齐方式 */
    align: {
      type: [String],
      default: 'left'
    },
    pagination: {
      type: Object,
      default: () => ({
        page: 1,
        pageSize: 10,
      }),
    },
  },
  data () {
    return {};
  }
};
</script>

<style scoped lang='scss'>
</style>
