import { request } from "./core.js";
import API from "./config.js"; //引入API模块
import Qs from 'qs';

let cityId = sessionStorage.getItem("cityId");
let cityName = sessionStorage.getItem("cityName");
let userGrade = sessionStorage.getItem("userGrade");

// menuTreeTableData 菜单权限
export function qryPermisionInfoData(params, config) {
  return request(API.METHODS.POST, API.PATH.qryPermisionInfo);
}

// menuTreeTableData 菜单权限
export function menuTreeTableData(params, config) {
  return request(API.METHODS.POST, API.PATH.menuTreeTableData);
}
