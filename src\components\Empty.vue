<template>
  <!-- 空状态 -->
  <div class="empty">
    <img
      width="80px"
      :src="require('@/assets/empty.png')"
      alt=""
    >
    <div class="h20" />
    <div style="color:#909399">
      暂无数据
    </div>
  </div>
</template>
 
<script>
export default {
  name: "Empty",
  components: {},
  data() {
    return {};
  },
};
</script>
 
<style scoped lang='scss'>
.empty {
  flex-direction: column;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>