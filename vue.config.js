
const os = require('os');
function getIPAdress () {
  var interfaces = os.networkInterfaces();
  for (var devName in interfaces) {
    var iface = interfaces[devName];
    for (var i = 0; i < iface.length; i++) {
      var alias = iface[i];
      if (alias.family === 'IPv4' && alias.address !== '*******' && !alias.internal) {
        return alias.address;
      }
    }
  }
}
const myHost = getIPAdress();
module.exports = {

  // 选项

  //  基本路径

  // publicPath: process.env.NODE_ENV === "production" ? "/portal/" : "/",
  publicPath: '/',
  //  构建时的输出目录

  outputDir: "zb-report-ui",

  //  放置静态资源的目录

  assetsDir: "static",

  //  html 的输出路径

  indexPath: "index.html",

  //文件名哈希

  filenameHashing: true,

  //用于多页配置，默认是 undefined



  //  是否在保存的时候使用 `eslint-loader` 进行检查。

  lintOnSave: true,

  //  是否使用带有浏览器内编译器的完整构建版本

  runtimeCompiler: false,

  //  babel-loader 默认会跳过 node_modules 依赖。

  transpileDependencies: [ /* string or regex */'chart.js'],

  //  是否为生产环境构建生成 source map？

  productionSourceMap: true,

  //  设置生成的 HTML 中 <link rel="stylesheet"> 和 <script> 标签的 crossorigin 属性。

  crossorigin: "",

  //  在生成的 HTML 中的 <link rel="stylesheet"> 和 <script> 标签上启用 Subresource Integrity (SRI)。

  integrity: false,

  //  调整内部的 webpack 配置

  configureWebpack:{
    module:{
      rules:[
        {
          test: /\.(gltf)$/,
          loader: 'url-loader'
        }
      ]
    }
  }, //(Object | Function)

  chainWebpack: () => { },

  // 配置 webpack-dev-server 行为。

  devServer: {

    open: process.platform === 'darwin',
    host: 'localhost',
    port: 8081,

    https: false,

    hotOnly: false,

    // 查阅 https://github.com/vuejs/vue-docs-zh-cn/blob/master/vue-cli/cli-service.md#配置代理

    proxy: {

//       "/protect-api": {
//         target: 'http://************:1092/', // 本地环境 zhangxx*
//         pathRewrite: {"/protect-api/": "/"}, // 本地环境 zhangxx*
//         // target: 'http://**************:8081',
//         // target:'http://***************:1092/', // 接口的域名 测试环境
//         // target: 'http://*************:8081/', // 接口的域名 测试环境

//         // target: 'http://************', // 接口的域名 生产环境
// /*        target: 'http://***************:1092', // 接口的域名 生产环境*/
//         // target: 'http://***********:8093', // 接口的域名
//         // target: "http://************:8092", // 接口的域名
//         secure: false, // 如果是https接口，需要配置这个参数
//         changeOrigin: true, // 如果接口跨域，需要进行这个参数配置
//         headers: { Referer: "" }
//       },
      '/protect-api': {
        // target: 'http://***********:1092',  // 测试环境 
        // target: 'http://************:1092', // 郭振ip  
        target: 'http://************:1092',
        pathRewrite: {"^/protect-api": "/"}
      },


    }, // string | Object

    before: app => { }

  },

  // CSS 相关选项

  css: {

    // 将组件内的 CSS 提取到一个单独的 CSS 文件 (只用在生产环境中)

    // 也可以是一个传递给 `extract-text-webpack-plugin` 的选项对象

    extract: true,

    // 是否开启 CSS source map？

    sourceMap: false,

    // 为预处理器的 loader 传递自定义选项。比如传递给

    // Css-loader 时，使用 `{ Css: { ... } }`。

    loaderOptions: {

      css: {

        // 这里的选项会传递给 css-loader

      },

      postcss: {

        // 这里的选项会传递给 postcss-loader

      }

    },

    // 为所有的 CSS 及其预处理文件开启 CSS Modules。

    // 这个选项不会影响 `*.vue` 文件。

    // requireModuleExtension: false

  },

  // 在生产环境下为 Babel 和 TypeScript 使用 `thread-loader`

  // 在多核机器下会默认开启。

  parallel: require('os').cpus().length > 1,

  // PWA 插件的选项。

  // 查阅 https://github.com/vuejs/vue-docs-zh-cn/blob/master/vue-cli-plugin-pwa/README.md

  pwa: {},

  // 三方插件的选项

  pluginOptions: {



  }

};
