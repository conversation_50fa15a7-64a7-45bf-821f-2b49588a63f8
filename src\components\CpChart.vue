<template>
  <div class="cp-chart">
    <div ref="myChart" :style="{ width: '100%', height: '100%' }" />
  </div>
</template>

<script>
import * as ECharts from "echarts/lib/echarts";
import merge from "lodash/merge";
import "echarts/lib/chart/line";
import "echarts/lib/chart/bar";
import "echarts/lib/chart/pie";
import "echarts/lib/component/polar";
import "echarts/lib/component/title";
import "echarts/lib/component/dataZoom";
import "echarts/lib/component/toolbox";
import "echarts/lib/component/tooltip";
import "echarts/lib/component/legend";
import "echarts/lib/component/legendScroll";
import "./chartsoption/light"; //主题四
import "./chartsoption/dark"; //主题一
import "./chartsoption/chalk"; //主题六大屏
import "./chartsoption/infographic"; //主题五
import "./chartsoption/macarons"; //主题七
import "./chartsoption/roma"; //主题八
import "./chartsoption/shine"; //主题九
import "./chartsoption/westeros"; //主题二
import "./chartsoption/wonderland"; //主题三
import "./chartsoption/customed"; //大屏
import "./chartsoption/walden"; //大屏
import bus from "@/eventBus/bus.js";
import { resizeObserver, remove } from "@/utils/util";
import _ from "lodash";
ECharts.__charts__ = [];
document.body.onclick = function () {
  const _charts = ECharts.__charts__;
  _charts.forEach((chart) => {
    chart?.myChart?.dispatchAction?.({
      type: "hideTip",
    });
  });
};
export default {
  name: "CpChart",
  props: {
    options: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      width: "",
      height: "",
      options_pro: {
        color: [],
        title: {
          textStyle: {
            fontSize: 15,
            fontWeight: "500",
            fontFamily: 'Arial',
          },
          // textAlign: left,// 水平对齐方式，默认根据x设置自动调整，可选为： left' | 'right' | 'center
          // subtextStyle: { // 副标题文本样式{"color": "#aaa"}
          //   fontFamily: 'Arial, Verdana, sans...',
          //   fontSize: 12,
          //   fontStyle: 'normal',
          //   fontWeight: 'normal',

          // }
        },
        tooltip: {
          textStyle: {

            align: 'left'
          }

        },
        grid: {
          left: "21",
          top: "60",
          bottom: "1%",
          right: "6%",
          containLabel: true,
        },
      },
      colorOption: "walden", //light,dark,chalk,infographic,macarons,roma,shine,westeros,wonderland,
      editBtn: true,
      dragLayout: false,
      editBtnDrag: true,
    };
  },
  // 生命周期 - 创建完成（可以访问当前this实例）
  created() {
    bus.$on("option", (msg) => {
      this.colorOption = msg;
      this.setLigth(msg);
    });
    bus.$on("editBtn", (msg) => {
      this.editBtn = msg;
    });
    bus.$on("editBtnDrag", (msg) => {
      this.editBtnDrag = msg;
    });
    window.addEventListener("setItem", () => {
      // this.newVal = sessionStorage.getItem("watchStorage");
      this.colorOption = sessionStorage.getItem("color");
      this.setLigth(this.colorOption);
    });
  },
  watch: {
    options: {
      handler(options) {
        this.myChart.setOption(
          merge({}, this.options_pro, _.cloneDeep(options)),
          true
        );
      },
      deep: true,
    },
    editBtn: {
      handler(n, o) {
        console.log(n);
        this.editBtn = n;
      },
      deep: true, // 深度监听父组件传过来对象变化
    },
  },
  mounted() {
    ECharts.__charts__.push(this);
    this.init();
    // 监听外部变化重新渲染echarts
    this.ro = resizeObserver(this.$el, this.myChart.resize);
    // 组件销毁时销毁监听
    this.$once("hook:destroyed", () => {
      this.ro.unobserve(this.$el);
      remove(this, ECharts.__charts__);
    });
    this.colorOption = sessionStorage.getItem("color");
    this.setLigth(this.colorOption);
  },
  methods: {
    init() {
      this.allOptions = merge({}, this.options_pro, this.options);
      this.myChart = ECharts.init(this.$refs.myChart, "chalk");
      this.myChart.setOption(this.allOptions);
      this.initEvent();
      const self = this;

      this.myChart.on("click", function (param) {
        // console.log(self.editBtn);


        if (
          self.editBtn === false ||
          self.editBtn != "" ||
          self.editBtnDrag == false
        ) {
          bus.$emit("paramCharts", param); //这里根据param填写你的跳转逻辑
          bus.$emit("showOrHidden", "true"); //这里根据param填写你的跳转逻辑
          bus.$emit("serName", param.seriesName); //这里根据param填写你的跳转逻辑
        } else {
          bus.$emit("resCharts", param); //这里根据param填写你的跳转逻辑
        }
      });
    },
    setLigth(option) {
      this.myChart.dispose();
      this.myChart = ECharts.init(this.$refs.myChart, option);
      this.myChart.setOption(this.options);

      const self = this;

      this.myChart.on("click", function (param) {
        // console.log(param);
        self.editBtn = sessionStorage.getItem("editBtnDrag");
        // bus.$emit("resCharts", param); //这里根据param填写你的跳转逻辑
        if (self.editBtn === "false") {
          bus.$emit("paramCharts", param); //这里根据param填写你的跳转逻辑
          bus.$emit("showOrHidden", "true"); //这里根据param填写你的跳转逻辑
          bus.$emit("serName", param.seriesName); //这里根据param填写你的跳转逻辑
        } else {
          bus.$emit("resCharts", param); //这里根据param填写你的跳转逻辑
        }
      });

      this.myChart.on('timelinechanged', function (timeLineIndex) {
        // console.log(timeLineIndex);
        bus.$emit("timeLineIndex", timeLineIndex); //这里根据param填写你的跳转逻辑
      });

      // 监听外部变化重新渲染echarts
      this.ro = resizeObserver(this.$el, this.myChart.resize);
      // 组件销毁时销毁监听
      this.$once("hook:destroyed", () => {
        this.ro.unobserve(this.$el);
        remove(this, ECharts.__charts__);
      });
    },
    initEvent() {
      const events = Reflect.ownKeys(this.$listeners);
      events.forEach((event) => {
        this.myChart.on(event, (params) => {
          this.$emit(event, params);
          this.myChart.dispatchAction({
            type: "hideTip",
          });
        });
      });
    },
  },
};
</script>

<style scoped>
.cp-chart {
  width: 100%;
  height: 100%;
}
</style>
