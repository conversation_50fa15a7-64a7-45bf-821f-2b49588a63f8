<template>
  <div class="UserInfo">
    <el-col :span="17" style="padding-top: 0px; width: 100%;text-align: right;">
      <el-dropdown class="userInfoBox">
        <div class="userInfoTitle">
          <img src="../assets/slices/userImg.png" alt="" class="userInfoImg" />
          <p class="userInfoName">{{ username }}</p>
        </div>

        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item>
            <div @click="changePwd">
              <span class="iconfont icon-yonghuziliao-xianxing iconAlign" />

              修改密码
            </div>
          </el-dropdown-item>
          <el-dropdown-item>
            <div @click="logout()">
              <span class="iconfont icon-tuichu iconAlign" />
              安全退出
            </div>
          </el-dropdown-item>
          <el-dropdown-item v-show="homeMenuShow">
            <div @click="jumpHome()">
              <span class="iconfont iconqingli iconAlign" />
              返回首页
            </div>
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
    </el-col>
  </div>
</template>

<script>
import {  logout } from "@/apis/api-login";
export default {
  name: "",
  // import引入的组件需要注入到对象中才能使用
  components: {},
  data() {
    // 这里存放数据
    return {
      username: "",
      homeMenuShow: false,
    };
  },
  // 监听属性 类似于data概念
  computed: {},
  // 监控data中的数据变化
  watch: {},
  // 方法集合
  methods: {
    changePwd() {
      const _this = this;
      _this.dialogVisible = true;
       this.$router.push({ path: "/DataSys/pass", replace: true });
    },
    async logout() {
      // window.location.href = "/login";

      
      // if (res.success) {
        let res = await logout();
        if (res.data.code == 200) {
          this.$router.push({ path: "/login", replace: true });
          // }
          sessionStorage.clear();
        }
      
      
    },
    jumpHome() {
        this.$router.push({ path: "/home", replace: true });
    }
  },
  // 生命周期 - 创建完成（可以访问当前this实例）
  created() {},
  // 生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {
    if (this.$route.path !== "/home") {
        this.homeMenuShow = true;
    }
    this.username = sessionStorage.getItem("userName");
  },
  // 这里存放数据
  beforeCreate() {},
  // 生命周期 - 挂载之前
  beforeMount() {},
  // 生命周期 - 更新之前
  beforeUpdate() {},
  // 生命周期 - 更新之后
  updated() {},
  // 生命周期 - 销毁之前
  beforeDestroy() {},
  // 生命周期 - 销毁完成
  destroyed() {},
  // 如果页面有keep-alive缓存功能，这个函数会触发
  activated() {},
};
</script>
<style lang='scss' scoped>
.UserInfo {
  width: 100%;
  min-height: 70px;
  position: absolute;
  .el-dropdown {
    // color: ;
  }
  .userInfoBox {
    padding: 10px;
    .userInfoImg {
      width: 52px;
    }
    .userInfoTitle {
      display: flex;
      flex-direction: row;
      flex-wrap: nowrap;
      align-content: center;
      justify-content: flex-end;
      align-items: center;
      .userInfoName {
        padding-left: 10px;
      }
    }
  }
}
// @import url(); 引入公共css类
</style>