<template>
  <!-- 暗色Input -->
  <div>
    <el-input
      v-bind="$attrs"
      v-on="{ ...$listeners }"
    />
  </div>
</template>
 
<script>
export default {
  name: "InputDart",
  components: {},
};
</script>
 
<style  lang='scss'>
@import "./dart-theme";

.input-dart {
  position: relative;
  font-size: 14px;
  display: inline-block;
  width: 100%;
  padding: 4px;
  background-color: $--dart-input-wrap-backgrond;
  border: 1px solid $--dart-input-wrap-border-color;
  box-sizing: border-box;
  line-height: 27px;

  img {
    position: absolute;
    width: 7px;
    &.left-bottom {
      left: -1px;
      bottom: -1px;
    }
    &.left-top {
      left: -1px;
      top: -1px;
    }
    &.right-bottom {
      right: -1px;
      bottom: -1px;
    }
    &.right-top {
      right: -1px;
      top: -1px;
    }
  }
  ::-webkit-input-placeholder {
    color: $--icon-color !important;
    font-size: 15px;
    position: relative;
    bottom: 2px;
  }
  :-moz-placeholder {
    color: $--icon-color;
    font-size: 15px;
    position: relative;
    bottom: 2px;
  }
  ::-moz-placeholder {
    color: $--icon-color;
    font-size: 15px;
    position: relative;
    bottom: 2px;
  }
  :-ms-input-placeholder {
    color: $--icon-color;
    font-size: 15px;
    position: relative;
    bottom: 2px;
  }
  .el-input__inner {
    border-radius: 0px !important;
    box-sizing: border-box;
    border: 1px solid $--dart-backgrond !important;
    background-color: $--dart-input-backgrond !important;
    color: $--dart-input-text-color !important;
    font-size: 18px !important;
    font-family: 微软雅黑;
  }
}
</style>