<template>
    <div
            ref="node"
            :style="nodeContainerStyle"
            @click="clickNode"
            @mouseup="changeNodeSite"
            :class="nodeContainerClass"
    >
        <!-- 最左侧的那条竖线 -->
        <!-- <div class="ef-node-left"></div> -->
        <!-- 节点类型的图标 -->
        <div class="ef-node-left-ico flow-node-drag">
            <!-- <i :class="nodeIcoClass"></i> -->
            <img :src="normal" alt="">
        </div>
        <!-- 节点名称 -->
        <div class="ef-node-text" :show-overflow-tooltip="true">
            {{node.name}}
        </div>
        <!-- 节点状态图标 -->
        <!-- <div class="ef-node-right-ico">
            <i class="el-icon-circle-check el-node-state-success" v-show="node.state === 'success'"></i>
            <i class="el-icon-circle-close el-node-state-error" v-show="node.state === 'error'"></i>
            <i class="el-icon-warning-outline el-node-state-warning" v-show="node.state === 'warning'"></i>
            <i class="el-icon-loading el-node-state-running" v-show="node.state === 'running'"></i>
        </div> -->
    </div>
</template>

<script>
import '@/components/ef/circuitTopologyIndex.css';
    export default {
        props: {
            node: Object,
            activeElement: Object
        },
        data() {
            return {
                fault: 'data:image/png;base64,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',
                normal:'data:image/png;base64,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',
                warn:'data:image/png;base64,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',
            };
        },
        computed: {
            nodeContainerClass() {
                return {
                    'ef-node-container': true,
                    'ef-node-active': this.activeElement.type == 'node' ? this.activeElement.nodeId === this.node.id : false
                }
            },
            // 节点容器样式
            nodeContainerStyle() {
                return {
                    top: this.node.top,
                    left: this.node.left
                }
            },
            nodeIcoClass() {
                var nodeIcoClass = {}
                nodeIcoClass[this.node.ico] = true
                // 添加该class可以推拽连线出来，viewOnly 可以控制节点是否运行编辑
                nodeIcoClass['flow-node-drag'] = this.node.viewOnly ? false : true
                return nodeIcoClass
            }
        },
        methods: {
            // 点击节点
            clickNode() {
                this.$emit('clickNode', this.node.id)
            },
            // 鼠标移动后抬起
            changeNodeSite() {
                // 避免抖动
                if (this.node.left == this.$refs.node.style.left && this.node.top == this.$refs.node.style.top) {
                    return;
                }
                this.$emit('changeNodeSite', {
                    nodeId: this.node.id,
                    left: this.$refs.node.style.left,
                    top: this.$refs.node.style.top,
                })
            }
        }
    }
</script>
