<template>
    <!-- 网络拓扑图 -->
    <!-- <div class="topology"> -->
    <cp-chart :options="options_graph_topology" />
    <!-- </div> -->
</template>
<script>
import CpChart from "@/components/CpChart";
import $ from "jquery";
import {
    graphData,
    graphLinks,
    getEdgeNodes,
    getEdgeLines,
    graphEndNodes,
    graphEndLinks,
} from "../utils/data.js";
export default {
    name: "Topology",
    components: { CpChart },
    props: {
        UPFnum: [Number, String],
    },
    data() {
        return {
            options_graph_topology: {},
        };
    },
    mounted() {
        this.$nextTick(() => {
            this.drawGraph();
        });
    },
    methods: {
        drawGraph() {
            var self = this;

            var res = [
                {
                    city: "上海",
                    local: "长虹",
                },
            ];

            if (res.length > 1) {
                $("#graphEcharts").css("height", "640px");
            } else {
                $("#graphEcharts").css("height", "420px");
            }

            self.options_graph_topology = {
                animationDurationUpdate: 1500,
                animationEasingUpdate: "quinticInOut",
                series: [
                    {
                        type: "graph",
                        layout: "none",
                        symbolSize: 50,
                        itemStyle: {
                            color: "#aaaaff",
                        },
                        tooltip: {
                            show: false,
                        },
                        color: ["#1492F5"],
                        label: {
                            show: true, //是否显示标签。
                            position: "bottom", //标签的位置。['50%', '50%'] [x,y]   'inside'
                            textStyle: {
                                //标签的字体样式
                                color: "#fff", //字体颜色
                                fontStyle: "normal", //文字字体的风格 'normal'标准 'italic'斜体 'oblique' 倾斜
                                fontWeight: "bolder", //'normal'标准'bold'粗的'bolder'更粗的'lighter'更细的或100 | 200 | 300 | 400...
                                fontFamily: "sans-serif", //文字的字体系列
                                fontSize: 12, //字体大小
                            },
                        },
                        data: graphData(),
                        links: graphLinks(),
                        lineStyle: {
                            opacity: 0.9,
                            color: "#00F5FF",
                            width: 1.5,
                            curveness: 0,
                        },
                    },
                ],
            };

            //  动态 边缘节点框与点

            var edgeNodes = getEdgeNodes(2);

            self.options_graph_topology.series[0].data =
                self.options_graph_topology.series[0].data.concat(edgeNodes);

            // 返回的 边缘节点数量  目前最大支持数量为2
            if (res.length > 1) {
                var nodes = [];

                nodes = JSON.parse(JSON.stringify(edgeNodes));
                for (var j = 0; j < edgeNodes.length; j++) {
                    nodes[j].y -= -40;

                    if (nodes[j].id) {
                        nodes[j].id += "node";
                    } else {
                        nodes[j].name += "z";
                    }
                }

                self.options_graph_topology.series[0].data =
                    self.options_graph_topology.series[0].data.concat(nodes);
            }

            // 动态边缘节点line

            var edgeLines = getEdgeLines();

            self.options_graph_topology.series[0].links =
                self.options_graph_topology.series[0].links.concat(edgeLines);

            // 返回的 边缘节点数量  目前最大支持数量为2
            if (res.length > 1) {
                var nodeLines = [];

                nodeLines = JSON.parse(JSON.stringify(edgeLines));
                for (var j = 0; j < edgeLines.length; j++) {
                    nodeLines[j].source = edgeLines[j].source + "z";
                    nodeLines[j].target = edgeLines[j].target + "z";
                }

                nodeLines.push({
                    source: "STNtorz",
                    target: "STNtor",
                });

                self.options_graph_topology.series[0].links =
                    self.options_graph_topology.series[0].links.concat(
                        nodeLines
                    );
            }

            // 5G专线与物理专线节点node 与 line

            var endNodes = graphEndNodes("5g");
            var endLinks = graphEndLinks("5g1");

            self.options_graph_topology.series[0].data =
                self.options_graph_topology.series[0].data.concat(endNodes);
            // 返回的 5G专线与物理专线节点node 与 line  目前最大支持数量为2
            if (res.length > 1) {
                var endNodeList = [];

                endNodeList = JSON.parse(JSON.stringify(endNodes));
                for (var j = 0; j < endNodes.length; j++) {
                    endNodeList[j].y -= -40;

                    if (endNodeList[j].id) {
                        endNodeList[j].id += "node";
                    } else {
                        endNodeList[j].name += "z";
                    }
                }

                self.options_graph_topology.series[0].data =
                    self.options_graph_topology.series[0].data.concat(
                        endNodeList
                    );
            }

            self.options_graph_topology.series[0].links =
                self.options_graph_topology.series[0].links.concat(endLinks);
            if (res.length > 1) {
                var endNodeLines = [];

                endNodeLines = JSON.parse(JSON.stringify(endLinks));
                for (var j = 0; j < endLinks.length; j++) {
                    endNodeLines[j].source = endLinks[j].source + "z";
                    endNodeLines[j].target = endLinks[j].target + "z";
                }

                self.options_graph_topology.series[0].links =
                    self.options_graph_topology.series[0].links.concat(
                        endNodeLines
                    );
            }

            // echarts setOption

            // this.myChart = echarts.init(
            //     document.getElementById("graphEcharts")
            // );

            // this.myChart.setOption(option);

            // this.myChart.on("click", function (params) {
            //     console.log(params);

            //     self.$emit("tabChange", params);
            // });
        },
    },
};
</script>
<style scoped lang='scss'>
.topology {
    width: 100%;
    height: 100%;
}
</style>