<template>
  <div style="height: 100%">
    <div
      ref="circuitTopologyPanel"
      style="height: 100%"
      class="circuitTopologyPanel"
    />
    <el-dialog
      :visible.sync="dialogVisible"
      width="30%"
      top="20%"
    >
      <span><i
        class="el-icon-success"
        style="color: #67c23a"
      /> 保存成功</span>
      <span
        slot="footer"
        class="dialog-footer"
      >
        <el-button
          @click="save()"
        >确 定</el-button>
      </span>
    </el-dialog>
    <el-button
        v-if="zoom"
      size="mini"
      style="position: absolute;
    top: 15%;
    right: 2%;"
      @click="goBack()"
    >返回上一页</el-button>
    <el-button
        v-if="!zoom"
      circle
      icon="el-icon-search"
      size="mini"
      style="position: absolute;
    top: 40%;
    right: 2%;"
      @click="fullScreen()"
    />
  </div>
</template>

<script>
// import draggable from "vuedraggable";
// import { jsPlumb } from 'jsplumb'
// 使用修改后的jsplumb
import "./jsplumb";
import { easyFlowMixin } from "@/components/ef/mixins";
import flowNode from "@/components/ef/circuitTopologyNode";
import nodeMenu from "@/components/ef/node_menu";
import FlowInfo from "@/components/ef/info";
import FlowHelp from "@/components/ef/help";
import FlowNodeForm from "./node_form";
import lodash from "lodash";
import { getDataA } from "./data_A";
import { getDataB } from "./data_B";
import { getDataC } from "./data_C";
import { getDataD } from "./data_D";
import { getDataE } from "./data_E";
import { ForceDirected } from "./force-directed";
import {getRouteSeriesConnInfoAndAlarm, updateTransferCircuitConfig } from "@/apis/api-login";

import * as echarts from './echarts.min.js';

export default {
  components:{
  },
  props:{
    faMsg: Boolean
  },
  data() {
    return {
      zoom: 0,
      dialogVisible: false,
      // 控制画布销毁
      easyFlowVisible: true,
      myChart: {},
      links: [],
      data: [],
      saveData: [],
      // save: {
      //   circuitId: '',
      //   name: '',
      //   left: '',
      //   top: ''
      // },
      faMsgData: true,
    };
  },

// watch: { //监听父组件传过来的success 拓扑状态
//     faMsg: { 
//         handler(newVal,oldVal){ 
//           // console.log(newVal);
//             this.faMsgData = newVal; 
//         }, 
//         immediate:true,//immediate:true代表如果在 wacth 里声明了之后，就会立即先去执行里面的 handler方法，如果为 false，不会在绑定的时候就执行。 
//         deep:true//deep，默认值是 false，代表是否深度监听。 
//     }
// },
  created(){
    this.getRouteSeriesConnInfoAndAlarm();
  },
  mounted(){},

beforeRouteLeave(to, from, next) { 
      let aaa = this.saveData.filter(item=>{
          return item.success == false;
      });
      // 离开当前组件 判断拓扑是否发生改变
      if (aaa.length > 0) {
          // 发生改变 打开保存对话框
          this.dialogVisible = true;
      } else if (aaa.length == 0){
          // 未发生改变 直接跳转到新页面
          next(); 
      }
    },
  methods:{
    async getRouteSeriesConnInfoAndAlarm(circuitId) {
      this.params = {circuitId:this.$route.query.circuitId};
      this.zoom = this.$route.query.zoom;
      const res = await getRouteSeriesConnInfoAndAlarm({circuitId:this.$route.query.circuitId});
      let mapgetIDCRoomDetail = res.data.data;
      this.$nextTick(() => {
        // 默认加载流程A的数据、在这里可以根据具体的业务返回符合流程数据格式的数据即可
        let data = this.dataFormat(mapgetIDCRoomDetail);
              let x = [];
          for (let index = 0; index < data.nodeList.length; index++) {
            data.nodeList.forEach((value, i) => {
              x.push(value.x);
              if ([...new Set(x)]  == 0 || [...new Set(x)]  == "0px") {
                if (i<1){
                 value.x = 100;
                 value.y = 300;
                 value.left = 100;
                 value.top = 300;
               }
               let indexNum = i + 1;
               const s = parseInt(indexNum / 4);
               const l = indexNum % 4;
              
                            if(s%2==0) {
                 let XArr = [200,500,800,1200];
                 if (l<1) {
                   value.x = XArr[0];
                   value.left = XArr[0];
                 } else {
                   value.x = XArr[l-1];
                   value.left = XArr[l-1];
                 }
               } else {
                 let XArr = [1100,800,500,200];
                 // 商数是基数
                 if (l<1) {
                   value.x = XArr[0];
                   value.left = XArr[0];
                 } else {
                   value.x = XArr[l-1];
                   value.left = XArr[l-1];
                 }
               }
               
               if (l==0){
                 value.y = 300+50*(s-1);
                 value.top = 300+50*(s-1);
               } else {
                 value.y = 300+50*s;
                 value.top = 300+50*s;
               }

              }
              // 初始化拓扑状态
               value.success = this.faMsgData;
            });
          }
      this.saveData = data.nodeList;
      this.$emit('saveData',this.saveData);
        this.circuitTopologyPanel(data);
      });
    },
    // 保存
    async save (msg) {
      // 调用后台保存接口
      let res = await updateTransferCircuitConfig(this.saveData);
      // 关闭保存对话框
      this.dialogVisible = false;
      // 修改拓扑是否发生改变的状态
    //   this.success = true;
      let aaa = this.saveData.map(item=>{
          return item.success = true;
      });
      this.successData = aaa;
    },
    // 全屏
    fullScreen(){
          this.$router.push({ path: '/DataSys/circuitTopologyPanel', query: {zoom: 5,...this.$route.query} });
    },
    // 返回上一页
    goBack(){
      this.$router.push({ path: '/DataSys/circuitTopology', query: {circuitId: this.$route.query.circuitId,} });
    },

    // 数据处理
    dataFormat(data) {
      var obj = { name: "", nodeList: [], lineList: [] };
      var preNode = null;

      for (var i = 0; i < data.length; i++) {
        var item = data[i];
        var nodeA = {...this.params};
        nodeA["name"] = item["A_PORT_NAME"];
        nodeA["id"] = "A_" + i;
        nodeA["label"] = item["A_CTP_PORT_NO"];
        nodeA["x"] = item["NODE_LEFT"];
        nodeA["y"] = item["NODE_TOP"];
        nodeA["left"] = item["NODE_LEFT"];
        nodeA["top"] = item["NODE_TOP"];
        if (item["ICO"] == 'Normal') {
          nodeA["symbol"] = 'image://data:image/png;base64,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';
        } else if(item["ICO"] == 'Fault'){
          nodeA["symbol"] = "image://data:image/png;base64,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";
        } else if(item["ICO"] == 'Warn'){
          nodeA["symbol"] = 'image://data:image/png;base64,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';
        }

        var nodeZ = {};
        nodeZ["name"] = item["Z_PORT_NAME"];
        nodeZ["id"] = "A_" + i;
        nodeZ["label"] = item["Z_CTP_PORT_NO"];
        obj["nodeList"].push(nodeA);
        let labelPro = "";
        if (i == 0) {
          obj["lineList"].push({
            source: nodeA["id"],
            target: nodeZ["id"],
            value: nodeA["label"],
          });
        } else {
          if (preNode["label"] == undefined || preNode["label"] == null) {
            labelPro = nodeA["label"];
          } else {
            labelPro = preNode["label"];
          }
          let lineStyle = {};
          if (item["ICO"] == 'Normal') {
            lineStyle = {
              width: 2,
              curveness: 0,
              normal: {
                      // color: 'rgba(231, 60, 60, 1)',
                      color: '#178dfa',
                      opacity: 1
                  }
              };
            } else if(item["ICO"] == 'Fault' || item["ICO"] == 'Warn'){
              lineStyle = {
              width: 2,
              curveness: 0,
              normal: {
                      color: 'rgba(231, 60, 60, 1)',
                      // color: '#178dfa',
                      opacity: 1
                  }
              };
            }
          obj["lineList"].push({
            source: preNode["id"],
            target: nodeA["id"],
            value: labelPro,
            lineStyle : lineStyle
          });
          obj["lineList"].push({
            source: nodeA["id"],
            target: nodeZ["id"],
            value: nodeA["label"],
          });
        }
        obj["lineList"] = obj["lineList"].filter(item=>{
           return item["source"] !== item["target"];
        });
        

        // if (i == data.length - 1) {
        //   obj["nodeList"].push({
        //     id: "last",

        //     name: "",
        //   });
        //   obj["lineList"].push({
        //     from: nodeZ["id"],
        //     to: "last",
        //     label: labelPro,
        //   });
        // }
        preNode = nodeZ;
      }
      return obj;
    },
    // 拓扑图
    circuitTopologyPanel(data){
     const Fault = "image://data:image/png;base64,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";
     const Normal = 'image://data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAFUAAABJCAYAAABM4Wq6AAAAAXNSR0IArs4c6QAAGTZJREFUeF7VXAl0W+WV/v63aLEWy7IlWV5kO/u+EAghCSQNpJSSUih0WnZaaKdMZw50emiBQjdoO22Hljnt6XRKWabQMhA6ITQQpkBZQhISEgIJSZzdqyxvsi1Z1vb0/jlXerIlL5K8ZOk9xxBJ/3vvf9+79/vuvf8vMfydGOdcZIwl/h6my871SXLOSwH8G4AvA3gOwLcYYy3n8rzPWVA55wKA+wF8BwB5qArAAIDm/AsA32OMRc5FcM9JUDnntwD4OQArgGAGoDoNVAlASAP8PxljBPg5Y+cUqJzzSwE8AmCuBqaSgRR5KYGaNpq7DOAUgHsYYy+eK6ieE6ByzucD+BmATwLoBxAdBaDhoKaHEE0QuNs1cHedbXDPKqic8zIAPwVwqxbO4RyAjAVq+hARANHCJg1c8uCzYmcFVEqPND4kIaIQJ37keRDIB2r6cAKW7utXAL7LGCPPP6N2xkHlnJNXkgiZtVDPKzKKCrx4BJY1tWAOY8H4ECVQdvAgAXwmc9wzBirnfL0mQrNHEaExkdp6ArqHt8NysgeSSQf+T+dBuXMZFCP5Y35Li1kz5bcAXmCM5YuI/GfNM+K0g8o5X6B55mU5RGjENPf5IP1gGyy7vNDpRPDr5iD+dhPk1iBYuRn82xch/g9zkRAKu4O0mJGIUfHw7qSRy3GCwqY0gRlwzp2aCN1cgAgNXqElCOFH78K8+RgM4GAbZiLywCr0e4ohRxPQPbYP0q/2QApEweaVQX1wNeJra5KFQSGWFrO/aOAeLeSg8Y6ZclA55xSYxGPfBhAHMFCACIFAenQ3TE/sR1FUAbvAjdj3LkZwmTspZGQ0VxIryR8B+/f3ID/9MUQlAba2BonvrkZ8bllescsUM/Le/wJwH2MsMF7gco2fUlA551/SQr1oPCL05Ecw/nI3zD0RCLU2KPevRP+GmYhmTu5/DsLw6B6YyHtfvA7hcjPEkz1gD2+HvPUERKKBL8yD8u2ViLvo6oUZiRk9+O8T30+VmE0JqJzzyzURmjkeEXpFE6FTPZBsBqh3X4D+25cgLJEPDbNDnRBv3IySzjCEEj34v16IyG2LoTJA3N0K4fvbIO9rh1Akg3/tPChfXwaliCDLb2kx82nR9afJitmkQOWcL9I8c50GZiz/PQAfaCK0WxOh2xZh4JvLEbISi+awvgiYbwDCLZthawpAOvI1dFj1yYRfzzmEzUch/mQn5KY+MKcJ/J4ViF8/DwlxlIc0ymXSYvaBxrdvFnIvo42ZEKicc5dWVt44LhEKQHh4O8wvHYOB8yRH4raFGPj+GgT1JCEF2tUbUUJZwbZb0PXSERh2+yB/aRESn5wGhGJgTx+A9B97INFDmG2H+uDFiF9aO24x26qBe6jAaQ0OGxeonHMKqO9qOR95ZeEitAumJw4MidADq9H/oQ/yI7thsumhfv8S9F8xfajm5xx48B2YPVYkvrIEYabNlETqqudhP9EDid4i1zbroPbHICxyIm7WQXjhWkR7o2C/3AXpqf2Q4gmwi6uR+N7FiM93jEvM6FE/DuBexpi/UHALBpVzfrvmnVTTUOmXN42hSuiJj2B8NEOEvrMK/RtmDIHXFQajFOr5wzCurkbs4bUIzixBgt6/Ywts5JHnuxG7fBr4Li/4203QE0iMgX91CQaum4PInDIozx2C4YG3YY0oYE9tQN/l05MQyA19yRRN3nI8JWbXzoFy70ooFeaCwSVHon7uw9QsZ4xlds5GxTkvqJzzL2opEk2TwMx7UrrSy8eh/9F2mE/1QioxQL1rOfpvX5wSoZ/uhOndZuhmlEB5eC36TTL4h+2QvvMWLAc6k6E8cM+FCEGA+NgHsD32IcSeCGDVARdVg3/oAx9QgKNfQ2f6roJRsK0noSfPnmWHYjcmQSOe1BO4e9vAfvAu5Pe9EA1y8oEo/3I+FHNmM3FsV0yLWReJGWPsv3N5bV5QE4mETxAEu/a00g3jnJGQ4MDv9qLoF+/DRGG5xIXYz9chuMAJJZoAzvs9HP4IBFkEf/UL8M9zpB4UeefrJ6H/yU6YEyrYTQvAVlWBRRTgeA8wrwwg0bn7NSAUB993B7oMYv6IASAOxGH4xW7of7MHMqH9mZlI/OASxN3j81g69ASABbnSr7ygRqPR96PR6CGj0XiRJEnkrcSj1FXKa+0hCD/cBvOmIzAKAvhN8xG+byX6KQwPdEKaVoJEuSkFys92wvTrvTDduhDRVVXQbz4KtuU4MN0GfHkxMK1k6HKP7ALeawWm28HvWY6BDTMREoXRw5lQ2HQE+oe2weILQZxRAvXHaxG/2FPQw6CLpquwPi3Doei4cNKghsNhSjMgy3K5wWBYJQhC8XhSqPdaId//FiyHuyDbjVDvX4ngDfMRIfF5dDeK3myE/q7liL3ZANOzB8FMMnDvSoCo4qn9wD4fcGkdcP08wErBjNR7fzwIVJqBe1YiUWNFqNyMcKaX1HdBvP8tWHe2QlckQ73rAoTuXIawLCSb2hT4uZKtdIpFnS4SqbSGTC2oaV/R6/Xz9Hr9ci1JJkrIu3RMlPD4hzA+sgvmQBTC0nLEfrwWwZ4I9OTNR/3AFdNTf4e6gLUeIK34H/iAJ/cDwWiyagKlTiIDVA5QlpDOQ40ylLpiBHUC4j99D6an9qNIUVP9gx9egqDbPMI76RGl170yI49AJycnMIevQpweUOnqgiDoDQbDCkmSZjHGiA4KogTiTQrFjfUwMICtr0sBRZ73zMepAv+2RcBFVdnsEleBl48Bf64HnKYUJcx3ZI8hFLY3A88cBO8eAJtph/LjtQisrk6WomMZXTIpZtofhXs61Ec75vSBmr6aLMtOogTGmJ0xRl472vrS4OSiCQiNvTDv9MLw2D6wEz2ARQfcsABYVQn87xGAuHR+GXD7EsBNrewM84dT4G9rBi6qBG5ZCJQVAc0B4PGPgIOdAPVa6UHdshDhaSXo1+cXMwKUVm6psUKakStdPP2gZlDCHIPBsFzrJo2gBFLzliCKWoMwqVo1ReH7twbgTweBYAyYUZICkjiVuPRAB/DZWcA1swHdsIqrvgt4Yj/QGgTOKwfe9wJEMZdUAzctBEqon0URxcDdZgx4rKOKGfEmPTbKPj4G0A2A8nC39v7Z8dTMqzLGdEaj8UJZlqm7T4t4IaK99n4YGwMwxRNJJR1h/THg2UPAa7RUx4F1tcAN81NpFHEp8SaBvZSKY83ovXeagN9/BFDKVWNNjZlLS4mjmCxArbai320ZFDPqZRGAR7Rl7uFHWQCUa2MyPztznpp5VVEUy4qKilZxCGUHOsH7Y8mmR1471Qv8/kOARMssA1+cD6ytAbYeB148Ctx1AbC0HGjsS4oeDncD1ImiUL9cE698FymSoRAX60W0AzgIIFcTiPjWBoAeZ7pMODugdocF+2G/uMJulqrmOGS0hxgl9gUZeeBbTcAfPwb6okCdDbhjSSpfpcLhuUPAqydT3rvGA9y0ACjWQj3fBSgdsxkAXz8gC+ius+GQzZAUpXxG4FIMkDT2TDpPjcViewcGBvbkuyp93h9nRQe7xQu6I8IsjVuTueYClwyXRQJ5YrigIjdZMSUB/D8NwAsrgcNdKaBrCejFwGzaulaAER+Xm1K8TeVu2hjAiw3wTreh3ign6SqfEX15AXw111ajvBWVoig3qqr6j5FI5JiqqqNCEktAPuyXFreFhEVqakPDCLPqGc6rlKFwManUJFKFWGaok4BRAbB+WlKA8hoNKdeyh7Ycq/+MIeEwomGaHcckNmZvg1LGpwG8kq+JXcDUKNS4W1XVh+LxeFUkEmlM340KsKN+cU5zUFwW5yhoEcNjEzDPqUPHAEMnJS8FGIX6nraUZ6YrqnyH2fQpWiAwY3lLk9TZJIaY24xjHhsayIszrkFbin5XaPuvIFDTJ+ecX6YoyrcikUjXqV5uO94rrogkWEZVnu9WU59TBTTfKaGimCiBYSBXal7YKQdHpUM9EAV6c2bMY59YJyJUbUW924yPANCuwoLoL4NWxjfrDc/z5VfN4FuXOhT7Cb9ScBiPdhWLLkUJHCKaAqk8c6JG3kGFAp2ChGgSp0reE6Vrr5/CznAc39x9O9s5nnkV7KkXPsaruISHwXEzYxDoBr66RIVBiMMXzNuvzjmnqmIB8506dEUYOgoqdrNPN5FQH2tC9HBfOAx4hziYM+CFOMN9e29j1PbLa3lBnf04txQzasziG8BI3lxdDXx+dgK+QBwD8Yn7BzVI5jolVBVLaOhjSfXPZ7Su5TIDfZFUVjAZowJi6wlgR8voXs6BGAN+IyTw0M47ci+t5AW14oH655xVnsv0eiM1qkc1urmbFnAscSggSiBhmaiZiBIqZAhCihJoSWa4JUPdksog2icZ6nTu/R2p4oJ4OJcpHL09Yew7+XVGq8djWl5Qzf+84zWAr7a7KrrLKzylTBDGTLU9VuArS1QwNY7O0OQoocIqYKFLRndEQHsGJVBNTxnAeFR9rLunnHXTkVSrMZdxIN4XQWdfFMUcONF+N1s8FaDS5jKIkuSv8EyPW0tKM6rwkae/rBa4akYCrX2xZF0+UaNcdI5DQk2JBG8/S3azSNEp3Cdj5OHU5aLCIl+6FVbQSW3EBE9WVCSA+6cU1FTXmKHUVhx0VtaIgr5ozNy0SAJuXcQxp0TBKaKESaBQJDMsr9bhZO8YaybjODcVHhvrAS/10XK6pxoJRrnqj4rUdBmM6CkGlVrKqT9aHxYYS7icjoESV7UBgjjmBptkO2+xilgsDn944pRwXqUOneFx7LgYBhhFzKsngO1jCNHQcJ4YiKkD/ohgSHA24r6mBNSSu3e8FVf4mhSgQhLQ5IPTXhtlMVxeUZkwFpcNaydn39WVMzg+VZtAU288b8iN5kBLK3XomiCoBzqTO7HzZghxJTHQHQaLqknvHNVMOt5w/E52PmOMeq+j2phCFQqF3G1tPVdd9WTzfU19vCbtoQRsmgaSAGuvi03GUHlFNRP0xjEpgTjx9sUcNZY4GnoKrB21aU8E1N5IaiUhnxBBVSM9YZ4IKKIpH5NYpWj7k8vqn3JXlH1Q56ncqq12ZB02AtSenh5bTyC8obWlfWFHlz987w79zW0DwjSWAeZoXkuf07Kts9Q2UOIoN0CQxqSEeWUcX1rEEQzH0BcpjG2XVlBxUFj4kxC925xqEeYWIp4IRZOhblTBCur5WsVo+088238riiJzuxxSZbVzZ6Wr7A3G2GCXa4iAOS9q9rav97Z2rPD6umKcp7LNe3cYbm4LC9NYMuQzvVT7d5oWaLD2uUESIy6XQzEW25P7SUd7+vTm1bOBSz0KGvxx0MJeLisUVBKiF+pTyyy5LBXqjEVVofCvZtBClgZq+tySJImVbgevqXa/4XCUvJ2Egf7TwbnZd/DYN041tuoURc2Ky3t3Gm72DYjTCJt0uKf+r4E8+P7I1xaTPlTudDFBNzYlUN75lSUcTkMczX1jU8KSCh26c3gqCRGlSOShuXyfq2rEH+Fqf1wsqKs2/MEMBzX9ebHVbFi6aN7TpaWWg0lQu3p7123b9sFaVeUj/OXencYkqIMgEna0ByHZ0NTEK0O4UjqW5dGqw2YKlZSW6SFIY+5covWnmxck4A/FEYyOhCUXqB93ppL4nKUqhxKKJSLdEdHAx+j55uNT+twuhVseqt5JOwGzjDz2ogvmvuZ0Ot9Jgtrb2zv9wMGTd3Z09Yxo5W5t0s186ZRuQ1gVrGnVH8wABFqMJA8eooIsmqD0SwNeLwkRZ6ktbrAUm5PPZRSj+v/zczlWVyo46VeQyHjEo4FKQrTpaGpZOpfFFJVUXYipY1eD+QHlfLahZ+/Njvo3SqRIVvmh18vyrBme3pnTa35N3Dp4c36/f2FTS/c1za1eORZTsuIwoIjyE4cMa/b3yCtUzujHDAbDfyjF0tKt9GfkzVpOOyhsYDAb5ZCz1MbEHJTgLEpRglWOwRtIIbu4Qge/Fv6FChGFencYakgRJhTqg6EtRn3X2E9uucDc1poJviAIcLtK9dWVlW9WVNj/lt5mmeUxnHOdt73r8qbGtlVeX+eIYnC/X+d45rjxys6IVDPcQ1MApjx3qEgYep2ZfjEmqKUWfchus+m4IGq7o0b6yopK4ItzE+gIxDGjTIY/KqJFE6KWXELEudIfUaP+uGDgnBWWMoziqhLU2Pnm9reuL63fJQnZ1Gi1mvQ11e4Gl8O2sbi4OGtD8KhhGAxyh6+j+bqGJq8nEAhl9W7Ib144ZVr8pte4PsoF0yCvZorXYNgPZQyj0YJOQtRRYo4Zi8zmoZ1T2XcnC7QHgCf3T/32A5ZXiKJxdcAfhhjjwpgPK3+oA5W6/iM3lNZvrTUEslZbZVmSPFWuqLvKudllt+8f7Vw5u1Q9PT2LG5s7r2pqadPH40pWa6Q7IhieOF687nDAsAwcAiN+zai2hue12a8zqAIMJoMQctqsEGXdmMk3TTSfqneHwUPK+FKk4aAYmRJYb2vcermtsX74Z+XOUkOtp3xHRYXrVcbYmPsF8rb+iBJafV3rm5taL/b6ukdQwu4uQ8XGxpJP+2NCZVL1M1KvrCosmcBpPJuR82aIHC8tkvqLrSYdy0EJIzyDcyUYUaM9McHIUxOYkDFwdY7Bv/tWx6E3LVI8CzCz2aivq670VlSUPm82m2kTRk7LC2r66EAgUNbREbi2obm1ri/QnwVugjP2bGPxsu0dlnUxLhgHhSxTrAhIQdCygQyhy+BgwkQWWdRh0cWMRiN57ZggkdfG4klVF+OTDPUSKdJ6rf34lqWmDvou1aBJoihUV7m4u8qxpby0dE++pen0gQWDmj6AsoQWb9dnm1raDdFoLIsSvBHZ9NTJ0vUn+42LkpXCCPGisKczDeW5mbSQ+nfKo4tkNuCw6rkoySMoQU2oke4I7fufXKhLghq5yNz2t8/bj+4RWfZ6haPMZqypqtjr8ZT/JbMEzeelyYAsZNDwMfTVnxaf77KWps41vo7uiKpm1wzvdlk9m1rtVwbiknOoaEjnrBniNcjBY6RjgsBL9Ky/xKyTORMM4DweiPFYTyQJ5oRDne7HowsevMVR/6pbF8zKzYuMel1tjburptq9saioiL7SPm6bEKjpq3DO7cdONF3X3Npe19tL+5yHLKYy4Zkm14r3/ZY1CgTdYNtQAzKzYEh6tEYVo6VjsoCYxSipobggxLlQ2PdJxoCiSFB6Pl3S8PInrE1ZK6PUIKl0O8SaaverDkfJjsn8gtCkQE3POxgMzmto8F19qrnNFI/Hs9ZBGweM1j80lX+qOWyYOyhUwwqE4W3FVPo1rJcgiGDChFNOMMYTi4q6d9zoOPSOiWVnMnZ7saHGU3GwzuPeNBU/DzIloBK49JV0r7djXWNr5yd8vo7BLlca+Nc6Sme80uG4IqTKtOM6q9E9BOLojfDkeAJ1guJeKg40fcFxfMt8Y1dWQavX62VPlTtQU1325+Li4uPjjvMxDpgyUDMowXbqlPfaU83emb29gawsIZwQpD+0VK3+MGBbpTJByuoljJJupWhBW8IhLx0nqLKgDqw2t77+udJj+zIJmDHGKsrLdLWeitddrtK3puor6RNW/0KfZlcgMMfX2nl1Q2ObJRqNZlHCkZDZ/myb5wpf1DBjKMxTqj96LyHdRyhQmxj4NF3f/psc9X91yaGsbXA2m0XvqXafmFFXRb+n0lvo/Yxn3JR7aubF6aeSvB0dn2ht7ljX4u2Mpxvf6TEvd1bMe93vujysSskO2AjxyuolpAHPfXtmId71GfuJl1dbvA2ZI/WyJFVXu8NVte4XSy0W2kF92uy0gppBCcUnG7yfa2jyzh5OCX2KrHvGN23Nx6GSCwEmppI8ASyjX5vdchwdCwFcWWLu2nZj6eHtBiG7y1ZeXmqo8VRuqywv+ytjrIANRZPD+4yAmp5iIBCY1dTSeU1DU5t1OCUc6C9xPt9Re2WXYvQkaSBZgWU0wtPfVBvlfl3ywMnry468PNPQk9UtovJyWm1Vc7mzeqPFwvJ0XScHZObRZxRULUsQfb6uSxpbfet9vu54IjG0gZJKiE1dtYu39VWuj1EHLEu8Rk5VJyT611qa//pZ+8kDmTdFXfiqCmfCU+d+yWGzJb8CeibtjIOavrnu7m5rTyB8TUtz27zunkDWfvtuxWB4pmP2pUcjJcu0TvdwTPgsY+/em8oOvVE6rAvvdJYaqquc79dUuV8+W7+vetZATaNESzleX9fnGprabeFwJIvv9oRcFZv8M67sVfQV6fG08HaN/cSW5WZf1q/9FhmNujqPu8PjcVF5mdWhP5NeStc666BqlCD4fL0XN3m9n2xr61QyKYE6YBv9s85/P1h+8RJTx3vXO+rfkzDUhRcEJlRXlrO6Gtcrdrv9vUI7SacT6HMC1IwswdzU1Hb1qaa2hd3+3ixKIHCHd5LKqLysrdpfU+XarH3p+HRiVfC5zylQhyghXNfa1nZtU7PXPhCOjuiwG/V6uba2vLeupvIFo9F41n4ndSyUz0lQ05TQ2dmzsrG17VMtLe0q7Umg8rKy0iV5Kpyvl5eXvTPV5WXBrphn4DkLagYlmJqb2zd0+ftmW62GBrvNstlutxfy1cepwmjc5/l/Ic7W0anbaxkAAAAASUVORK5CYII=';
     const Warn = 'image://data:image/png;base64,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';
      const chartDom = this.$refs.circuitTopologyPanel;
      this.myChart = echarts.init(chartDom);
      var option;

      option = {
  title: {
    text: ''
  },
  animationDurationUpdate: 1500,
  animationEasingUpdate: 'quinticInOut',
  series: [
    {
      type: 'graph',
      layout: 'none',
      symbolSize: 50,
      symbol: Normal,
      nodeScaleRatio: 0,
      // zoom:this.zoom,
      // roam: true,
      fixed: true,
      draggable: true,
      edgeSymbol: ['circle', 'arrow'],
      edgeSymbolSize: [4, 10],
      // edgeLabel: { 
      //   show: true, 
      //   textStyle: { fontSize: 12, fontWeight: '400',color: 'rgba(0,0,0,0.85)' }, 
      //   formatter: "{c}" 
      // },
      label: { 
        show: true,
        align: 'center',
        textStyle: { fontSize: 12, fontWeight: '400',color: 'rgba(0,0,0,0.85)'}, 
        formatter: params => {
           var newParamsName = ``;// 最终拼接成的字符串
                  var paramsNameNumber = params.name.length;// 实际标签的个数
                  var provideNumber = 15;// 每行能显示的字的个数
                  var rowNumber = Math.ceil(paramsNameNumber / provideNumber);// 换行的话，需要显示几行，向上取整
                   if (paramsNameNumber > provideNumber) {
                      /** 循环每一行,p表示行 */
                      for (var p = 0; p < rowNumber; p++) {
                          var tempStr = "";// 表示每一次截取的字符串
                          var start = p * provideNumber;// 开始截取的位置
                          var end = start + provideNumber;// 结束截取的位置
                          // 此处特殊处理最后一行的索引值
                          if (p == rowNumber - 1) {
                              // 最后一次不换行
                              tempStr = params.name.substring(start, paramsNameNumber);
                          } else {
                              // 每一次拼接字符串并换行
                              tempStr = params.name.substring(start, end) + "\n";
                          }
                          newParamsName += tempStr;// 最终拼成的字符串
                      }

                  } else {
                      // 将旧标签的值赋给新标签
                      newParamsName = params;
                  }
            return "\n\n\n\n\n\n\n" + newParamsName;
            
        },
    },
      data: data.nodeList,
      links: data.lineList,
      // lineStyle: {
      //   width: 2,
      //   curveness: 0,
      //   normal: {
      //           // color: 'rgba(231, 60, 60, 1)'
      //           color: '#178dfa',
      //           opacity: 1
      //       }
      // }
    }
  ]
};

option && this.myChart.setOption(option);
this.myChart.on("mouseup",(params)=>{
  let aaa = this.saveData.filter(val => {
    return val.name == params.data.name;
  });
      aaa[0].left = params.event.offsetX;
      aaa[0].top = params.event.offsetY;
      aaa[0].x = params.event.offsetX;
      aaa[0].y = params.event.offsetY;
      // 当拓扑发生改变 将状态改为false
      aaa[0].success = false;
      
      this.$emit('saveData',this.saveData);
      });
    },
    
  },



};
</script>
<style>
#efContainer {
  overflow: hidden;
}
.circuitTopologyPanel {
   background: url("../../assets/tuopu/tuopuBg.png") center center
        no-repeat;
      background-size: cover;
}
/*滚动条样式*/
#efContainer::-webkit-scrollbar {
  width: 4px;
  height: 4px;
}

#efContainer::-webkit-scrollbar-thumb {
  border-radius: 10px;
  -webkit-box-shadow: inset 0 0 5px #c1c1c1;
  background: rgba(0, 0, 0, 0.2);
}

#efContainer::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 5px #c1c1c1;
  border-radius: 0;
  background: rgba(0, 0, 0, 0.1);
}

#last img {
  display: none;
}
</style>
