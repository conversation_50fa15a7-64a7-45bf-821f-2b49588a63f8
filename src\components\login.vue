<template>
  <div
    id="full-page"
    class="full-page page"
  >
    <div class="logo_img_box logo_img">
      <!-- <div class="block_1 flex-row justify-between"> -->
      <!-- <div class="block_2 flex-col"></div> -->
      <!-- <span class="text_1">统一门户</span> -->
      <span class="text_1">O域数据整合平台</span>
      <!-- </div> -->
    </div>
    <div class="col-sm-12 login-box">
      <!-- <div class="login-container block_3 flex-row"> -->
      <div class="login-header flex-col">
        <div class="login_header_title text_2" />
        <el-form
          ref="ruleForm"
          :model="ruleForm"
          :rules="rules"
          class="flex-row justify-between"
          @submit.native.prevent="loginMethod('ruleForm')"
        >
          <el-form-item
            prop="userName"
            :error="nameError"
          >
            <el-input
              id="account"
              v-model.trim="ruleForm.userName"
              type="text"
              autocomplete="off"
              placeholder="请输入账户"
            >
              <i
                slot="prefix"
                class="glyicon iconfont icon-dengluyonghuming"
              />
            </el-input>
            <span class="erroraccount">请输入账号</span>
          </el-form-item>
          <el-form-item
            prop="passWord"
            :error="psdError"
          >
            <el-input
              id="passWord"
              v-model.trim="ruleForm.passWord"
              type="password"
              autocomplete="off"
              placeholder="请输入密码"
            >
              <i
                slot="prefix"
                class="glyicon iconfont icon-tianchongxing-"
              />
            </el-input>
          </el-form-item>
          <el-form-item
            prop="checkCode"
            :error="nameError" 
          >
            <el-input
              v-model.trim="ruleForm.code"
              type="text"
              maxlength="5" 	
              autocomplete="off"
              placeholder="请输入验证码"
              @keyup.enter.native="loginMethod('ruleForm')"
            >
              <i
                slot="prefix"
                class="glyicon iconfont icon-yanzhengma"
              />
            </el-input>
            <span class="erroraccount">请输入验证码</span>
            <img
              :src="codeUrl"
              alt=""
              class="animate fadeIn login_code"
              @click="getCode()"
            >
          </el-form-item>
          <!-- <el-form-item>
                        <div class="checkbox">
                            <label class="label_one">
                                <input
                                    type="checkbox"
                                    class="pwd_again"
                                />记住密码
                            </label>
                            <label style="float: right">
                                <a href="" class="linka">忘记密码?</a>
                            </label>
                        </div>
                    </el-form-item> -->

          <el-form-item class="login-btn">
            <el-button
              id="login-btn"
              class="btn btn-primary btn-login1"
              type="primary"
              @keyup.enter.native="loginMethod('ruleForm')"
              @click="loginMethod('ruleForm')"
            >
              登录
            </el-button>
          </el-form-item>
        </el-form>
      </div>
      <!-- </div> -->
    </div>
    <!-- <div class="form-group login-footer" style="margin-top: 8%">
      <span>{{ footer_title }}</span>
    </div> -->

    <div
      v-show="downloadPageLoading"
      class="inner-loading-box"
    >
      <div class="el-loading-mask">
        <div class="el-loading-spinner">
          <svg
            viewBox="25 25 50 50"
            class="circular"
          >
            <circle
              cx="50"
              cy="50"
              r="20"
              fill="none"
              class="path"
            />
          </svg>
        </div>
      </div>
    </div>
    <el-dialog
      append-to-body
      title="首次登录修改密码"
      :visible.sync="dialogVisible"
      destroy-on-close
      width="500px"
      class="form-dialog dialogStyle"
    >
      <div class="content-attention">
        <el-alert
          title="注意：请务必妥善保管修改后的密码"
          type="warning"
          show-icon
        >
          <!-- 注意：绑定设备后，该号卡只能在下列终端中使用 -->
        </el-alert>
      </div>
      <el-form
        ref="changeForm"
        inline
        :model="changeForm"
        :rules="rulesChangeFirst"
        style="padding-top: 50px;"
        label-width="130px"
      >
        <el-form-item
          label="原密码"
          prop="passWord"
          :error="passWord"
        >
          <el-input
            v-model="changeForm.passWord"
            type="passWord"
            size="small"
            placeholder="请输入"
            clearable
          />
        </el-form-item>
        <el-form-item
          label="新密码"
          prop="newpassword"
          :error="newpassword"
          style="margin-bottom: 33px"
        >
          <el-input
            v-model="changeForm.newpassword"
            type="password"
            size="small"
            placeholder="请输入"
            clearable
          />
        </el-form-item>
        <el-form-item
          label="确认密码"
          prop="confirmPwd"
          :error="psdError"
        >
          <el-input
            v-model="changeForm.confirmPwd"
            type="password"
            size="small"
            placeholder="请输入"
            clearable
          />
        </el-form-item>

        <el-form-item class="change_button">
          <div
            class="form-button"
            style="text-align: right"
          >
            <el-button
              type="primary"
              @click="changeFirstPassword(changeForm)"
            >
              确定
            </el-button>
            <el-button @click="chanel">
              取消
            </el-button>
          </div>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import { loginCode, login, updateCurrentLoginUserPassword } from "@/apis/api-login";
import { remove, treeArrSort } from "@/utils/util";
import { isComplexPassword } from "@/utils/validate";
import { encrypt, decrypt } from "@/components/chartsoption/jsencrypt.js";
// // import store from "@/store/store.js";
// const uuid = require("uuid");
// const diuu = uuid.v4().replace(/-/g, "");
export default {
  name: "",
  components: {},
  props: {},
  data() {
    var validateName = (rule, value, callback) => {
      if (value === "") {
        callback(new Error("请输入账号"));
      } else {
        this.nameError = "";
        callback();
      }
    };
    var validatePass = (rule, value, callback) => {
      if (value === "") {
        callback(new Error("请输入密码"));
      } else {
        this.psdError = "";
        callback();
      }
    };
    // var validateCode = (rule, value, callback) => {
    //   if (value === "") {
    //     // this.$message({ duration: 1000, message: '请输入验证码', type: 'error' })
    //     callback(new Error("请输入验证码"));
    //   } 
    //   else {
    //     this.codeError = "";
    //     callback();
    //   }
    // };

    // 验证码自定义验证规则
    const validateCode = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请输入验证码'));
      } else if (value !== this.identifyCode) {
        console.log('validateVerifycode:', value);
        callback(new Error('验证码不正确!'));
      } else {
        callback();
      }
    };





    var validateoldPwd = (rule, value, callback) => {
      if (value === "") {
        callback(new Error("请输入旧密码"));
      } else if (value !== this.ruleForm.passWord) {
        callback(new Error('原始密码输入不正确!'));
      }  else {
        this.nameError = "";
        callback();
      }
    };
    var validatenewPwd = (rule, value, callback) => {
      if (value === "") {
        callback(new Error("请输入新密码"));
      } else if (value == this.changeForm.passWord) {
        callback(new Error("新密码与旧密码相同"));
      } else if (!isComplexPassword(value)) {
        callback(
          new Error(
            "密码必须大于八位，且包含大写字母、小写字母、数字、特殊字符中的任意三类"
          )
        );
      } else {
        this.psdError = "";
        callback();
      }
    };
    var validateconfirmPwd = (rule, value, callback) => {
      if (value === "") {
        // this.$message({ duration: 1000, message: '请输入验证码', type: 'error' })
        callback(new Error("请输入确认密码"));
      } else if (value != this.changeForm.newpassword) {
        callback(new Error("密码输入不一致"));
      } else {
        this.codeError = "";
        callback();
      }
    };
    return {
      sys_logo: "../assets/slices/sys_logo.png",
      ruleForm: {
        userName: "",
        passWord: "",
        code: "",
      },
      subForm: {
        userName: "",
        passWord: "",
        code: "",
        rememberMe: false,
        key: "",
      },
      nameError: "",
      psdError: "",
      passWord: "",
      newpassword: "",
      dialogVisible: false,
      changeForm: { passWord: "", newpassword: "", confirmPwd: "" },
      rules: {
        userName: [{ validator: validateName, trigger: "blur" }],
        passWord: [{ validator: validatePass, trigger: "blur" }],
        checkCode: [{ validator: validateCode, trigger: "blur" }],
      },
      rulesChangeFirst: {
        passWord: [{ validator: validateoldPwd, trigger: "blur" }],
        newpassword: [{ validator: validatenewPwd, trigger: "blur" }],
        confirmPwd: [{ validator: validateconfirmPwd, trigger: "blur" }],
      },
      login_title: "",
      footer_title: "",
      codeUrl: "",
      codeKey: "",
      codeShow: true,
      downloadPageLoading: false,
      loading_btn: false,
      obmsLoginTitle: "",
    };
  },
  watch: {},
  created() {},
  mounted() {
    this.getCode();
  },
  activited() {},
  update() {},
  beforeRouteUpdate() {},
  methods: {
    // 获取验证码
    async getCode() {
      let result;
      result = await loginCode({});
      this.codeUrl = result.data.data.base64Image;
      this.codeKey = result.data.data.key;
    },
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.$router.push("/home");
          // sessionStorage.setItem("account", this.ruleForm.userName);
          // sessionStorage.setItem("passWord", this.ruleForm.passWord);
        } else {
          return false;
        }
      });
    },
    chanel() {
      this.dialogVisible = false;
      // this.visible = false;
      this.changeForm = {};
      // this.getCode();
    },
    // login
    loginMethod(formName) {
      this.$refs["ruleForm"].validate(async (valid) => {
        let result, ruleForm, subForm;
        if (valid) {
          // 有验证码登录
          if (this.codeShow) {
            // this.loading_btn = true;
            ruleForm = {
              ...this.ruleForm,
              code: this.ruleForm.checkCode,
            };
            subForm = {
              // ...this.ruleForm,
              userName: encrypt(this.ruleForm.userName),
              passWord: encrypt(this.ruleForm.passWord),
              code: this.ruleForm.code,
              rememberMe: false,
              key: this.codeKey,
            };

            result = await login(subForm);
            // this.downloadPageLoading = false;
            // if (result.data.data.ifFirstLogin) {
            //   this.dialogVisible = true;
            //   // this.$router.replace("/DataSys/pass");
            // }
          } else {
            // 无验证码登录
            ruleForm = { ...this.ruleForm, diuu };
            // const res = await pswEncode({
            //   value: ruleForm.passWord,
            // });
            this.downloadPageLoading = false;
          }

          if (result.data.code == 40001) {
            // this.loading_btn = false;
            // this.$notify({
            //     title: "登录结果",
            //     type: "error",
            //     message: "登录失败",
            //     duration: 2500,
            // });
            this.downloadPageLoading = false;
            this.getCode();
          }
          if (result.data.code == 200) {
            // this.loading_btn = false;
            // result.data.data.ifFirstLogin = true;
            // console.log(this.ruleForm.passWord);





            if (result.data.data.ifFirstLogin) {
              this.dialogVisible = true;
            } else {
              sessionStorage.setItem("tokenName", result.data.data.tokenName);
              sessionStorage.setItem("userName", result.data.data.loginId);
              sessionStorage.setItem("tokenValue", result.data.data.tokenValue);
              this.getCode();
              // this.$router.replace("/home");
              this.$router.replace("/DataSys");
              this.downloadPageLoading = false;
              this.$message({
                title: "登录结果",
                type: "success",
                message: result.data.msg,
                duration: 2500,
              });
            }
            
            
            
            // store.commit("set_xaiot_token", result.data.sid);
            // store.commit(
            //     "set_xaiot_curruser",
            //     result.data.data.curruser
            // ); //curruser:"name,userName,utype,baseurl"
            // sessionStorage.setItem("tokenName", result.data.data.tokenName);
            // sessionStorage.setItem("userName", result.data.data.loginId);
            // sessionStorage.setItem("tokenValue", result.data.data.tokenValue);
            // this.getCode();
            // this.$router.replace("/home");
            // this.$router.replace("/DataSys");
            // this.getPlatforminfo();
            // sessionStorage.setItem("usid", result.data.data.usid);
            // sessionStorage.setItem("account", this.ruleForm.userName);

            // await this.getAccess({
            //   ...ruleForm,
            //   sessionId: result.data.data.usid,
            // });

            // this.downloadPageLoading = false;
            // let baseinfo = JSON.parse(sessionStorage.getItem("baseinfo"));
            // if (baseinfo.utype == "1") {
            //   this.getPlatforminfo();
            //   document.title = this.login_title + "后台配置系统";
            //   this.$router.replace("/ombsHome");
            // } else {
            //   let permlist = JSON.parse(sessionStorage.getItem("permlist"));
            //   this.getPlatforminfo();
            //   document.title = this.login_title;
            //   this.$router.replace("/" + permlist[0].linkurl);
            // }
          } else {
            this.downloadPageLoading = false;
            this.$message({
              title: "登录结果",
              type: "error",
              message: result.data.msg,
              duration: 2500,
            });
            this.getCode();
            
            if (result.data.code == 500) {
              if (result.data.msg == "请输入准确的验证码") {
                this.ruleForm.code = "";
              } else{
                // this.ruleForm.userName = "";
                this.ruleForm.passWord = "";
                this.ruleForm.code = "";
              }
            }
            
          }
        }
      });
    },
    changeFirstPassword(changeForm) {
      let that = this;
      this.$refs["changeForm"].validate(async (valid) => {
        let result, changeFormObj;
        let subFormFirst = {};
        if (valid) {
          changeFormObj = changeForm;
          // const resPassword = await pswEncode({
          //   value: changeForm.passWord,
          // });
          // const resNewPassword = await pswEncode({
          //   value: changeForm.newpassword,
          // });

          subFormFirst.oldPassWord = changeForm.passWord;
          subFormFirst.passWord = changeForm.newpassword;
          // subFormFirst.userName = this.ruleForm.userName;
          result = await updateCurrentLoginUserPassword(subFormFirst);
          if (result.data.code == 200) {
            // this.loading_btn = false;
            this.downloadPageLoading = false;
            this.$notify({
              title: "修改密码",
              type: "success",
              message: "修改密码成功，请重新登录！",
              duration: 2500,
            });
            this.dialogVisible = false;
            this.changeForm = {};
            this.ruleForm.passWord = "";
            // store.commit("set_xaiot_token", result.data.sid);
            // store.commit(
            //     "set_xaiot_curruser",
            //     result.data.data.curruser
            // ); //curruser:"name,userName,utype,baseurl"
            // sessionStorage.setItem("account", "EPadmin");
            // sessionStorage.setItem("passWord", "EPadmin");
            this.getCode();
          } else {
            this.downloadPageLoading = false;
            // this.$notify({
            //     title: "登录结果",
            //     type: "error",
            //     message: result.error_msg,
            //     duration: 2500,
            // });
            // this.loading_btn = false;
            this.getCode();
          }
        }
      });
      // });
    },
    // 向storage中储存用户信息
    async saveUserInfo(result, requestData) {
      sessionStorage.setItem(
        "permprops",
        JSON.stringify(result.data.data.permprops || {})
      );
      let permlist = this.sortMenus(result.data.data.permlist || []);
      const common =
        permlist.filter((item) => item.name === "公共配置权限")[0] || {};
      // common.action = (common.action ?? "") + homedepment.join(",");
      // 公共权限
      sessionStorage.setItem("common", common.action || "");
      remove(permlist, common);
      // 菜单
      sessionStorage.setItem("permlist", JSON.stringify(permlist));
      // 基本信息
      sessionStorage.setItem(
        "baseinfo",
        JSON.stringify(result.data.data.baseinfo)
      );
      sessionStorage.setItem("alias", result.data.data.baseinfo.alias);
      // subtoken 统一认证平台id
      await this.unientry(result.data.data.baseinfo.userid + "");
      // 子服务分发的usid
      // sessionStorage.setItem("usid", requestData.sid);、
      // this.saveUserPassword(requestData);
      // checkSid();
    },
    // 对菜单进行排序
    sortMenus(menus) {
      const sort = (a, b) => a.subid - b.subid;
      return treeArrSort(menus, "children", sort);
    },
    // 储存用户ID为subtoken
    unientry(name) {
      return unientry({ name }).then((res) => {
        sessionStorage.setItem("subtoken", res.data);
      });
    },

    // 获取权限
    async getAccess(requestData) {
      const result = await auth(requestData);
      if (result.data.success) {
        try {
          this.saveUserInfo(result, requestData);
        } catch (error) {
          this.$notify({
            title: "提示",
            message: "服务器错误",
            type: "error",
          });
        }
      } else {
        this.$notify({
          title: "授权",
          type: "error",
          message: result.error_msg,
          duration: 2500,
        });
      }
    },
  },
};
</script>


<!-- <style src="./common.css" /> -->
<style src="../style/index.css" />
<style  scoped>
.el-input__prefix {
  height: 25px;
  line-height: 25px;
  margin: 6px 0;
  margin-top: 8px;
  width: 52px;
  /* line-height: 60px; */
  /* padding-right: 18px; */
  border-right: 1px solid #55FFFC;
}
</style>

<style lang="scss" >
#full-page .el-form-item__content {
  margin-left: 0 !important;

  .block_1 {
    width: 1131px;
    height: 113px;
    margin: 37px 0 0 37px;
  }

  .block_3 {
    width: 620px;
    height: 660px;
    margin: 0px 0 73px 294px;
  }

  .text_6 {
    width: 64px;
    height: 45px;
    overflow-wrap: break-word;
    color: rgba(255, 255, 255, 1);
    font-size: 32px;
    text-align: left;
    white-space: nowrap;
    line-height: 45px;
    margin: 14px 0 0 218px;
  }
  .border_prefix {
    border-left: 1px solid #000;
  }
}
.label_one {
  color: #fff;
  float: left;

  cursor: pointer;
}
.pwd_again {
  margin-right: 10px !important;
}
.login-title {
  font-size: 3rem;
  /* margin-top: 148px; */
  letter-spacing: 0.44141rem;
  margin-left: 30rem;
  position: relative;
  text-align: left;
  font-weight: 900;
  color: #fff;
  min-width: 700px;
}

.layui-layer-content {
  color: #000000 !important;
}
#full-page .el-input__inner {
  // border-radius: 21px !important;
  width: 100%;
  height: 36px;
  background: rgba(0, 153, 255, 0.08);
  color: #55c4ff;
  border-radius: 4px;
  border: 1px solid rgba(0, 176, 255, 0.5);
}

#full-page .el-input__inner::placeholder {
  color: #55c4ff;
  
}
/* 谷歌 */
#full-page .el-input__inner::-webkit-input-placeholder {
  color: #55c4ff;
  
}
/* 火狐 */
#full-page .el-input__inner:-moz-placeholder {
  color: #55c4ff;
  
}
/*ie*/
#full-page .el-input__inner:-ms-input-placeholder {
  color: #55c4ff;
  
}
.full-page {
  background: url("../assets/home_slices/loginBg.png") center center no-repeat;
  background-size: cover;
  min-height: 100%;
}
body,
html {
  height: 100%;
  color: white;
}
.container {
  height: 100%;
}
.login-box {
  // padding-top: 12rem;
  // margin-top: -103px;
  justify-content: flex-start;
  /* align-items: center; */
  display: flex;
  align-content: center;
  flex-wrap: wrap;
  flex-direction: column;
  margin-top: 60px;
}
.login-container {
  // margin-top: 7%;
  /* margin-left: 26%;
    margin-right: 36%; */
  // display: flex;
  // flex-direction: column;
  // flex-wrap: wrap;
  // justify-content: center;
  // align-items: stretch;
  // align-content: space-around;
}

.logo_img_box {
  width: 100%;
  height: 80px;
  text-align: center;
  background: url("../assets/home_slices/header.png") center center no-repeat;
  background-size: 100%;
}

.btn-login1 {
  display: block;
  height: 42px;
  width: 100%;
  // border-radius: 35px;
  background: linear-gradient(180deg, #38B6FF 0%, #165FD2 100%);
  border-radius: 8px;
  border: none;
  font-size: 14px;
  letter-spacing: 1px;
}

#full-page .btn-primary:active:focus,
#full-page .el-button--primary:focus,
#full-page .el-button--primary:hover {
  background-color: none;
  box-shadow: none;
}

.el-button--primary:focus,
.el-button--primary:hover {
  background: linear-gradient(180deg, #38B6FF 0%, #165FD2 100%);
}

.btn-login {
  width: 100%;
  border-radius: 12px;
  margin-top: 25px;
  // background-color: #0059fd;
}
.btn-login:hover {
  width: 100%;
  border-radius: 12px;
  // background-color: #0059fd;
  // border-color: #0059fd;
}
#full-page .btn-primary.active.focus,
#full-page .btn-primary.active:focus,
#full-page .btn-primary.active:hover,
#full-page .btn-primary:active.focus,
#full-page .btn-primary:active:focus,
#full-page .btn-primary:active:hover {
  // background-color: #0059fd;
  // border-color: #0059fd;
}

.form-group {
  margin-bottom: 23px;
}
#full-page form {
  width: 100%;
  margin-top: 56px;
  font-size: 1rem;
}
#full-page .form-control {
  border-radius: 21px;
  font-size: 1rem;
  height: 40px;
}
.login-header {
  // border-bottom: 1px solid #ddd;
  padding-left: 100px;
  padding-right: 100px;
  // width: 640px;
  // height: 580px;
  background: url("../assets/home_slices/formBg.png") center center no-repeat;
  background-size: 100%;
  width: 600px;
  height: 530px;
}
.glyphicon-user:before {
  content: "\e008";
}
.glyphicon {
  position: relative;
  top: 1px;
  display: inline-block;
  font-family: "Glyphicons Halflings";
  -webkit-font-smoothing: antialiased;
  font-style: normal;
  font-weight: normal;
  line-height: 1;
}

#full-page input[type="text"] {
  padding-left: 37px;
}
#full-page input[type="password"] {
  padding-left: 37px;
}
#full-page input[type="checkbox"],
#full-page input[type="radio"] {
  margin: 2px 0 0;
}
.glyicon {
  position: relative;
  /* top: 7px; */
  // left: 11px;
  color: #55c4ff;
  font-size: 20px;
}
.glyicon:after {
  border-right: 1px solid #fff;
}
.linka {
  color: #ffffff;
  text-decoration: none;
}
.linka:hover {
  color: #ffffff;
  text-decoration: none;
}
.errorpassword {
  color: red;
  padding-left: 37px;
  padding-top: 4px;
  position: absolute;
  display: none;
}
.erroraccount {
  color: red;
  padding-left: 37px;
  padding-top: 4px;
  position: absolute;
  display: none;
}
.login-footer {
  justify-content: center;
  align-items: center;
  display: flex;
  color: #fff;
  position: relative;
}

.el-input--prefix .el-input__inner {
  padding-left: 65px !important;
}

.login_code {
  width: 98px;
  height: auto;
  /* margin: 12px; */
  padding: 0;
  position: absolute;
  top: 50%;
  right: 1px;
  transform: translate(0, -50%);
  display: inline-block;
  border-radius: 4px;
  cursor: pointer;
}

#full-page .el-form-item__error {
  padding-top: 6px;
  left: 35px;
  .el-form-item {
    margin-bottom: 22px;
  }
}

.login_header_title {
  width: 132px;
  height: 74px;
  background: url("../assets/home_slices/formlogo.png") center center no-repeat;
  background-size: 132px 74px;
}

.full-page .el-dialog {
  background: rgba(0, 0, 0, 0.5) !important;
  box-shadow: 0 0 2.3836rem 0 #5c6e87;
  
}

.form-dialog .el-dialog {
  // background: #080d31 !important;
  // width: 600px;
  // height: 530px;
  // box-shadow: 0 0 2.3836rem 0 #5c6e87;
  // background: url("../assets/home_slices/formBg.png") center center no-repeat;
  // background-size: 100%;

    // width: 740px;
    height: 480px;
    background: #080d31;
    background: url("../assets/home_slices/formBg.png") center center no-repeat;
    background-size: 100%;
}

.form-dialog .el-dialog__title {
  color: #fff;
}

.form-dialog .el-form-item__label {
  color: #fff;
}

.form-dialog .el-input {
  width: 260px;
}

.login-btn {
  margin-top: 58px;
}
.form-dialog .el-form--inline .change_button {
  width: 100%;
}

.form-dialog .el-form--inline .change_button .el-form-item__content {
  width: 100%;
}

.form-dialog .el-icon-close {
  display: none;
}


</style>
