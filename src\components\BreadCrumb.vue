<template>
  <div class="box_breadcrumb">
    <el-breadcrumb class="breadcrumb-container navbar"
                   separator-class="el-icon-arrow-right">
      <el-breadcrumb-item v-for="item,index in levelList"
                          :key="index"
                          :to="item.path">
        {{ item.meta.title }}
      </el-breadcrumb-item>
    </el-breadcrumb>
  </div>
</template>

<script>
export default {
  name: "BreadCrumb",
  components: {},
  props: {},
  data () {
    return { levelList: [] };
  },
  computed: {},
  watch: {
    $route: {
      handler() {
        this.getBreadcrumb();
      },
      immediate: false
    }
  },
  created () {
    // 使用 nextTick 确保路由系统已经初始化
    this.$nextTick(() => {
      this.getBreadcrumb();
    });
  },
  mounted () { },
  activited () { },
  update () { },
  beforeRouteUpdate () { },
  methods: {
    getBreadcrumb () {
      // 检查 $route 是否存在，避免在路由未初始化时出错
      if (!this.$route || !this.$route.matched) {
        console.warn('Route not available yet');
        return;
      }

      let matched = this.$route.matched.filter((item) => item.name);

      const first = matched[1];
      // console.log(first.name.trim().toLocaleLowerCase());
      if (
        (first &&
          first.name.trim().toLocaleLowerCase() ==
          "号卡自服务".toLocaleLowerCase()) ||
        first.name.trim().toLocaleLowerCase() ==
        "电子围栏".toLocaleLowerCase() ||
        first.name.trim().toLocaleLowerCase() ==
        "业务变更订单".toLocaleLowerCase()
      ) {
        // console.log(1);
        matched.splice(1, 0, {
          path: "/cardManage/cardSelfService",
          name: "物联卡",
          meta: { title: "物联卡" },
        });
        this.levelList = matched.slice(1, 3);
      } else if (
        (first &&
          first.name.trim().toLocaleLowerCase() ==
          "人联卡".toLocaleLowerCase())
      ) {
        matched.splice(1, 0, {
          path: "/netsResources/rlcard",
          name: "人联卡",
          meta: { title: "人联卡" },
        });
        this.levelList = matched.slice(1, 2);
      } else if (
        (first &&
          first.name.trim().toLocaleLowerCase() ==
          "专网概述".toLocaleLowerCase()) ||
        first.name.trim().toLocaleLowerCase() ==
        "资源信息".toLocaleLowerCase() ||
        first.name.trim().toLocaleLowerCase() ==
        "UPF管理".toLocaleLowerCase()
      ) {
        matched.splice(1, 0, {
          path: "/netsResources/priNetsOverview",
          name: "网络资源",
          meta: { title: "网络资源" },
        });
        this.levelList = matched.slice(1, 3);
      } else if (
        (first &&
          first.name.trim().toLocaleLowerCase() ==
          "服务监控".toLocaleLowerCase()) ||
        first.name.trim().toLocaleLowerCase() ==
        "域名管理".toLocaleLowerCase()
      ) {
        matched.splice(1, 0, {
          path: "/DNS/serviceMonitor",
          name: "DNS",
          meta: { title: "DNS" },
        });
        this.levelList = matched.slice(1, 3);
      } else if (
        (first &&
          first.name.trim().toLocaleLowerCase() ==
          "网元告警".toLocaleLowerCase()) ||
        first.name.trim().toLocaleLowerCase() ==
        "电子围栏告警".toLocaleLowerCase() ||
        first.name.trim().toLocaleLowerCase() ==
        "号卡诊断".toLocaleLowerCase() ||
        first.name.trim().toLocaleLowerCase() ==
        "连通性诊断".toLocaleLowerCase()
      ) {
        matched.splice(1, 0, {
          path: "/priNetsMonit/priNetsMonitor",
          name: "专网监控",
          meta: { title: "专网监控" },
        });
        this.levelList = matched.slice(1, 3);
      } else if (
        (first &&
          first.name.trim().toLocaleLowerCase() ==
          "业务感知分析".toLocaleLowerCase()) ||
        first.name.trim().toLocaleLowerCase() ==
        "物联卡分析".toLocaleLowerCase() ||
        first.name.trim().toLocaleLowerCase() ==
        "人联卡分析".toLocaleLowerCase() ||
        first.name.trim().toLocaleLowerCase() ==
        "专网运维报表".toLocaleLowerCase() ||
        first.name.trim().toLocaleLowerCase() ==
        "专网月度报告".toLocaleLowerCase()
      ) {
        matched.splice(1, 0, {
          path: "/analyReport/analyAwares",
          name: "分析报表",
          meta: { title: "分析报表" },
        });
        this.levelList = matched.slice(1, 3);
      } else if (
        (first &&
          first.name.trim().toLocaleLowerCase() ==
          "企业信息".toLocaleLowerCase()) ||
        first.name.trim().toLocaleLowerCase() ==
        "成员管理".toLocaleLowerCase() ||
        first.name.trim().toLocaleLowerCase() ==
        "角色管理".toLocaleLowerCase() ||
        first.name.trim().toLocaleLowerCase() ==
        "系统日志".toLocaleLowerCase()
      ) {
        matched.splice(1, 0, {
          path: "/analyReport/analyAwares",
          name: "系统管理",
          meta: { title: "系统管理" },
        });
        this.levelList = matched.slice(1, 3);
      } else if (
        (first &&
          first.name.trim().toLocaleLowerCase() ==
          "新增电子围栏".toLocaleLowerCase())

      ) {
        matched.splice(1, 0, {
          path: "/cardManage/cardSelfService",
          name: "物联卡",
          meta: { title: "物联卡" },
        });
        matched.splice(2, 0, {
          path: "/cardManage/elFence",
          name: "电子围栏",
          meta: { title: "电子围栏" },
        });
        this.levelList = matched.slice(1, 4);
      } else if (
        (first &&
          first.name.trim().toLocaleLowerCase() ==
          "修改电子围栏".toLocaleLowerCase())

      ) {
        matched.splice(1, 0, {
          path: "/cardManage/cardSelfService",
          name: "物联卡",
          meta: { title: "物联卡" },
        });
        matched.splice(2, 0, {
          path: "/cardManage/elFence",
          name: "电子围栏",
          meta: { title: "电子围栏" },
        });
        this.levelList = matched.slice(1, 4);
      }
      else if (
        (first &&
          first.name.trim().toLocaleLowerCase() ==
          "系统公告".toLocaleLowerCase())

      ) {

        this.levelList = matched.slice(1, 3);
      } else {
        this.levelList = matched.slice(1, 3);
      }
      // console.log(matched);
      // console.log(matched, matched.slice(1, 3));
      // this.levelList.push(matched[0]);
      // this.levelList.push(matched[2]);

    },
  },
  filter: {},
};
</script>

<style lang="scss" scoped>
/deep/ .el-breadcrumb__inner.is-link {
  font-weight: bold !important;
  font-size: 14px;
  font-family: Microsoft YaHei;
  color: #00cbfe;
}
$font_size_16: 14px;
/deep/.el-breadcrumb {
  font-size: $font_size_16;
  font-weight: normal;
}
.box_breadcrumb {
  // padding: -6px;
  margin-left: 40px;
  margin-right: 40px;
  // margin-top: 10px;
  // padding-bottom: 0px;
  margin-bottom: 15px;
}

.navbar {
  width: 100%;
  // float: left;
  // background-color: #fff;
  flex-wrap: wrap;
  align-items: center;
  justify-content: flex-start;
  height: 40px;
  line-height: 40px;
  // padding-left: 10px;
}
</style>
