<!DOCTYPE html>
<html lang="">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width,initial-scale=1.0">
    <meta base="/portal/">
    <link rel="icon" href="">
    <!-- <title><%= htmlWebpackPlugin.options.title %>网络智慧运营平台</title> -->
    <title id="title"> </title>

    <!-- <script src="http://*************:8081/baidumap/mapv2/build/mapv.min.js"></script>
    <script src="http://*************:8081/baidumap/bmapgl/mapvgl/examples/static/common.js"></script> -->
    <script src="http://code.bdstatic.com/npm/mapvgl@1.0.0-beta.141/dist/mapvgl.min.js"></script>
    <script type="text/javascript" src="https://code.bdstatic.com/npm/mapvgl@1.0.0-beta.123/dist/mapvgl.threelayers.min.js"></script>

    <!-- <script type="text/javascript">window.BMAP_AUTHENTIC_KEY='baidu-72a3df37d9ce440f81baf91e2fc6458b'</script>
    <script type="text/javascript" src="http://*************:8081/baidumap/bmapgl/"></script>
    <script src="http://*************:8081/baidumap/bmapgl/api/booter.js"></script> -->


    <!-- 百度地图API -->
    <script src="https://gis.10010.com:8219/dugis-baidu/baidumap/bmapgl/api/booter.js"></script>
    <script src="https://gis.10010.com:8219/dugis-baidu/bmapgl/?qt=getscript&libraries=visualization"></script>
    <!-- <script type="text/javascript">window.AUTHENTIC_AK='baidu-72a3df37d9ce440f81baf91e2fc6458b'</script> -->
    <!-- <script src="//api.map.baidu.com/api?v=1.0&type=webgl&ak=AS40l3XfrcwQSg6CVgG1v6XSKN2UuiP3"></script> -->
    <!-- <script type="text/javascript" src="https://api.map.baidu.com/api?type=webgl&v=1.0&ak=062cK8Hh67h1TxbkEkA25MZo6znPkBLg"></script> -->
    <link href="//mapopen.cdn.bcebos.com/github/BMapGLLib/DrawingManager/src/DrawingManager.min.css" rel="stylesheet">
    <script type="text/javascript" src="//mapopen.cdn.bcebos.com/github/BMapGLLib/DrawingManager/src/DrawingManager.min.js"></script>

    <!-- <script type="text/javascript" src="http://mapv.baidu.com/build/mapv.min.js"></script>
    <script src="https://code.bdstatic.com/npm/mapvgl@1.0.0-beta.159/dist/mapvgl.min.js"></script>
    <script type="text/javascript" src="https://code.bdstatic.com/npm/mapvgl@1.0.0-beta.123/dist/mapvgl.threelayers.min.js"></script>
    <script src="https://mapv.baidu.com/gl/examples/static/common.js"></script>
    <script type="text/javascript" src="https://bj.bcebos.com/v1/mapopen/github/BMapGLLib/TrackAnimation/src/TrackAnimation.min.js"></script> -->
    <!-- 高德地图 -->
    <!-- <script type="text/javascript"
        src="https://webapi.amap.com/maps?v=1.4.15&key=a4e810ff03fdea40608fa6a8192b4c93&plugin=AMap.MarkerClusterer,AMap.Marker,AMap.PolyEditor,AMap.InfoWindow,AMap.Autocomplete,AMap.PlaceSearch,AMap.MouseTool,Amap.GeometryUtil,AMap.CircleEditor"></script> -->

        <!--加载鼠标绘制工具-->
<script>
window.onload = function() {
  if (typeof BMapGL !== 'undefined') {
    var script = document.createElement('script');
    script.src = 'http://api.map.baidu.com/library/DrawingManager/1.4/src/DrawingManager_min.js';
    document.head.appendChild(script);
  }
};
</script>
<link rel="stylesheet" href="http://api.map.baidu.com/library/DrawingManager/1.4/src/DrawingManager_min.css" />
        <style>
            @media screen and (min-width: 320px) {
	body {font-size: 16px}
}
@media screen and (min-width: 481px) and (max-width:640px) {
	body {font-size: 18px}
}
@media screen and (min-width: 641px) {
	body {font-size: 20px}
}
        </style>
</head>

<body class="">
    <noscript>
        <strong>We're sorry but <%= htmlWebpackPlugin.options.title %> doesn't work properly without JavaScript enabled.
                Please enable it to continue.</strong>
    </noscript>
    <div id="app"></div>
    <!-- built files will be auto injected -->
</body>
<script>
    var faviconurl = "<%= BASE_URL %>favicon.png";//这里可以是动态的获取的favicon的地址

    var link = document.querySelector("link[rel*='icon']") || document.createElement('link');

    link.type = 'image/x-icon';

    link.rel = 'shortcut icon';

    link.href = faviconurl;

    document.getElementsByTagName('head')[0].appendChild(link);
    document.title = "通信重保场景"
</script>

</html>

