import {mapState} from 'vuex';
import state from '../store/state';
export default {
    inserted(el,bindling){
        // console.log(el,bindling);
    	//bindling.value为指令的绑定值
        let perVal = bindling.value[0];
        if(bindling.value){
        //假设某用户对某模块只有添加和删除的权限
    	//这个权限信息(即pers)应该是不同用户登录时从后台拿到的对应的信息
            // let pers = state.pers;
            // console.log(pers);
            let pers = sessionStorage.getItem("pers");
            //hasPer为true为有权限
    		//hasPer为false为无权限
            // let pers1 = pers.split(',');
            // let pers1 = [];
            
            // console.log(pers.split(','));
            let hasPer ;
            // console.log(perVal);
            if(pers !='*'){
                hasPer = pers.some(item=>{
                return item == perVal;
              });
            }else{
                hasPer = true;
            }
             //没有权限就先隐藏此元素吧
             if(!hasPer){
                el.style.display="none";
            }
        }
    },
};